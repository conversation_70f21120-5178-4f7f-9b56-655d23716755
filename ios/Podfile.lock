PODS:
  - advance_pdf_viewer (1.0.5):
    - Flutter
  - "app_settings (3.0.0+1)":
    - Flutter
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - connectivity (0.0.1):
    - Flutter
    - Reachability
  - Firebase/Auth (10.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.10.0)
  - Firebase/CoreOnly (10.10.0):
    - FirebaseCore (= 10.10.0)
  - Firebase/Database (10.10.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 10.10.0)
  - Firebase/DynamicLinks (10.10.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 10.10.0)
  - Firebase/Messaging (10.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.10.0)
  - firebase_auth (4.6.3):
    - Firebase/Auth (= 10.10.0)
    - firebase_core
    - Flutter
  - firebase_core (2.14.0):
    - Firebase/CoreOnly (= 10.10.0)
    - Flutter
  - firebase_database (10.2.3):
    - Firebase/Database (= 10.10.0)
    - firebase_core
    - Flutter
  - firebase_dynamic_links (5.3.3):
    - Firebase/DynamicLinks (= 10.10.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.6.4):
    - Firebase/Messaging (= 10.10.0)
    - firebase_core
    - Flutter
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.10.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseCore (10.10.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseDatabase (10.10.0):
    - FirebaseCore (~> 10.0)
    - leveldb-library (~> 1.22)
  - FirebaseDynamicLinks (10.10.0):
    - FirebaseCore (~> 10.0)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.10.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 8.0)
  - google_sign_in_ios (0.0.1):
    - Flutter
    - GoogleSignIn (~> 6.2)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base
  - GoogleSignIn (6.2.4):
    - AppAuth (~> 1.5)
    - GTMAppAuth (~> 1.3)
    - GTMSessionFetcher/Core (< 3.0, >= 1.1)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (1.3.1):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 3.0, >= 1.5)
  - GTMSessionFetcher/Core (2.3.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - open_file_safe (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - Reachability (3.7.6)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sms_autofill (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - Stripe (23.7.1):
    - StripeApplePay (= 23.7.1)
    - StripeCore (= 23.7.1)
    - StripePayments (= 23.7.1)
    - StripePaymentsUI (= 23.7.1)
    - StripeUICore (= 23.7.1)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.7.0)
    - StripeApplePay (~> 23.7.0)
    - StripeFinancialConnections (~> 23.7.0)
    - StripePayments (~> 23.7.0)
    - StripePaymentSheet (~> 23.7.0)
    - StripePaymentsUI (~> 23.7.0)
  - StripeApplePay (23.7.1):
    - StripeCore (= 23.7.1)
  - StripeCore (23.7.1)
  - StripeFinancialConnections (23.7.1):
    - StripeCore (= 23.7.1)
    - StripeUICore (= 23.7.1)
  - StripePayments (23.7.1):
    - StripeCore (= 23.7.1)
    - StripePayments/Stripe3DS2 (= 23.7.1)
  - StripePayments/Stripe3DS2 (23.7.1):
    - StripeCore (= 23.7.1)
  - StripePaymentSheet (23.7.1):
    - StripeApplePay (= 23.7.1)
    - StripeCore (= 23.7.1)
    - StripePayments (= 23.7.1)
    - StripePaymentsUI (= 23.7.1)
  - StripePaymentsUI (23.7.1):
    - StripeCore (= 23.7.1)
    - StripePayments (= 23.7.1)
    - StripeUICore (= 23.7.1)
  - StripeUICore (23.7.1):
    - StripeCore (= 23.7.1)
  - system_settings (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - advance_pdf_viewer (from `.symlinks/plugins/advance_pdf_viewer/ios`)
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - connectivity (from `.symlinks/plugins/connectivity/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_database (from `.symlinks/plugins/firebase_database/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - open_file_safe (from `.symlinks/plugins/open_file_safe/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sms_autofill (from `.symlinks/plugins/sms_autofill/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - system_settings (from `.symlinks/plugins/system_settings/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseDatabase
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleDataTransport
    - GoogleMaps
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - Reachability
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore

EXTERNAL SOURCES:
  advance_pdf_viewer:
    :path: ".symlinks/plugins/advance_pdf_viewer/ios"
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  connectivity:
    :path: ".symlinks/plugins/connectivity/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_database:
    :path: ".symlinks/plugins/firebase_database/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  open_file_safe:
    :path: ".symlinks/plugins/open_file_safe/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sms_autofill:
    :path: ".symlinks/plugins/sms_autofill/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  system_settings:
    :path: ".symlinks/plugins/system_settings/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  advance_pdf_viewer: bceb86fe141a1dd54f12259c9ded0206ad893d22
  app_settings: dec48968acca6472cfb9f487d8a366afffd3d1a1
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  connectivity: e7d9fd79099f39025737062855c00248b7afcdf0
  Firebase: facd334e557a979bd03a0b58d90fd56b52b8aba0
  firebase_auth: 34c36e0f659d6b151808fefc725c1aa6c1952b77
  firebase_core: 6a807fac416909ef93a294ce0ec3056ac1db18fd
  firebase_database: 2bdec51cf93cfbba36867d575c9672dcfc954d9b
  firebase_dynamic_links: abd17fc17fb39856fdc16924bf7d4a10e6ae8cfd
  firebase_messaging: 9b1c23072a19c92376a3b92e0c0f9e21f491292e
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: 5ddbe23ebc4e647469261f5c59cd12a04f37c8e6
  FirebaseCore: d027ff503d37edb78db98429b11f580a24a7df2a
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseDatabase: c3f61dfb9dbd2e8a2804454283d493e9372f1465
  FirebaseDynamicLinks: 3f61f496236d30fa749377159fb7b3d82ecb3c49
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 8a3b9a8b98ce72a42d22e69865cf662e38d2d6f5
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_local_notifications: ef18f0537538fcd18e9106b3ddc91cc10b4e579a
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  google_maps_flutter_ios: e523ee12cfa731f83c9200bd54f331bdbfd76a73
  google_sign_in_ios: 39b65fd3f954159b06bdf448966bff7b0900e166
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac
  GoogleSignIn: 5651ce3a61e56ca864160e79b484cd9ed3f49b7a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: 0ff230db599948a9ad7470ca667337803b3fc4dd
  GTMSessionFetcher: 3a63d75eecd6aa32c2fc79f578064e1214dfdec2
  image_picker_ios: afb77645f1e1060a27edb6793996ff9b42256909
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  open_file_safe: 9cb8d1b3667d20942f870416987cc3f8719bdf31
  package_info_plus: ae4a63389b62b5be8544b36bfc5b069617938813
  path_provider_foundation: 0f28622a447f952060798e710afcae6800d1dfa0
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Reachability: fd0ecd23705e2599e4cceeb943222ae02296cbc6
  share_plus: 4dfe6f47fc3248e89b3eea0be59a95d0433ebe1a
  shared_preferences_foundation: 4e65c567e7877037d328829a522222c938bf308c
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sms_autofill: b36b2147535657fea83d7f3898d7831de70bd8e4
  sqflite: 5b24d06a453c198c13b305ceea4b4286cc07cfe4
  Stripe: fbe3d1d61cba70ac7a920c6097222ba9525352c4
  stripe_ios: ecfa445f735073200ba9e81a17bc5bd117171dba
  StripeApplePay: 06e3899c9db102567804b77c41cec2861b5e2c39
  StripeCore: 9b331e5f9d4067ef7296718c680a73ad6f2d1066
  StripeFinancialConnections: 7d8116c4dcd3b034f13a181b4d96e6991b594cfc
  StripePayments: 46fbd561312cb300c93dd855b9c896285c8eb1fb
  StripePaymentSheet: 016fd1c6053efff1f348fc6f44babecacda9d0af
  StripePaymentsUI: cb06c01d3c9efde91ebce218f9e4e94e902dd676
  StripeUICore: a9db3f2a82c932d5ca58dd8fc4ac0a6b72f1ccee
  system_settings: 0afa5deab4239f6b9325f54aba2d3b0932bcf23d
  url_launcher_ios: b7f13d9491e07f5230bf3e056ab68678f896980a
  webview_flutter_wkwebview: 6e6160e04b1e85872253adc5322afe416d9cdddc

PODFILE CHECKSUM: 01dcb7aa6d40d2e415b8cf90cd6a768de2ab11ea

COCOAPODS: 1.16.2
