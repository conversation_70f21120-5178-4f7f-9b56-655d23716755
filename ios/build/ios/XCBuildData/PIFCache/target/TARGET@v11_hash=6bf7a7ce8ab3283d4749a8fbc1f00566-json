{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e414352ff61b24d89ea9c7ffba619ba9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c392ec74493e7b4bf565e4729c169668", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c84a48c53651010f18f2de79938d62e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986797de5fe0206b734cd80c51a7908944", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c84a48c53651010f18f2de79938d62e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c24c1ab1c5df7ecfb2dfd897e5c49abb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b84903c898070e5bcaa8179ea538fe4", "guid": "bfdfe7dc352907fc980b868725387e98fa0250c490e126195ec05a8ee1e92cb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882778a1840d6d030da5aee1c1ed6b95a", "guid": "bfdfe7dc352907fc980b868725387e989772e6ecbe9a58185dbbcebd90d2f8b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826309c26f854401cc7403d933a265ce5", "guid": "bfdfe7dc352907fc980b868725387e98f2cf0ebb636a5e631ca25bf78833cc30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2dc84c6bb0191e6d9aa504e729c5d20", "guid": "bfdfe7dc352907fc980b868725387e985576deb9ac2ea1e02faac91a2b953fc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fdd6a0e0f5dc9e0fd3b4d35e2e59534", "guid": "bfdfe7dc352907fc980b868725387e98f7388c03273739c457a6a9eb95fae5a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0b2bc8f3cb4bbed4cbec7ccc5212e1", "guid": "bfdfe7dc352907fc980b868725387e98eb72c5fd1dc7ef00be59f214db8fd46a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831223fbe54ba9e54354911f3420c7dbb", "guid": "bfdfe7dc352907fc980b868725387e988978ea9ddbce4a669ba9467569fd8c98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d976eccf2c36d5204e290865007370", "guid": "bfdfe7dc352907fc980b868725387e983cf29c7e16771f2036f94e1ec5b969cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb93514d09cbd1bc1a410c494c64bee", "guid": "bfdfe7dc352907fc980b868725387e987d56ed1515f93f21de636c4617db2e0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98174951b936e7076a4d5cc3f6038a33fc", "guid": "bfdfe7dc352907fc980b868725387e984688bb8abeff9b4ab404955ff9025abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa9da664d140a56d8c314e8f068b298", "guid": "bfdfe7dc352907fc980b868725387e98e901ee0e1c5cae2f4fbb53b5b2335f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d43052432c4727fa02f9bc38d9441c3", "guid": "bfdfe7dc352907fc980b868725387e98c7bbd922f10e7c60acd367017251ff5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98478341b64cfaa309fbbbf6c45798f2cf", "guid": "bfdfe7dc352907fc980b868725387e9885b7224dcad539b6b24d7d7ec26ba539", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb859bb1282b9fe9a1e93590fb3c4cdd", "guid": "bfdfe7dc352907fc980b868725387e983b422399dc3d03c30a8f8e24cb579b20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc518b7970d71cd0dd065204e6a3d585", "guid": "bfdfe7dc352907fc980b868725387e982c6309653e9900186079a8bc7a668e3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c0a4cb93e1d525c83ba522734824eb1", "guid": "bfdfe7dc352907fc980b868725387e98a71098676d061868d9ce015dd73c016d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87c5e169b5e26ca2042358c78979eb3", "guid": "bfdfe7dc352907fc980b868725387e9861236c882b03455b99548adce0d3df0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7ad810a511d676ad3f6d288fb54ac86", "guid": "bfdfe7dc352907fc980b868725387e986f7cb76b1f2b4879861137256cca92f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a155f9c9b5119a2e52964d74540e921", "guid": "bfdfe7dc352907fc980b868725387e989be4fa17c3f3b37fe2bea9d8ba8236c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d8d104ac37f60759fa3000886d1e21", "guid": "bfdfe7dc352907fc980b868725387e988aaed55b3531f2242e6ee4a00faed5c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e9467a67a5f6d144140f5ebb283fd1f", "guid": "bfdfe7dc352907fc980b868725387e987ca09f38f1b04447c6065b41097f35c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984173c50d169dd8ed5dedea050214d6f4", "guid": "bfdfe7dc352907fc980b868725387e98d60af0ccbc6a67791393b8659de94206", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e64cead7d2b7af76e14a68327a2a0a15", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986164868f777e6120189750d716b8dc8e", "guid": "bfdfe7dc352907fc980b868725387e986571c4fe7d436ceaff0df6e34ea1da74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7a2aee96b63b05ca203f89a4a452ce", "guid": "bfdfe7dc352907fc980b868725387e9892fc3893998ccdf61246ef53fabec054"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0add6157955df35eac847cc05afffb6", "guid": "bfdfe7dc352907fc980b868725387e985c5027e17f3b7d7721cf97af50e6929a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a3cbf98450aff014f2358e9efc356d", "guid": "bfdfe7dc352907fc980b868725387e98b34baf03cdea64b5566831d4373fe66b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7d58734e2ffed11fcabd6664f51757f", "guid": "bfdfe7dc352907fc980b868725387e98bf38f9c6ea4004afc211e78f2cc21ee0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f72b5911d3b59bb2c6c415f56030cb8", "guid": "bfdfe7dc352907fc980b868725387e984913e4dcf4262dc4938990231cafffe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876e7093b3213dbbea54988c5ee0f5984", "guid": "bfdfe7dc352907fc980b868725387e983b2dea2062efe7829436968b19762de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98749aa10fd117cf191b8ddcb68dfbc921", "guid": "bfdfe7dc352907fc980b868725387e983636c497b0682f7da333eea26308e406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985838401e2a155ce7972edc600a740ef0", "guid": "bfdfe7dc352907fc980b868725387e9822ff2e39dfbfeb11474dfebd76a6d4d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f648d3ccd3ac78e43de224025c79fe8e", "guid": "bfdfe7dc352907fc980b868725387e984457d00dc688eb1073431fe1d93af4f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3622d6426945bffba0364af0a9d2633", "guid": "bfdfe7dc352907fc980b868725387e98ca2831e3b171fee23645083341442e8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836c10d48a9e608232e3047acee2bf497", "guid": "bfdfe7dc352907fc980b868725387e980f15366e82555814750cdb2b33f2f350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e058f049e62d72cf7a01adee8f0bf2c2", "guid": "bfdfe7dc352907fc980b868725387e9819e944fb9df0dd865f7f989d24e47b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac086b5ddf41e07b810ffdf48164aec9", "guid": "bfdfe7dc352907fc980b868725387e98140fb2079989b7ead36cfe8a2e8d8a46"}], "guid": "bfdfe7dc352907fc980b868725387e983cfb2767d9f7a51af99ea949bdd79ebe", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98a2be04e8fde4ebdf4f759ef0ce0d4644"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a61d761249950157b7def56ae6875b", "guid": "bfdfe7dc352907fc980b868725387e98118a94e0549acd84427687d009e24fdf"}], "guid": "bfdfe7dc352907fc980b868725387e98895d165274c4fa3138ad62f0cb148ac6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9874208d0f968cc81c397b5474620802c0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}