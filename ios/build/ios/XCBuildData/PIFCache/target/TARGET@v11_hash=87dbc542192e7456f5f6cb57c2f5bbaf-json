{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2f23505b32bc57b07d04ddecee27d56", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5cbb7e07f913e19eda86177557a3560", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987315b04f39140b12301e3d4851059572", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9876dc472946d603bb961c2adc55ff40fe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987315b04f39140b12301e3d4851059572", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98654f502e2a596f43fe06e10cfabc5221", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987d7d216e355a2b70728f0b16208a57ee", "guid": "bfdfe7dc352907fc980b868725387e98b964fa04ea4edb466f296b037e7db6ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868a991f1a8e55b5670385f40f085652b", "guid": "bfdfe7dc352907fc980b868725387e98ba5e0905033cfa0bb8dceeba7c93022a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c228ab01b530ade771694cfea865fa1c", "guid": "bfdfe7dc352907fc980b868725387e987a2a3fa86e17edd648b8072abf823262", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ecf181a7eb52cf6e04027b0f264716", "guid": "bfdfe7dc352907fc980b868725387e98dbd0797d36f3b3d2e4a9e71f67ce794b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84f131f0557e0c161da3fe420415003", "guid": "bfdfe7dc352907fc980b868725387e98718045276910ef854c38e62f4a7fcf2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f601fe1b1bbcbe0ee1da3183cf7cf47c", "guid": "bfdfe7dc352907fc980b868725387e980953057436aa66ccddc0ea9400c65877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef9e872fe20a4fbdb5dcd62f1c56aab", "guid": "bfdfe7dc352907fc980b868725387e985c9263f585f6ac507cd05ccae4e03607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984093ef55cddd006ef0661debcd10f610", "guid": "bfdfe7dc352907fc980b868725387e984968c4762ebf7089c8df085c4e9e3fe7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e9fe8f8a055ca34adbf2751818728d", "guid": "bfdfe7dc352907fc980b868725387e987af3d39c7ba3e092ab2135e6471e03cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e8c77f99166c15bd1f0c6df81567b9", "guid": "bfdfe7dc352907fc980b868725387e980089ad5a81db574158c6e75477e2968b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ec66f59195a8948b4f706fc9b9df23", "guid": "bfdfe7dc352907fc980b868725387e98234a36f65617f079e2a06551bb432e35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e14f3556d431631d4376cdd9f3bc7b6b", "guid": "bfdfe7dc352907fc980b868725387e981f8f480860bf8be7739cec38beddbae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a488427c40993a740b9ff820b89ef1c", "guid": "bfdfe7dc352907fc980b868725387e983234276053c70fd1525bdae2bfcd2ae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6af1b06d977eb5c637bd890b8a75e55", "guid": "bfdfe7dc352907fc980b868725387e9895a89ec8868e8aa7e7d9eb7c4b95042e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdaa62f69d63a08e9a32355d02732edc", "guid": "bfdfe7dc352907fc980b868725387e98792a70cc9002d0f31c05f121f85a7a71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6b5f50323288b21636872ef43e25aae", "guid": "bfdfe7dc352907fc980b868725387e984d9c385f85813adaa6c7e62c619159a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4acc40b5a4692e79c330798263a3f32", "guid": "bfdfe7dc352907fc980b868725387e986c802fe72a2f7957c447bfd14030a2b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f457413435619d6bd6f44197e9aaffb", "guid": "bfdfe7dc352907fc980b868725387e98f4210af05e8cfc6c990c4e5a1d9dc2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986596da5611395d8ff760eeb3245f84f6", "guid": "bfdfe7dc352907fc980b868725387e986faf89184e9ec1f9868be23f06349f80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899880d5bc5a9c739b0ae429567e61c3d", "guid": "bfdfe7dc352907fc980b868725387e98df2c7a50b8e25a186cec4986b26ff07c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b35b661f55fe2d460d2c7f7e3d802d37", "guid": "bfdfe7dc352907fc980b868725387e982e46ef5c5f8abc8a998aa48c0366c3b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c1ba0937c4f2ea03126db2abbdcb69e", "guid": "bfdfe7dc352907fc980b868725387e98a775a3d3df429b1bd1298cdb8e8dec84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aaffd6c22589e57fa6da5fab0c4bc3f", "guid": "bfdfe7dc352907fc980b868725387e985ed2b467c3a9ad1acd8ff4fa1bbc79b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acee1284eade76c338bc95cfde075df9", "guid": "bfdfe7dc352907fc980b868725387e98ba7ea32b80377a72123c15e49db15792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab54db07e81425756362225d346d4749", "guid": "bfdfe7dc352907fc980b868725387e98cda3e912e922527a7eb3dbf7ce0a7fc0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8bd56c698a6ee25e038c6155e30e570", "guid": "bfdfe7dc352907fc980b868725387e980edebfcda281477b7dd107b87fbb48de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c413717fe9ee639eb3a9cab1e51ec9aa", "guid": "bfdfe7dc352907fc980b868725387e98aba0d6331f4f654b8a8088e5d635f111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c84e4e13ec47327c893f5f10a0084611", "guid": "bfdfe7dc352907fc980b868725387e98f847fb360461a6b4ba00d9e88412ed9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9689950870a684a8fe6201e040f8d28", "guid": "bfdfe7dc352907fc980b868725387e989c84bc2db444ecd7848f2c92b9a9a9be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51e9d70baa2827e1511d7894ff7b837", "guid": "bfdfe7dc352907fc980b868725387e98034850a421a246084a80101bf55f18fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f83912b9803475ac5a486529d5f789c", "guid": "bfdfe7dc352907fc980b868725387e9838ed991e747d784311e5e3a93e83113e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc92276e699aab90ab28bd25eade5ff", "guid": "bfdfe7dc352907fc980b868725387e98ad02f1cfb7beefd9d2b5c7099595f029"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef5d1f085f8a6c4573537f5475666fe", "guid": "bfdfe7dc352907fc980b868725387e98a31cdedb4b63a1855b0d547cde1b92c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858a665b41a29acba1983572887852e82", "guid": "bfdfe7dc352907fc980b868725387e98a95e936d451f810c2a60337812e17456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c15ba719bf91740e8304d31c3b87e97d", "guid": "bfdfe7dc352907fc980b868725387e987971786093c474950e316472184798d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98167e832342c511d4650db287cdfda154", "guid": "bfdfe7dc352907fc980b868725387e988c51664404506da0ee7b56eb9fbf30ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ff67cc223cd44f769932f87513c398", "guid": "bfdfe7dc352907fc980b868725387e98c10e7c615c6750ab56881c190776e80c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af958f9baa7f13a5ac132ffc2b9871f", "guid": "bfdfe7dc352907fc980b868725387e98d9989c2c68605235844eb54f805a833f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661923cded55bbf04a9896893c0bd11e", "guid": "bfdfe7dc352907fc980b868725387e987a23f67082c8b4683efb469a5ff8b7eb"}], "guid": "bfdfe7dc352907fc980b868725387e987ef4c1e57d9021548dd8da880e9aa0e9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d1d0a3a67f7051a3a368ebc71b7b6c70", "guid": "bfdfe7dc352907fc980b868725387e98a4673cb272231f1190ab73b0a95e1a25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0190a3de83fce3168c4da1932e05402", "guid": "bfdfe7dc352907fc980b868725387e980575502302cf8ee909b54367822bfccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98270f5414e4979169e96929e6a8c5ce65", "guid": "bfdfe7dc352907fc980b868725387e987b26320deddfed46fc11626cb9ee0d8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f82c60e0584a8a02ca510ed4c63d42", "guid": "bfdfe7dc352907fc980b868725387e98efe950dcbdf6f6740af154bd78532ce8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf1209eca8c12f24ea5ff79107c6397", "guid": "bfdfe7dc352907fc980b868725387e9816256f7988a881cd702e1911f7e0f94d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fa36e041d7da99f55a2dab77bf0778e", "guid": "bfdfe7dc352907fc980b868725387e9858091141d807ce9f49224348f6dc965d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981210a64550261c9de13f5efad63269d0", "guid": "bfdfe7dc352907fc980b868725387e98018795c6889d11982965d2557e293355"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe55e76aa229b0a556fe835ec63f212b", "guid": "bfdfe7dc352907fc980b868725387e98f5464c3fc4cbb5991997a5c50c45ff10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844217b84e1b78bfeff8e31fc76db601d", "guid": "bfdfe7dc352907fc980b868725387e9830e74e5913dcd022a2cdfbee5a370c78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98365ef19c68176f89f438b9082814bf83", "guid": "bfdfe7dc352907fc980b868725387e9896b2eb4150233577948e4dad04f6285a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98559880a1e75a05367be587152b897709", "guid": "bfdfe7dc352907fc980b868725387e982d941608fd7200a536513817cf85922f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad03fce028f5ac984d4fe07815c1da7", "guid": "bfdfe7dc352907fc980b868725387e9803d1d8077ce847ee6d5a79e3a193cb10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98856e4855d0490ee82b86dcfee7eb9f90", "guid": "bfdfe7dc352907fc980b868725387e98412de8a8b6d16e5b5aba194d093b39d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98077eb38900ef84dae1d7b0b63eb41c34", "guid": "bfdfe7dc352907fc980b868725387e98df6514a11428cc5adfc2c95091d17d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a292f65051f3f0fd9208478e1ca94e", "guid": "bfdfe7dc352907fc980b868725387e98621f9b3a5cadb09e41d79cae6ba04994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d96bfe88c93464fdd892e71a9c4675", "guid": "bfdfe7dc352907fc980b868725387e98f95ca45fa24adcb97ba458447667a15c"}], "guid": "bfdfe7dc352907fc980b868725387e985b2f3c93926ab369ee93359559ba659b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e983235ee8773637ad3fbbc845aa243f6bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f3385c3fc924ea7214cb0fc98a1a262", "guid": "bfdfe7dc352907fc980b868725387e98abd6cad811a570febdd24360fa87945b"}], "guid": "bfdfe7dc352907fc980b868725387e9872b121ceb5e89f033bd518678c933d4f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987cafbeeb9edfda8157c47711de69e8ac", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e98b623f22c0d3d037d0450c736133d3c3e", "name": "FirebaseDynamicLinks", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98834460d3052fa1f4117c6696cee22bc3", "name": "FirebaseDynamicLinks.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}