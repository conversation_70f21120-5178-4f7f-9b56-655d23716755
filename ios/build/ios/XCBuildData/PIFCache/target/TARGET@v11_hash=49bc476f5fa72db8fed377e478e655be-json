{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9887caf20f6fc3e337b5796fb054c8b3f2", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b44da7a35eb8b26a79fdc765bc663b27", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b80f7e6ff8d26b6eb28a5662ab4774ae", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d66f7e42b49fabb2050b4dc67c313861", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b80f7e6ff8d26b6eb28a5662ab4774ae", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5c129b7854a20b39565f0a45fe4530b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980ef6918551d6c84a9e1ceddabc82708c", "guid": "bfdfe7dc352907fc980b868725387e988c4e8c8a4958fdba9c1bf00320ca3cd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f0999f39acf6584a34d820dfa78810f", "guid": "bfdfe7dc352907fc980b868725387e9886db73884481fd468ee48f5554d4518c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fbbcfa310745fc17ce34dcbe0bc2bf7", "guid": "bfdfe7dc352907fc980b868725387e987c1e4eee005d4589ecc5afcbdea4ee13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c60c3c4101bfbbdce382a4562098503", "guid": "bfdfe7dc352907fc980b868725387e9804a5b0174f97b8300b74c49b9a4c8483", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cbb2484f5707d7078796fe5dc6a4a66", "guid": "bfdfe7dc352907fc980b868725387e98197acac69bf5ee409cc807ca63e96314", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19765d3473417a95cebab43bcef19e6", "guid": "bfdfe7dc352907fc980b868725387e98b0093639dfcb8e045c787a763b236ea2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc05cac2dd6e5c61a2c35edee4dd8055", "guid": "bfdfe7dc352907fc980b868725387e9885c903b42c2094f3d1310d26792b626f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f614f9e758fe5b22281bffd343fed4bc", "guid": "bfdfe7dc352907fc980b868725387e988dcc3283e43ea7d8058ad095b6078a7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf1b9ccd57b5d9f6fc3b142cb90b56b0", "guid": "bfdfe7dc352907fc980b868725387e9864989a1f013e7e76a854a1dafd1b3a92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e01aabf2dc8ff163a8d7962e212073e", "guid": "bfdfe7dc352907fc980b868725387e986ac658bbf996ada14cccddd9a9665950", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f4451cdc365dce2689c47b0dda627e4", "guid": "bfdfe7dc352907fc980b868725387e98c87ccf927389744ce531c8fea16f0415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cdeafef9138f79dbe2666e24efb1ff7", "guid": "bfdfe7dc352907fc980b868725387e986fff5aa57549513859cf0b23a29d0436", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953362a915e6de871e9eaafe4891b148", "guid": "bfdfe7dc352907fc980b868725387e98373b0dfbaef710487a3ba4d58f2245d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984201e4311023ad46b830308b0a9252a6", "guid": "bfdfe7dc352907fc980b868725387e9884ef74f03d2096aac845af5a7c81d8dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d789e5a643149a06f4397d72bbdaf109", "guid": "bfdfe7dc352907fc980b868725387e9800cecab8518f86e0a1af8c341ff56bdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c0ce10155afcb721187d77f3bfc5d4", "guid": "bfdfe7dc352907fc980b868725387e98bf627475b4d9accb68dc4b4f34194029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd56399bec57a67dc11498f4d12af185", "guid": "bfdfe7dc352907fc980b868725387e98d37884cd0762db075c9ff1b1166262c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843fe0d85ace2d71ed16dfec82ebe6eb6", "guid": "bfdfe7dc352907fc980b868725387e9881b4ccd046fc150cd687c7a02b8de69b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9811af2ef61d70014f1ec98968e0f4d3f1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98856829e9009d91abb44a5161c9b8c717", "guid": "bfdfe7dc352907fc980b868725387e983df20b705c8af97e5e56ee9ebe58ad99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6b39001ef1bd6a136bd942286bc1bd", "guid": "bfdfe7dc352907fc980b868725387e980dcde2a46c6be5a5ab0fbe5b4936de0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c116e31601a408341501028e3df0176a", "guid": "bfdfe7dc352907fc980b868725387e98e51baf0db9005d973d5bcf0b3960ee1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805a1aa43894d2da8439b28c213c9456e", "guid": "bfdfe7dc352907fc980b868725387e98d405ffcd0890a6820ca24fa9cf789cdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2cd800f747fa0009a6414976578e00c", "guid": "bfdfe7dc352907fc980b868725387e9847e44e913c1c6bbafad6f26c8336e0aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98640c2166ce2571b38defa2b03a48ee52", "guid": "bfdfe7dc352907fc980b868725387e9882cf890d3960e3fc3ae7a455f7bbe41b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805369916ac40dcb4edfaaa3287882830", "guid": "bfdfe7dc352907fc980b868725387e98cfb2cb12c22253cb2aa369cd9e81633c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef3e23cf09b0597f1e45ab2d02965988", "guid": "bfdfe7dc352907fc980b868725387e98b890ebffd5b19945b6bb6b378d0713d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cf013ada22b127865b43126ee53c111", "guid": "bfdfe7dc352907fc980b868725387e988433f47faf06a3c8b965a4f198a6b4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985121ea7803c30049d5fc7690d1ad0dff", "guid": "bfdfe7dc352907fc980b868725387e98b83bc61f07e23694ce1d6fa646cf0e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987684984d2c111c60704062770024d3a0", "guid": "bfdfe7dc352907fc980b868725387e989b02ac18057535966649e96d0d22a7bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0455f3ccbd3cf5939fc644de90b5bf0", "guid": "bfdfe7dc352907fc980b868725387e98b1ce62dd750b927a29500e2b08f6b20d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b42e3658892d4aa3b50e6a6b370e377f", "guid": "bfdfe7dc352907fc980b868725387e9848d85649d6f59fb70bb06f2fb5f404f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980939ad8ccd64ecda9cb8f44b8a8a5f14", "guid": "bfdfe7dc352907fc980b868725387e98689c3b271f6e7ae77b1d5086551e34cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fc7f3f0e1369adb034b7b46545cfb7c", "guid": "bfdfe7dc352907fc980b868725387e9869e0a97741f9a01c9c0e363cc312b904"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d470bccbbec24e41b7a14c1d4a0f25e", "guid": "bfdfe7dc352907fc980b868725387e9896ccff15ff222aec35d851a96823419f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2f7d088b8cce28735e84bed22fe1b23", "guid": "bfdfe7dc352907fc980b868725387e98baf02d0ce0dcfc50dd6278ad305426d2"}], "guid": "bfdfe7dc352907fc980b868725387e98f058f43412b1cfa2f72676b316aebe74", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e9813ae4d80d7e6dcf3efabadd70f0523ac"}], "guid": "bfdfe7dc352907fc980b868725387e98689e3ceb22785def0e8e3aa4a3f0c5ff", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9829c79da60965f21f0c2875b130dbb9bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}