{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868985ed7fac9bfe9d371070dc34c8c8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9853049b86ca06d9ee72fabaddfd877a2e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876864a8d2ed4320ade90c7ec2b0aec74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d125cec4835c8372755eed75b116f3ea", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876864a8d2ed4320ade90c7ec2b0aec74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988bfb903bbd87113418d642965a075c7a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98237e051fbf1ef0ab3b680141f56f2988", "guid": "bfdfe7dc352907fc980b868725387e98243892d1797e0cd412a1dee79bdb1856"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b63c0cdbfa3fde7028ec29667bef5ff4", "guid": "bfdfe7dc352907fc980b868725387e98767fa33a9623c9a44acb1378605ff6ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbff24adfcf67c5a222e5f196657d0d0", "guid": "bfdfe7dc352907fc980b868725387e9826cf1c60c8085cf485898a975592287c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a4e80ceecf0456099d498a39215b0d", "guid": "bfdfe7dc352907fc980b868725387e98da772b695fd78dfb9f0dabfa3a6f6975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2492cae2030dd1d837d8050e077b25", "guid": "bfdfe7dc352907fc980b868725387e985ff373217e65cf28b01dfb7e33f7a19c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d2d471d0d1fa536f28ad122eeac250d", "guid": "bfdfe7dc352907fc980b868725387e989c2c88e34674e0997b42c49339c2d1ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f736c6fe24a04f346955dcbc26843100", "guid": "bfdfe7dc352907fc980b868725387e9875ee8312ffa3b93b28a1d876a18b03ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980449d368c6e0c63edfd3b506b9c8bc32", "guid": "bfdfe7dc352907fc980b868725387e982088d027cdacf01b73d7bad02a32aa0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe05c6aab6716a9f4953e8f31221b843", "guid": "bfdfe7dc352907fc980b868725387e98c91d790bbb0c3fe99a27312d0c9fde64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ee50a5f06d5370e977534660bd7bfe4", "guid": "bfdfe7dc352907fc980b868725387e9887f61d55292a61361932b92785ae054e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e54fc7ff1b86101f5fcfdcae498eb39", "guid": "bfdfe7dc352907fc980b868725387e98b5d0c8a145a8077403580782c95651e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e5db41ae62400af37cbf9d70fc4dac", "guid": "bfdfe7dc352907fc980b868725387e985f1469e50aaf39fcdc23b12ce6b455f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab687c5e240b0c754070d7d0c79fcc3c", "guid": "bfdfe7dc352907fc980b868725387e9851ed84466b9c7f20b94c37001b7cefc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833db6874521949ef1e7881a1f682bfa4", "guid": "bfdfe7dc352907fc980b868725387e9860fdc478bf1b4c4b1c1475677441035f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845aae97f5a936ef7d3423b799355f233", "guid": "bfdfe7dc352907fc980b868725387e98ddf91adcc67687a6bad7a1d341dff4e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2d54a700117729f0c3612948bd3497", "guid": "bfdfe7dc352907fc980b868725387e9810938db985c335c0a55d85447b0fc769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1a310a038ce74b02286e9e3f08d3abe", "guid": "bfdfe7dc352907fc980b868725387e983ce2563d33fafbab76346348e0e03795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c074d871e8e62ae1f6ea8a0634a10c1d", "guid": "bfdfe7dc352907fc980b868725387e982fe26a37bcdd95270bc81ca73d6ead7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dffdef8922e86d2d04d330a4175adf3", "guid": "bfdfe7dc352907fc980b868725387e98fd65e05e1cbaf6d4f1b990a2dad11027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984efd508502e6a7025e8a408e563f158a", "guid": "bfdfe7dc352907fc980b868725387e98535a17da3990d3d08fa0da373262aed9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bff121011c78980594cb0eb7fab7ae1", "guid": "bfdfe7dc352907fc980b868725387e987c970216a8d11574ab218699276b2ba8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812244d6b1d40fd1e698445d1814a0b29", "guid": "bfdfe7dc352907fc980b868725387e981d2d56d362c0108b73fa06c846103ef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98492767e164e8e4e3594445eab121c27b", "guid": "bfdfe7dc352907fc980b868725387e98d85c4c915bcab30b79e9e1e2b4ffe1e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da08657d5065c7189d66ce280bfaf191", "guid": "bfdfe7dc352907fc980b868725387e98ccf3a438edc80df63bcaffc2a95b094d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38c5a009ed0e15e49d3332f8b2736d2", "guid": "bfdfe7dc352907fc980b868725387e9827d257c48ae92443462eb6c7951073f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0c0fb1259ff6b74813f05c478d500e", "guid": "bfdfe7dc352907fc980b868725387e981056c46e23fd3c9cfe4c3f3ac740d3ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898352d7685f7cbd9f6db69a7a7213b75", "guid": "bfdfe7dc352907fc980b868725387e98819ce7d03103b51b200a34a5ad0a9c32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe15d8a1d104903b0acaf58d9ffdf916", "guid": "bfdfe7dc352907fc980b868725387e980824e4316ab7bfcd0003b2241f44ad02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984402597fd01da530ebffceac747643c7", "guid": "bfdfe7dc352907fc980b868725387e986b72cdcf7679934aa4009ef9b5ae2f7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980865c493d2cf4bcb213dd2299378b9f2", "guid": "bfdfe7dc352907fc980b868725387e9893169a8c5f52d523b63511dd688faa7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981826827a3aba34ffb9d453d1d3483dde", "guid": "bfdfe7dc352907fc980b868725387e98a4ed43722d9ad2b07f67a19823d97e50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8531eb1d99362f5a3c04bc4475f48c", "guid": "bfdfe7dc352907fc980b868725387e98716e04d18cc296a8526fe7517b7c6b88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ecae398993841f7e13ca07832effbb", "guid": "bfdfe7dc352907fc980b868725387e986a1af7cad2a006ed5c5c509ce91d939f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d9f61dfb093a774cad3bcc44ddd7a6", "guid": "bfdfe7dc352907fc980b868725387e9883daf3d56bbbdfacb751a0244ed7df50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2429e5b47da1d0c1ec8bf347ae5a054", "guid": "bfdfe7dc352907fc980b868725387e989eed4583846839553000ec72d42f6b10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1c766c1864fd07432a87a2653c9a381", "guid": "bfdfe7dc352907fc980b868725387e9893381ccfaec052744380184a84447416"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dc73e5de776984200084289cf80ba6e", "guid": "bfdfe7dc352907fc980b868725387e98d158ac7c8c0c7c16f9b64245d0e48bc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a26852ef0534b6e7197729c83b00f4a8", "guid": "bfdfe7dc352907fc980b868725387e985cbf861eeec495d03ab5305e88a50559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d840ce4aeeeb873ad4c5dbfb4498019", "guid": "bfdfe7dc352907fc980b868725387e98cdf4eeec65d4420fe20a7e3688f759a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811546a65ae41d4395b7210d180f3b7b1", "guid": "bfdfe7dc352907fc980b868725387e9849b60a5a2e26eb868dc25261fbc09f1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98590c56bcf9d57ee74ea8e90372624f0c", "guid": "bfdfe7dc352907fc980b868725387e9809b81d672a68bd739992ab2615b4656e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8b6000f0afe423c109b90eb1882d45", "guid": "bfdfe7dc352907fc980b868725387e9890db0f25f307aeb153327dcac2ecb580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f431d17b99576827c92f7792acc63139", "guid": "bfdfe7dc352907fc980b868725387e986634f832c3e816fea3953e576957c80a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98138ef25a273891b998531c82266863ea", "guid": "bfdfe7dc352907fc980b868725387e98da9191b331ec606a9061b49590fac446"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988801d17562b3c10cb60f17a2d6ff2515", "guid": "bfdfe7dc352907fc980b868725387e98dd3b5e7d7d9b6930a0d7efe8bbcd7cb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984190c73667a9f08c629803d7da1f91e4", "guid": "bfdfe7dc352907fc980b868725387e9894ccc26de1c1c926bb7ff11db3ff664d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b843ab1293d37be947be74b1548911", "guid": "bfdfe7dc352907fc980b868725387e9801a0047d957892810081e31429e63f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a34649a867c1c8b7a2a490257a04cce", "guid": "bfdfe7dc352907fc980b868725387e980acac1c87f943e2c6bc60013660c16ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e93f43cece25e94fe37142ccfffe5881", "guid": "bfdfe7dc352907fc980b868725387e9835cf36f5273a7e4d2055147ba4c78de3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e98b91e9709b7a92da336af2d55cac14", "guid": "bfdfe7dc352907fc980b868725387e9847f7c6de362d10e268bb3de844928c7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981595ab6560b91ecbdf443dafa37d1e16", "guid": "bfdfe7dc352907fc980b868725387e98624df2eb5badc3c7b7fa22a013fd8a1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b776447eef7b80a38ed65e0f7dd1a41", "guid": "bfdfe7dc352907fc980b868725387e980202639798874337de5b535d10654016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875abb7ce4e45b692c1f97f165925ca7a", "guid": "bfdfe7dc352907fc980b868725387e9824d5e95eb2d0bf8c9ceb26dc5b85c66a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a9474d7e7d59a231cb554cce087f9e5", "guid": "bfdfe7dc352907fc980b868725387e9818cc08dcc61f87c1ed3af643b351b40a"}], "guid": "bfdfe7dc352907fc980b868725387e986ffd37acf71160221163c3c757e11565", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98db261f9963fb907707d7ef884ab471ff", "guid": "bfdfe7dc352907fc980b868725387e988d2127e82bb796c908eecfe5cfbade37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983959fe369bdea0d2ff931a2ace0f37b4", "guid": "bfdfe7dc352907fc980b868725387e981e014c6206bd468f957a6e0e4f68f6e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd82f1f5ca79512e5142ff26302548c7", "guid": "bfdfe7dc352907fc980b868725387e98550c1780d418d78527815e892965542d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1719299afe91e2ea1d288374db76ada", "guid": "bfdfe7dc352907fc980b868725387e985a40bd95265d5d721756d6d6704342f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8b9d0e272c18716ad28ce10f6e8976b", "guid": "bfdfe7dc352907fc980b868725387e983286a152072cdecdbbea96e0b4a7562d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f5ac4b06c0ef8f2efb2c3745779f21b", "guid": "bfdfe7dc352907fc980b868725387e986d0de1627990404dd1ee387f74e065b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bfd12c62f2e63e538cf15a5e66f3295", "guid": "bfdfe7dc352907fc980b868725387e98f87cfb93a2f637fc27c7c07746fa7580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4919bd6613bf977a33dc9f5d13f6258", "guid": "bfdfe7dc352907fc980b868725387e98eccfcd30571ac37631b0d46d9596dd64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118fc3c2e95c8a1a3b318f05ff84ee9b", "guid": "bfdfe7dc352907fc980b868725387e988727f9d4ec1c7ebcaaa38fcbaeefe5b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4be0b428d2aaab2d77b4e2ec26f806", "guid": "bfdfe7dc352907fc980b868725387e98e20354debc91bd97e21a8514e2d14e99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838bf0992496c04f9018f1195226e5388", "guid": "bfdfe7dc352907fc980b868725387e9857508f73dcb6728d0101adc3d104f3f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef3bad25e2221e395a740a845acd5984", "guid": "bfdfe7dc352907fc980b868725387e98131aadf7fbad71b721363de339cb8b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988112a4180eee33f423eddf8a8ff0a96c", "guid": "bfdfe7dc352907fc980b868725387e98203ebe8d4f1a417de24b92de51a46419"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898567c1a65943a6787c9302f216be887", "guid": "bfdfe7dc352907fc980b868725387e9869dbe9fd04efc43d9f000dceaccb2b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c1a2004a2143b0cddb2c43c6acacf03", "guid": "bfdfe7dc352907fc980b868725387e98352b20d08d176d560717d2c6dfe70dbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb8a06b12434cfcb65afa4f669a3c88f", "guid": "bfdfe7dc352907fc980b868725387e986b226fd80e25465d026b608bd1ea6648"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810fd4e182f1887632d82738bb80169bc", "guid": "bfdfe7dc352907fc980b868725387e98989b52559cb0545042ed68e0f7530890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d3470d32db9223d254dbdfe93a4da17", "guid": "bfdfe7dc352907fc980b868725387e98703f74facb5366dba65c7fb1ff0ae7f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bb73fd29a0fa344b6258f7ba5c0e083", "guid": "bfdfe7dc352907fc980b868725387e98c507c01bb8f0630aadfb0457d1b41661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50d8cdaf16b086b74f003c9a12fada5", "guid": "bfdfe7dc352907fc980b868725387e985a68e9a21e7324ced74d077c6910d535"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f30d16508b02a84ba27ee0b938e0e5", "guid": "bfdfe7dc352907fc980b868725387e9882e8fce4bd9a0c39e3a29840209bde53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801a7d2dc7604e86830adc45c725fe0bb", "guid": "bfdfe7dc352907fc980b868725387e98d9328aadd585b2a5b6160894a8de6368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d55a25c0fb332355a0d1862bbb07da5", "guid": "bfdfe7dc352907fc980b868725387e9850519fc57d070469b758b2031aa67a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd757bc0113d76e26ecf77505745355", "guid": "bfdfe7dc352907fc980b868725387e980d487a3512d4f90c5cada1885a754dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5b5adefd24f7aab26702f91ff81028", "guid": "bfdfe7dc352907fc980b868725387e98762e4650d37ea556332bf0b0d08c1bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb4b0bd809e83472370cd0576625ee49", "guid": "bfdfe7dc352907fc980b868725387e98f4da3f0fea79ea0075bcf7a8b79d132c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883686edccc4c368993911087fa7960ba", "guid": "bfdfe7dc352907fc980b868725387e980cc37e86b732475611ad203aae0a3cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983191bbcdac38d20148e8c51b4e0adf59", "guid": "bfdfe7dc352907fc980b868725387e989ab084e055fc3e64113870ca10b46aac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d922d7cc310c3ba4f8757cf525038160", "guid": "bfdfe7dc352907fc980b868725387e98fd341a34ece063b8a1a415826b016353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509df1bc67b08aa119245a55d2f512bc", "guid": "bfdfe7dc352907fc980b868725387e98f5e84106f034a3300f745e7c57242f33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989284f54975abc9c75cffa0156f6c37c7", "guid": "bfdfe7dc352907fc980b868725387e98b4095f756df2b26e440aafbd4cf1d686"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986246aac77bdfd6d222383bdc72f74262", "guid": "bfdfe7dc352907fc980b868725387e98f54f403bb3250da2deb660b658d64a45"}], "guid": "bfdfe7dc352907fc980b868725387e98034016056441fc3f420f207c415a728b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98952d881a353070d374fdcfa3fe5c9767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e736fe3cd34dacbc42b6af920b1000bd", "guid": "bfdfe7dc352907fc980b868725387e98270ec2464b42ff71f66892e248b22d2b"}], "guid": "bfdfe7dc352907fc980b868725387e98e656e34dfd908cb7f4e777a9ef3f029f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fad4cc00daa7543965996b27bde9f4dd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}