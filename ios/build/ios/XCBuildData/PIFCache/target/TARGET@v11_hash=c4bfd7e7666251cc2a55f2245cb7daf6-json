{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e414352ff61b24d89ea9c7ffba619ba9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98639abcb1767a852af4ddbe510dd0fd99", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c84a48c53651010f18f2de79938d62e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1e8ed8433a2198eb0c1885a2d1e9299", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c84a48c53651010f18f2de79938d62e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad62eb03465f66fc72541cc9d3d4c2b2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b84903c898070e5bcaa8179ea538fe4", "guid": "bfdfe7dc352907fc980b868725387e98fbdcbbd56883960efca0a44c1c145f12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882778a1840d6d030da5aee1c1ed6b95a", "guid": "bfdfe7dc352907fc980b868725387e98ec07d3e011e02f4c0789dcc406ff4b07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826309c26f854401cc7403d933a265ce5", "guid": "bfdfe7dc352907fc980b868725387e98354a26bf52d87bf9c236d58df94412c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2dc84c6bb0191e6d9aa504e729c5d20", "guid": "bfdfe7dc352907fc980b868725387e989c1d149ae6bb9b6f2a07cb3cba81ab88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fdd6a0e0f5dc9e0fd3b4d35e2e59534", "guid": "bfdfe7dc352907fc980b868725387e98ac645e47b61c56f6f200966ed72512cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0b2bc8f3cb4bbed4cbec7ccc5212e1", "guid": "bfdfe7dc352907fc980b868725387e984dfe6d30fd4ea15ffae74cf7f34d4f13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831223fbe54ba9e54354911f3420c7dbb", "guid": "bfdfe7dc352907fc980b868725387e98739945ff871f7279a67b24e6f2271133"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d976eccf2c36d5204e290865007370", "guid": "bfdfe7dc352907fc980b868725387e98224c0e4ba1a590b5792a1ddde94f0610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb93514d09cbd1bc1a410c494c64bee", "guid": "bfdfe7dc352907fc980b868725387e985463329b4b52b93b75ff9e066ad49332", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98174951b936e7076a4d5cc3f6038a33fc", "guid": "bfdfe7dc352907fc980b868725387e98e41202e8bee6df0e47f25b0201173614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa9da664d140a56d8c314e8f068b298", "guid": "bfdfe7dc352907fc980b868725387e98bcd4973229ed052c7938ee1a33bdcc06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d43052432c4727fa02f9bc38d9441c3", "guid": "bfdfe7dc352907fc980b868725387e98e594fdb5bbf0d4445066be2d06da8e1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98478341b64cfaa309fbbbf6c45798f2cf", "guid": "bfdfe7dc352907fc980b868725387e986da84b0489ba7f64c2e5dc982af36a0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb859bb1282b9fe9a1e93590fb3c4cdd", "guid": "bfdfe7dc352907fc980b868725387e983791b31b38edb13be8846aa18bb896d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc518b7970d71cd0dd065204e6a3d585", "guid": "bfdfe7dc352907fc980b868725387e985039a33b8f49c115e57685c33ece354f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c0a4cb93e1d525c83ba522734824eb1", "guid": "bfdfe7dc352907fc980b868725387e98649d4c39e687d30079c7546c0b4ff9d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b87c5e169b5e26ca2042358c78979eb3", "guid": "bfdfe7dc352907fc980b868725387e9870b10b4dfe8f1af71781cd1effbf6bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7ad810a511d676ad3f6d288fb54ac86", "guid": "bfdfe7dc352907fc980b868725387e9866f70aa5fd0fa60dec9c3bb560637ba5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a155f9c9b5119a2e52964d74540e921", "guid": "bfdfe7dc352907fc980b868725387e981743face72891b8634abfd8ce2077933", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d8d104ac37f60759fa3000886d1e21", "guid": "bfdfe7dc352907fc980b868725387e98a5555f992679f0025bad680c7c6d0cd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e9467a67a5f6d144140f5ebb283fd1f", "guid": "bfdfe7dc352907fc980b868725387e98931327afe1aeef60a1aac671761bb8e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984173c50d169dd8ed5dedea050214d6f4", "guid": "bfdfe7dc352907fc980b868725387e987c7d5d8531fd19424c04525cda9e0522", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981a24a04a148f9ec918f6dc141e6b4f5e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986164868f777e6120189750d716b8dc8e", "guid": "bfdfe7dc352907fc980b868725387e989a2361529bab399af22e80feb56b3bf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7a2aee96b63b05ca203f89a4a452ce", "guid": "bfdfe7dc352907fc980b868725387e98f39b4b3822aaf1e2043588c9a62c5562"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0add6157955df35eac847cc05afffb6", "guid": "bfdfe7dc352907fc980b868725387e9861070de1bad1b11584c2f9852f609179"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a3cbf98450aff014f2358e9efc356d", "guid": "bfdfe7dc352907fc980b868725387e98c5d9d0ae30d0cd05c0939b5509d79450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7d58734e2ffed11fcabd6664f51757f", "guid": "bfdfe7dc352907fc980b868725387e98f1218e43dfaf5d65c4d3d2fc69f0e05f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f72b5911d3b59bb2c6c415f56030cb8", "guid": "bfdfe7dc352907fc980b868725387e98addee8e2ab1cd999cb0a26d5b5dce175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876e7093b3213dbbea54988c5ee0f5984", "guid": "bfdfe7dc352907fc980b868725387e98401715c33d7439391cfa6ac015feac09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98749aa10fd117cf191b8ddcb68dfbc921", "guid": "bfdfe7dc352907fc980b868725387e986b0cf65e5dcd99e2f024239dc2fc5b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985838401e2a155ce7972edc600a740ef0", "guid": "bfdfe7dc352907fc980b868725387e984e4867c9f1db8c53a8e2ae920d611665"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f648d3ccd3ac78e43de224025c79fe8e", "guid": "bfdfe7dc352907fc980b868725387e98aa49e5a7a9539f3449cb7845ce4ebab6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3622d6426945bffba0364af0a9d2633", "guid": "bfdfe7dc352907fc980b868725387e980314e2a7869b38fb06d0548fd0406602"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836c10d48a9e608232e3047acee2bf497", "guid": "bfdfe7dc352907fc980b868725387e986125a8ec384dcf4fd93d0dd533626ad6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e058f049e62d72cf7a01adee8f0bf2c2", "guid": "bfdfe7dc352907fc980b868725387e98d15305ed7aa61fb83051958999cd292b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac086b5ddf41e07b810ffdf48164aec9", "guid": "bfdfe7dc352907fc980b868725387e98d6d00521d6c7622d60620012aeac4eed"}], "guid": "bfdfe7dc352907fc980b868725387e98c29833e4f06b57dd9895ee69e20e7c36", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e985b82cdafa3082d6d43a9fe366f15ea9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a61d761249950157b7def56ae6875b", "guid": "bfdfe7dc352907fc980b868725387e985645ab7731f29346cd051f532c039d2b"}], "guid": "bfdfe7dc352907fc980b868725387e986ff080782cc8cc09e1f83af8df25711d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c30de06f018bddfccd1bac0f4c131d52", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}