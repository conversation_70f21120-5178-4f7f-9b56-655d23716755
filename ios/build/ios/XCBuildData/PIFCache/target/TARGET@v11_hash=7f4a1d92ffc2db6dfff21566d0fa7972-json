{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e61394dcaabca51093320f3b2ce669a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5030bee21ab4f8c175c3d0abdcb9559", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98970eed6a0afc8e2bf7b18893abe0becb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824a83afc18a5e5d8757788043911f6da", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98970eed6a0afc8e2bf7b18893abe0becb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981d6f3f5bd9d0a3dd2c2956da1ac13d1b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c3d432e4810a9bf7a9e765bbcd2f88c", "guid": "bfdfe7dc352907fc980b868725387e9886e93b42571f3cdd551c17c61c3ce152"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de1d80e07d8d9de40aedbcb53612afcf", "guid": "bfdfe7dc352907fc980b868725387e98bf525176f5c8a11ed807b33fc9c5b5e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ab036fcd9db7503f8b30828074aeca7", "guid": "bfdfe7dc352907fc980b868725387e98f7d566edaa6df5ab061fc4011c38d182"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae1387017332963412ee9897cc3cde8", "guid": "bfdfe7dc352907fc980b868725387e98e016c6fd6bdf0763c7e0ced390cfa036"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c31efaeafb6c4b56b3715c8d068d2b4a", "guid": "bfdfe7dc352907fc980b868725387e98b11dbf6ef5c1d5e220a8adcc5fa91e12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899935df78d00db34257b1c59c8d6be42", "guid": "bfdfe7dc352907fc980b868725387e980e34fa7d46694706431c54ec5dc88c14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abcd02cb673e47e54abad6c80a0afc02", "guid": "bfdfe7dc352907fc980b868725387e982fa2207de3f9307ade79d081dad12659"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98159e6f08de24d4c63deb3d2774596120", "guid": "bfdfe7dc352907fc980b868725387e9838f86048c8ce5e6997e09b445b6059a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888501f24122d02a2f3579ce35da39b91", "guid": "bfdfe7dc352907fc980b868725387e980a0d96be1cd2662416d0450ca88f095f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886026155305d640008900793c77192fb", "guid": "bfdfe7dc352907fc980b868725387e98549239828d575354c26442ce4e44b697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f6737f9a0c205e020a1d2ac475873e", "guid": "bfdfe7dc352907fc980b868725387e9876b7f459a5bca9b498836eb426cfeb4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7ea5aae10696f67762ff3b4cd2a3bfb", "guid": "bfdfe7dc352907fc980b868725387e9882da393a85b9c541206c867da5786c29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a6bf34742d05b90b16e0aa310a68b1", "guid": "bfdfe7dc352907fc980b868725387e989c26f7974732b4ad7788ba19f5d4bd8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899097a0b397a895b5f0c7ca216d06faf", "guid": "bfdfe7dc352907fc980b868725387e98d04cca74503a0e2f5c8c44d7765a3120", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afb90e76cdd21dcf7680a5ec8e04308", "guid": "bfdfe7dc352907fc980b868725387e989ac98f44ee4478de95f34564edd41135"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98573227909bd0ff3e2c8815114d78c1fc", "guid": "bfdfe7dc352907fc980b868725387e98d3939454a3545a47f4773f2a8068d153"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f15cf87cbf54a1700c965e4949adce", "guid": "bfdfe7dc352907fc980b868725387e98280454b213d659cc2e214d0694c59459", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe945505d266def540535bcdf9e6e8a6", "guid": "bfdfe7dc352907fc980b868725387e98160a5bf991d1ad343eefb09b92a8763a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60a1081cb4ab85361fb6f3cf0553dcb", "guid": "bfdfe7dc352907fc980b868725387e986292bed000021041aa7665e93e9c640b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a859336fdc3d6a2d5a5e7c3f599c4b92", "guid": "bfdfe7dc352907fc980b868725387e9805faac7ea73ea27ae877d52d4d72c69d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98619c36e14e546a6f162f56f570ef49b9", "guid": "bfdfe7dc352907fc980b868725387e98a0a902084b3e07e8423751efe8583d59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897441b7477a84bf0a1aabbc1c1ec43c2", "guid": "bfdfe7dc352907fc980b868725387e9871c9b4e6f07859bf14300c13800cc637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae7eedf584cb04561df5ea922b7fbf2c", "guid": "bfdfe7dc352907fc980b868725387e986b76671b9fde292d223e359dea482c89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6c922dc108f48f0d9f330cc84fad39d", "guid": "bfdfe7dc352907fc980b868725387e98bbe9f4123f5004598f1286c389acc7bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be2d21df325af1912dbc55665cdf94f1", "guid": "bfdfe7dc352907fc980b868725387e98a3e1a85960228d90ce47353e67af5d35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4da8ecc3e0d7632cb2321e861c00b5", "guid": "bfdfe7dc352907fc980b868725387e9848f141a055fc3378d4d0adc3d0ca39bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116fe5a32dd0230a507e395be07985b2", "guid": "bfdfe7dc352907fc980b868725387e982a1bc00e42a053ffc4344b1d80048a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdab65db9a0f47c2f4cf8aabcfb0b219", "guid": "bfdfe7dc352907fc980b868725387e98316694b756286f42a29b5255269f86c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde68678747c15ce9e7c0719982a0c26", "guid": "bfdfe7dc352907fc980b868725387e985924106d110335cf6b2689d39787d67d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e0ea75538c9bc12afbe24d8954f0bd8", "guid": "bfdfe7dc352907fc980b868725387e98304f791bd137b127386fddfa1e9ca541"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832824dd96086ea5b004681502d4d86ba", "guid": "bfdfe7dc352907fc980b868725387e98fb17e453d2cca4a64c9b6ab6234298b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98182e130468b8f45a91bbcc11bacc3cd9", "guid": "bfdfe7dc352907fc980b868725387e985bdc914d813a6f2139cf82da678c1529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4f02f3bb1ea99f2144222f7e60dddc", "guid": "bfdfe7dc352907fc980b868725387e98f56195382fa7e09d6781b0ff4a244bd4"}], "guid": "bfdfe7dc352907fc980b868725387e98fa1a2af8ff22ea548f40eed1faeba364", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988db728f1f2b3cd2781ee737bf247868e", "guid": "bfdfe7dc352907fc980b868725387e98f57868ae3ec4dd1b9f53d3c04f714339"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bed4182f05d7938476e0ee9629720df4", "guid": "bfdfe7dc352907fc980b868725387e98736beaf218f403328f671bdfac771d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98425ee0ab9ffd7bf9ad9ff55c52b13132", "guid": "bfdfe7dc352907fc980b868725387e98d75c5c9d5f9a49516caf583a0ba3a739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852be1557908aff88889cd13e767dc721", "guid": "bfdfe7dc352907fc980b868725387e9812d382faad8f0ba25cd2345509a95128"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51be0c9d32fc970770ceda898b5d8b4", "guid": "bfdfe7dc352907fc980b868725387e98551da6691c5d32ad41eba74d2054d0fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897576afc59d6fda82c73bb41cfc0b712", "guid": "bfdfe7dc352907fc980b868725387e98df41c45f1f7cf12fb436b8a66a6103d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda5ec33023b56b248c16033d7eb1951", "guid": "bfdfe7dc352907fc980b868725387e985bac82e26500f94bc7fc4aac3b63acf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98560f7c1e0a13e1f3239b0178d36d5082", "guid": "bfdfe7dc352907fc980b868725387e988ab84f1685a6c636ac2ed93965e453aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883def2b75ef63b1b3df110abc9f1c354", "guid": "bfdfe7dc352907fc980b868725387e985b4b733b225cc015185647daf369f274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984229b43facca620ba67886ef8d74b776", "guid": "bfdfe7dc352907fc980b868725387e980f4dd3ef702f989e85f04b19c2501aed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f14ab9795eea724f81d803d0471104d", "guid": "bfdfe7dc352907fc980b868725387e98198d44ac11d38ba1788e5a94c0a3dc07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826889a738fadcb044aba513dec3b720e", "guid": "bfdfe7dc352907fc980b868725387e98ed2d7ecde67b0fa3e2604ae099645383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983926ce286c9a1ee83620cecb761a7f1f", "guid": "bfdfe7dc352907fc980b868725387e987fee19f3888e82b3a000961c8defa8aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed5ffbfd11f13a4186b186a4afd7f21d", "guid": "bfdfe7dc352907fc980b868725387e985da02dd2d483a5fab3bca6838ff3b46c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ecbcfa0892a94746647a9de92804fdf", "guid": "bfdfe7dc352907fc980b868725387e9880f5c5d71d27791f7f467bcbab8713a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e1a0113b5b1f9c114aeaa9a4aabf75", "guid": "bfdfe7dc352907fc980b868725387e98058de7649d9dcd0d225a07f2e31525a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa2e9ebc21c49537a45d5d3936a975a4", "guid": "bfdfe7dc352907fc980b868725387e98849ddcc9d920e986953dba35e2e641e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7deefb5de12c1f3c5182bb5348016ba", "guid": "bfdfe7dc352907fc980b868725387e98cbf2b3d9f74e132488a74cb0aabf33c6"}], "guid": "bfdfe7dc352907fc980b868725387e985b2aafb6b1c816f00ddb722d01d0ee91", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e982bc71b2ad0ca6da5d3786e3c4dd8df56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e98c0a73b5d26686014c9676b5d21b836c5"}], "guid": "bfdfe7dc352907fc980b868725387e9854a4cf5b33d6894538445e6229268603", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a579fbbaba4962e0173c9508366ce8fc", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98356a3af1777c660353cba401e3838aea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}