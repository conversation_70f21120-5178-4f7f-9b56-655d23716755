{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f6d9fe140c982c19c8a97e7b4013eb12", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FMDB/FMDB-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FMDB/FMDB.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c8b7a9fb5a7d4be60b4be57c2edf6ba6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9816abbe9f5ecb77e94030b61c8ce9a022", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FMDB/FMDB-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FMDB/FMDB.modulemap", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989eea7d3ee4cf254117682ee36f6853cb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9816abbe9f5ecb77e94030b61c8ce9a022", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FMDB/FMDB-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FMDB/FMDB.modulemap", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e27ad195513e66b1a4151138e628e167", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989763b9d8d5bd2535d24914e3a193c545", "guid": "bfdfe7dc352907fc980b868725387e9865fda49bdf1dcfa4e9da26ceb8165bd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801bad71cc210a2924c073fe54faceef5", "guid": "bfdfe7dc352907fc980b868725387e98d20ad6972ddba643b1de6337ed9e1f7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812feec6eb27cae50a591111d69f9ca88", "guid": "bfdfe7dc352907fc980b868725387e98c2b68184cd35f122410d186265b980ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f87a51e674a2dbea7a2bc10eb3be5320", "guid": "bfdfe7dc352907fc980b868725387e98efca2ce05f51fd6be386deb70052f223", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98021c2bc239f2b1a73672538ebd6e46c8", "guid": "bfdfe7dc352907fc980b868725387e982626799b1333e45c1f998331bf381382", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98746a08e6451c244cd1cf8626bdaea8d5", "guid": "bfdfe7dc352907fc980b868725387e9856128025acc78956afe3088802c254e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef6952f36330364cb27bb32ebf7296c0", "guid": "bfdfe7dc352907fc980b868725387e98bf6fae3a591a6f864d5307ffa3570cb3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9884058f59e7227408554da08a6932b5b3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981a56b5810c20c0aee5e5ae9e89a7bca7", "guid": "bfdfe7dc352907fc980b868725387e9873ca6e9e1440aa07e463192bb0a1ffd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f11c9b6afeb87737038e374c9b21956e", "guid": "bfdfe7dc352907fc980b868725387e980535748d78847508675e078433c4f669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805df072c713821c0ca5571578c521746", "guid": "bfdfe7dc352907fc980b868725387e98f4863a505748b22aaa710e87124f07cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e4c74acf52c7ed3a73bac805a94b7c", "guid": "bfdfe7dc352907fc980b868725387e98bb3d3d7d0ed05f2532b64e193475371a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bfe5978d08e92b0a3afb58aa7453e80", "guid": "bfdfe7dc352907fc980b868725387e98805873256ed450c2a2ddf764a5f66369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1ce9857d7809c5373713b63d464cce8", "guid": "bfdfe7dc352907fc980b868725387e98f5da93ea763ba8cc3e4d861507342381"}], "guid": "bfdfe7dc352907fc980b868725387e98b9bc9324f90c2dab03adee919396678f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e9805902f56216b1706a8aabbabc4bba50f"}], "guid": "bfdfe7dc352907fc980b868725387e98071ff318d59a6abed859980fd2e05632", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988d3e5636b683337cacda1e0e0c428b0a", "targetReference": "bfdfe7dc352907fc980b868725387e98cab8b41b13f524e82b01b79420264253"}], "guid": "bfdfe7dc352907fc980b868725387e98085578d61aa97f83401f998e64ed1cc8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cab8b41b13f524e82b01b79420264253", "name": "FMDB-FMDB_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98e5199a3f70555689be7018ee7f9f878d", "name": "FMDB", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983a0b2834dab2d28f0f87c3e6cdedcc0d", "name": "FMDB.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}