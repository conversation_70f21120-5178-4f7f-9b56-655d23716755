{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814f65698f703e662ffcf7e1567a3e2e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9823a1e87901ddf1adfca10a4a7c954816", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9725d2d8c0d0342186c75bacf2e78e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c568a80ceaec9884a6a9927c79769664", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9725d2d8c0d0342186c75bacf2e78e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98310bc9684962cf498ca3aece34285b6c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c5a0afa96d81352853e2d65d6bac7da", "guid": "bfdfe7dc352907fc980b868725387e983a43cc0daace54edee72683fdba054a7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ade9a6802954570a614daf43e52a1023", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a4c44ef9aa2a25ad8eaf0968b6b1f84e", "guid": "bfdfe7dc352907fc980b868725387e982f52b64eb6eb76d10eaea7a78f5b240a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8974f93a6365f6fd0af7db34d6b3b7e", "guid": "bfdfe7dc352907fc980b868725387e9850602b0c0b35f38998f255b6a3817541"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878215102d89d25781cad60d7e146e448", "guid": "bfdfe7dc352907fc980b868725387e98fe657875747ae9965ce94be8d30a4ae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d38dd5768b5cbd92de3df779755a5b", "guid": "bfdfe7dc352907fc980b868725387e985e75ad662b0c43e7df931dceb0406e05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc9f7227bb5a6ec5d11b099b3373fd71", "guid": "bfdfe7dc352907fc980b868725387e985ef0d5369630664437ee66cd1644e39f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cf65653a039b997342f76666155b94b", "guid": "bfdfe7dc352907fc980b868725387e98a3dabe7078c9c14397e2eecd0139895e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f26587b107fac0ebfdc40ece906217e", "guid": "bfdfe7dc352907fc980b868725387e984950f919fc36ca17c5f95673c7a3ee08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985011522d9698f563270ec96d67980cb0", "guid": "bfdfe7dc352907fc980b868725387e9819a020f56d7fe2c8e562b43502fa70fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c76627858b76d0e0689882bf2b82771", "guid": "bfdfe7dc352907fc980b868725387e9823701b4ce69bc3a3959ff20d88a3dfc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a9c83128f087ed73282cee708aab62", "guid": "bfdfe7dc352907fc980b868725387e98e56698c97f2e8e1b78f3c42c5418fc1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab1e69012fd35140e7ca67c6f43346c5", "guid": "bfdfe7dc352907fc980b868725387e98e6ada392d8f61e8a654ae6077b3ef736"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf4f4fa4beff68fd1b80a55dc3043d0", "guid": "bfdfe7dc352907fc980b868725387e98913cdaaeb9efcb311a6f005b82dbe203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873c4a5647aecbb1812a30c2de62d9875", "guid": "bfdfe7dc352907fc980b868725387e98599c542184664a63d647198538ede1d4"}], "guid": "bfdfe7dc352907fc980b868725387e98d82eb68aaf15ec4d3389885474486dce", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98570130a659a019b4909b94cadcf5b0b0"}], "guid": "bfdfe7dc352907fc980b868725387e981176e8a446fc0f2bd1eb5dabb686b78c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984f68f0828824758d5db6e3d365eb42d5", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98bd33f80c8dc32f6c884942b8bbe104b3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}