{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b540d7c52b568675ab689e939210980", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c2037de7b17e9635dfd542578fbc9ea", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986921aa71890f8495620edf9689edf040", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980bebe1c7055a0b797ef28895d3621efd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986921aa71890f8495620edf9689edf040", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a650e2c75b7563c4de0f43c4b917399", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98650c601b3d1744900824cea61f4dff66", "guid": "bfdfe7dc352907fc980b868725387e98f046ec9bb4b4c36585540bd5e17576d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3dc65213e3da39cef275a59e2564732", "guid": "bfdfe7dc352907fc980b868725387e98e881f217c8b95c81becd9a4ae1b52964", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22fc6f51365a079d01f59c79568ea2f", "guid": "bfdfe7dc352907fc980b868725387e984142e78a96f299251b2c42cfc1cd75d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843aa79782701c37a0207246e4fa5abf7", "guid": "bfdfe7dc352907fc980b868725387e98e26ad6c90593b11f1df245e40d5bba0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bdcbe5b7726d8fcdf60afaa9a547fa6", "guid": "bfdfe7dc352907fc980b868725387e986ff0444137ebd9e528624b775eed11e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986030026c720292f683972878be5e40b4", "guid": "bfdfe7dc352907fc980b868725387e98d236dc179e7792d7cbb05ab3785a7bdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f102e3b3c4536ef96935d673849386", "guid": "bfdfe7dc352907fc980b868725387e98f9b1e116123106363713c7ad6d5b578d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce8f988c229420cb547af29ba4dd5dc", "guid": "bfdfe7dc352907fc980b868725387e980f84b2c42b7d9f64dd9d250aecd6681f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d406dac1b5d6e37eacb14444e792bdc3", "guid": "bfdfe7dc352907fc980b868725387e98f686df5b00d63709599d7dfa8a885309", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa2a301f181b5451663557d0215b2c26", "guid": "bfdfe7dc352907fc980b868725387e98e577227493a26c0bad7e7af0f591781f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe25f1eb5fb193501f872380b703930", "guid": "bfdfe7dc352907fc980b868725387e987c4601cbc4edc3f6ce0c29af9789dde1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b435a6f9d33468759b17ef50290064d4", "guid": "bfdfe7dc352907fc980b868725387e9851127f1e3d23627f78297f32d7317410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5d27866836dd90bbf3cbf07b5fac14", "guid": "bfdfe7dc352907fc980b868725387e982a4742b7bc50776e0419e2d5ad5429de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1e5494b41f8c037634c7b03a5113e53", "guid": "bfdfe7dc352907fc980b868725387e983a146483d3ca225d84ef7c83ba432543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988161beff5466480777256504244b3f4b", "guid": "bfdfe7dc352907fc980b868725387e9873856206962878c6836d71b2145ea16c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3c586f73495926fee3f23159c09eae", "guid": "bfdfe7dc352907fc980b868725387e988b3d1ef98df5f9f45579a890982bdacf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc7a88088bd72f10b787722512515d7", "guid": "bfdfe7dc352907fc980b868725387e984258741e42c3cf46915a3f0e12207096", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987afdeecb95757af6839f4259da25693e", "guid": "bfdfe7dc352907fc980b868725387e9891d99a6afbbfc6c9c2f81adf8eb94c26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c4a6cece06eefc6b36cd3b8e108eeb4", "guid": "bfdfe7dc352907fc980b868725387e98aa26664f6b7d4a4661e0f28d9cf6b2f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d027eadad0569c6cb049e60ee6273f", "guid": "bfdfe7dc352907fc980b868725387e982cbf32251d30511e24a90f6ff19bba8a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb78bef09b21800a2933cb0b7ff255e9", "guid": "bfdfe7dc352907fc980b868725387e983e20b3d141a15145cc55ee9d40f4e0fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c762866eab68d71856d799785257524", "guid": "bfdfe7dc352907fc980b868725387e98c7511df8bc299ab7aab294641aba860d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e38b1a036f3a45e5520a85c53664def9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf0c41f30938755a9258f7ddd558b818", "guid": "bfdfe7dc352907fc980b868725387e98b6cc85814015ed89fbaec3925b95ebb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa183a221756343c92e6fa8564112272", "guid": "bfdfe7dc352907fc980b868725387e981d1b1fe4efc2214dcd61737e2d98648b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d89ea9007896cd19549803a0d937c12b", "guid": "bfdfe7dc352907fc980b868725387e98f0ef3fb7e6281147054d4315d1957b49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac7645b050c0841ee97f05fe3d03864", "guid": "bfdfe7dc352907fc980b868725387e98a714d68bcbdc34323b2bf31afb74adc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616b7b8947dd249e762ae96cb833a421", "guid": "bfdfe7dc352907fc980b868725387e98bbedd4575c9d1989767e5392ea433567"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983290a38cb10921d798f0837d0324620b", "guid": "bfdfe7dc352907fc980b868725387e9895094df110f940f8ce11108a9ee505fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f205e25a0463b0545379b37fd047845", "guid": "bfdfe7dc352907fc980b868725387e9876785a1858e9daabae9f1d7501a642ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bf177787a5260ac1f343319301033f3", "guid": "bfdfe7dc352907fc980b868725387e986ab25994e4e8452136ea1157c7b8e133"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc90b9f3f2177d64eb99044df67b092", "guid": "bfdfe7dc352907fc980b868725387e98c5267b8040601b2d74927bd555460a3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad32f2f0465042da8cf35a05f97afb16", "guid": "bfdfe7dc352907fc980b868725387e9819ca81ec3c8c2306294b841f3997ecfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46d82bfa96fb69109886e497ca72151", "guid": "bfdfe7dc352907fc980b868725387e984e43c91397310345b0ff92b08163ecc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d53d7c2e59239732b9fe9cc923c380a", "guid": "bfdfe7dc352907fc980b868725387e989ee473aaa12b59a7d3c37ac18292b672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a39a2cf8f4abf1c7799df3d37673c073", "guid": "bfdfe7dc352907fc980b868725387e9880be2da23df7f4f8aac391e5511522ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b201ab9bc19f1b5ed88e6f6573f26b0b", "guid": "bfdfe7dc352907fc980b868725387e9809185166f8d28a11132149b9d5b54f53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ab17a86cd139abaad3df1331598907", "guid": "bfdfe7dc352907fc980b868725387e9860cbb36b1a1f1a0c6ea92bc01b4a4ca2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eeb0515e2fea4179c0b252ba377d7f4", "guid": "bfdfe7dc352907fc980b868725387e98511e88e198ebf3cbc79c4519baafaf12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984329b2fc58f74c4bdf3893213d3a0683", "guid": "bfdfe7dc352907fc980b868725387e98c6e493884d09deaa40de42b858cbb7ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df0c12e16c9482a0c4945c6ed8a8ac3", "guid": "bfdfe7dc352907fc980b868725387e985b9c5db6535d6014da55f7281d72c7a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6e4e5212dab636462428dbd2279c3b", "guid": "bfdfe7dc352907fc980b868725387e98c353cde2de64aba4339993ba9259adbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e2f5ef8728d7d1ff061cbd523ebbbc9", "guid": "bfdfe7dc352907fc980b868725387e9841763ce3c0d28dc610531419497d34a0"}], "guid": "bfdfe7dc352907fc980b868725387e9835749cfc777e5f540af7946d15a18aa9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e981cc43e921d25196c3bc19f66626327d9"}], "guid": "bfdfe7dc352907fc980b868725387e983145b1b4a6680abe2b9236be5d6401f0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d0702940fe4f06e5469755466d32af96", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e988a77991197f4600eb72a26615c46d35a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}