{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a1ffb5a9feb439b78f8b71387dcccbb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9870aaeead79c7fefdf64eb7d3e4af3784", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9881e51a6457fa6ff3002ab0339d525fc8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846dbf17c909baad90c899981bf7b58f5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9881e51a6457fa6ff3002ab0339d525fc8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e1e82d4250cb8a0155cd2e7a563dc21", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980fa92c975ed229053082801e7fe9c9e2", "guid": "bfdfe7dc352907fc980b868725387e98751c5cdb45eb6aeb5ed678892976cee1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f9e752199868c84aa7597120bd5037", "guid": "bfdfe7dc352907fc980b868725387e98cb131a3adbda17ec7360ac4fe8c6702b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873466500f24b385f2ce8988c48b9711f", "guid": "bfdfe7dc352907fc980b868725387e983379b2dc78a819e87c059e3c070dbd6a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9881e7e86a9bee926e1c244acee5fcfb32", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b82b7d88bbda7866b9bac34daad2f521", "guid": "bfdfe7dc352907fc980b868725387e9843efd10bf77fe59ef80f6e921a7df14d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890f6109ef8d99218ccb9b8a84b806f32", "guid": "bfdfe7dc352907fc980b868725387e98e81334de4676546946004bf7a0867d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99ea235e9e9a27cf443575721cd3e44", "guid": "bfdfe7dc352907fc980b868725387e98c5633d0aa82a27dc3a38d80ecd9c8c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a31823c26ee007acb519d2e4d7b7722d", "guid": "bfdfe7dc352907fc980b868725387e989d05d3c70cc0fffa2bf2a97c343169fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98680a71a408295f891e7e512f6abe401a", "guid": "bfdfe7dc352907fc980b868725387e980f5455780ca682570c6b51f66f46a714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3b3bf9a64a2d16d4b025a0044beec77", "guid": "bfdfe7dc352907fc980b868725387e98dced7fa2bb5ec76002029d0294a0db74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820ca8517771905e1c12108b910578e61", "guid": "bfdfe7dc352907fc980b868725387e98bd2f5f98a5f9880f4040ca2b63ae80c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e66f84ddf33b6eb41cead444be05f6d", "guid": "bfdfe7dc352907fc980b868725387e98cf2c23b979e169546ac190518135208c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985efed22168d91782e307d1a5d519bb4c", "guid": "bfdfe7dc352907fc980b868725387e985d7609879a75567fdb5f547de7162e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f00b4948ba1db23dcf3effaca24ba1f0", "guid": "bfdfe7dc352907fc980b868725387e9875232ae3a2419daa7bbe8a5e5b92abd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb57b122aeb1ee4767a79b78a47484f", "guid": "bfdfe7dc352907fc980b868725387e98a489503654ac586a96699fa41d0b3cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a8b998639fa141fd3dcdcff125a02dd", "guid": "bfdfe7dc352907fc980b868725387e98725305bb6dc27bf12e8714474fec18c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a1d8be9af017f24261e78b72d170fd7", "guid": "bfdfe7dc352907fc980b868725387e989e1f202eb8b211eaaf0a5292169015b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820a94a0b463484194ab7c23f160c4077", "guid": "bfdfe7dc352907fc980b868725387e9830692b7baec36e6ed6e0bbc669887c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3cebba2cd7460dad93dbcd6de8c8082", "guid": "bfdfe7dc352907fc980b868725387e984c4556807f1f60b1d2773bee9d46af57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3cb679830959879346b2daff295e308", "guid": "bfdfe7dc352907fc980b868725387e98418adabf4ab656d3207c52fdfc7f4527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9227680dc4fcfc352d87b22c14ae7b3", "guid": "bfdfe7dc352907fc980b868725387e987b5bdd9f403a44e6b9cfa61ef56c7d0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984feebcd082900d6f2f05ebc338094303", "guid": "bfdfe7dc352907fc980b868725387e98ed3eeaf7a951401fcb5c1b3c954d3e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bef435c386502ac9d137d3aaba4ff08", "guid": "bfdfe7dc352907fc980b868725387e986a789c431975b53a4f736ffcf2ea07f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d9b20c5377251adc8920a27e8d2f85", "guid": "bfdfe7dc352907fc980b868725387e984f0d721b36c583edfdfd1b30163d86bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ff68ba97c15be96eee40882401937fc", "guid": "bfdfe7dc352907fc980b868725387e9852662ef227d9e8c0c836f71d477db1f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899a5d77312beb42cdea04d5a8af20ea1", "guid": "bfdfe7dc352907fc980b868725387e9874ce4504c4145cce9a110f814ce1faef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc9a94f63a5069a528dbae74a3b495aa", "guid": "bfdfe7dc352907fc980b868725387e9833b81cd54cce766f034630635cdf4ea5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab3a2b3c84b180334388d70095a998b0", "guid": "bfdfe7dc352907fc980b868725387e98f948b68d3aceaa2d8d441b2eb8d82eb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ac13134795d36ce441899c4ecc38821", "guid": "bfdfe7dc352907fc980b868725387e98da525e25877d26b32681bc44d37bfba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c119bb2fe2ee7de9318e4a5800df76fe", "guid": "bfdfe7dc352907fc980b868725387e98bff1a5bb0392fbd51c60224c422607e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf34696822bd6dcaa62fa3f0c9d928a3", "guid": "bfdfe7dc352907fc980b868725387e98d829655d158b97c454401594e4d72080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802106b23081f4b5f8572261971129697", "guid": "bfdfe7dc352907fc980b868725387e98276368c3a17c2f86743ed0cecfdcde98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807da490fd9194e485a4a067ab46a9007", "guid": "bfdfe7dc352907fc980b868725387e989e4672da227867fe39a583c6b16fe893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e2843d3e7cdc14097b79cc6a93aee1c", "guid": "bfdfe7dc352907fc980b868725387e987cc86d0e1534a348c5bb2fbacd6b0d84"}], "guid": "bfdfe7dc352907fc980b868725387e9806489f7107955a60c6edcf745d40b9bd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98c76e70811ada38d59366c746867b297e"}], "guid": "bfdfe7dc352907fc980b868725387e986d4c4edb50e576d2946bc0c57d4b90ca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a643ee818a22023d4c33d2b5d01f4801", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "stripe_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}