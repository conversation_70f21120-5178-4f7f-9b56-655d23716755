{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982fb67575ec576a8fbd4cb3cd5c5bbe1b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db0536955b7e10ac14cfd284b69f8e99", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f72d6e4f8fd1e29cc24da93850b0a035", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ed8ad88f09c90922fdd4d5a58e50de1d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f72d6e4f8fd1e29cc24da93850b0a035", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c69d6e176a55dd5f14365ad2fefcf294", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984b835c4833c3913b53ee4090a18848a4", "guid": "bfdfe7dc352907fc980b868725387e98acbac8c383c56c2b34c2ffef524d2c82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c549c5b62ef375132e7a4a3e4eace3f", "guid": "bfdfe7dc352907fc980b868725387e98c0a68c86b5156614bd1c62f41c9f4137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0aa96d4784cb9bc9f3fd7762e855f76", "guid": "bfdfe7dc352907fc980b868725387e981f607c3b4eb23ba95e5fd186a9468664", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802d5e947f67195b22ffaff11832f37cb", "guid": "bfdfe7dc352907fc980b868725387e985ddfd304aac530847f92ec9ad7d79b89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d517ea041811d14fb411bd76b9e2ee3", "guid": "bfdfe7dc352907fc980b868725387e98ba920ec41903dd813c154a6a1d9323b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98356e6599ed0dac26547264ff72648120", "guid": "bfdfe7dc352907fc980b868725387e98176ca4f426761195e112a7e780036d36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a0c384f9abc88538480ef61d686724", "guid": "bfdfe7dc352907fc980b868725387e98754fd9cb976500b0f86b64d196d411ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3cd7cae5fa77be6d7f4db479e5be15", "guid": "bfdfe7dc352907fc980b868725387e98eaa36d1e07e5a77e748c06c00c1d2b3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fe6cd479b62d3d05f4111e68fe66646", "guid": "bfdfe7dc352907fc980b868725387e98def5349f7bf6d4b1b94da4e67c6d94b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a165b0dab6cf48e29e116e8bb5f46f", "guid": "bfdfe7dc352907fc980b868725387e9871dd4d2c1c977940ef18a373299941c8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9842eaeefe60c791280cfcdfe620465241", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a0f0f79086036b0e1d261e724cd36c", "guid": "bfdfe7dc352907fc980b868725387e98a07ab3357b130ac81e3b64cc96c6b4e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984284c83c2283962f0848a2d08dfe975d", "guid": "bfdfe7dc352907fc980b868725387e987dc138bb1a4a67b6127ead7f8dd1cd23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5da671a7c442061cbe9e3022568ee1a", "guid": "bfdfe7dc352907fc980b868725387e98e1c69a998a88ce0b6cdf94ac84e45af4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868fb4ec09be02ced9d30e941102755bd", "guid": "bfdfe7dc352907fc980b868725387e98dc6e7130ff8d82d481b6f55e3fde62ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e3969170de24cf80f73c5fadc770375", "guid": "bfdfe7dc352907fc980b868725387e980da8a456d2870e33cf4d358835ac09c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c645d9f830e220a95c3fdbd6b0ac83b2", "guid": "bfdfe7dc352907fc980b868725387e98cd04fc32e6618a490d2d43bf5a289f84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c9c5536367ee6176c6c6f0d8a29202c", "guid": "bfdfe7dc352907fc980b868725387e98a36a1f627137b0c14303f4a3210a11cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bedefef122b1df986ed29666c59b5524", "guid": "bfdfe7dc352907fc980b868725387e985f403ecc667638b6197167b75842edc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db08cd390cf59df54bf1d5bba1253013", "guid": "bfdfe7dc352907fc980b868725387e98625c5f37c66f43a1af261ed100c67559"}], "guid": "bfdfe7dc352907fc980b868725387e984237bb9dcdb792eb8a299ba4041b44b4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98481eb9b92705cdccdf24b944c940da48"}], "guid": "bfdfe7dc352907fc980b868725387e9813d4b8cde2bc4ca43fd7c4e94ee5c8ed", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e405d0add31d47b9b85f64dab2344686", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}