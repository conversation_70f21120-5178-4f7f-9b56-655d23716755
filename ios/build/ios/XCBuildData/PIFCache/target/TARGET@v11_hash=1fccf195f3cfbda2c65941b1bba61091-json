{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986fe280a4dd5e05ff8710952bae50cdfc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98befcdf6a0e188422590432219809fbe7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bcc03320811b8960d1e3e5679997b47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98057ed3b36a78fa62aaa78a0003c599ca", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bcc03320811b8960d1e3e5679997b47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98347af98b0d9c85612707ab496411cb2d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847a646dd1e8883c0def104b524028264", "guid": "bfdfe7dc352907fc980b868725387e9882c0936ea185410454a2bfc5920e5cb6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985a77192347722d47b9eec3e21ea3d5cb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a894dd8c203dbfdd39a34303cacd7368", "guid": "bfdfe7dc352907fc980b868725387e98ff35e91b55bba9cd684512a81a546f13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813801daa3805f1c5ff3830b6dcb82361", "guid": "bfdfe7dc352907fc980b868725387e987dcb297c4be617a7a99380285dc21977"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a00b83c4a8ee30ebb172a5d74bed2f", "guid": "bfdfe7dc352907fc980b868725387e98703ac5d3fa49bffe805e17d782a77959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b56f16ecd5b62235b6bb21a1058d9ee", "guid": "bfdfe7dc352907fc980b868725387e98e011ee1db0e2e2dbfc305fa97ddc493b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a11bde28d41001bf819eb7612d970123", "guid": "bfdfe7dc352907fc980b868725387e98a683af432960bde4096fd0ba7e8bf2f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f783fc72cb9cddec7d0754c7f136a0", "guid": "bfdfe7dc352907fc980b868725387e983f5b36feed5a68dcb82d8da0ce7ef7df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827388ce376aeaff9a7fc696da3658983", "guid": "bfdfe7dc352907fc980b868725387e98bf9488c08f4db5a6aeaa535d5d725696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ca9eddd9091cad14b47f8323af1b14", "guid": "bfdfe7dc352907fc980b868725387e98fa8105313e38e288dd0ebbbfb7416c9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983911421a961862c8ce1af894a03cbb0f", "guid": "bfdfe7dc352907fc980b868725387e981bad8d54c9e3b96e88847d6279dbad5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b03f9c84a04e0ddc36f7f1f057a5a8b", "guid": "bfdfe7dc352907fc980b868725387e981dafb21c36cfa8bf9c979ec04d957f02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983766a54e2d4fe2ac545ca04cf62f98ac", "guid": "bfdfe7dc352907fc980b868725387e98f5d114ee5949e66350e3b12a4e8503b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e5c67cd686f6db2bf84b743a2197c66", "guid": "bfdfe7dc352907fc980b868725387e98ab775481519a1e5dc398a94ed2b84afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d229d7a8a00bee95cbde487b32653938", "guid": "bfdfe7dc352907fc980b868725387e98d75a957c129ff82a397d71cee114c090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f17b0d3221f90780d199b50045018611", "guid": "bfdfe7dc352907fc980b868725387e98dbf4a697bf1cd66c0e70f6171ce8c702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26312bc0e471291beee63f7dc8b6662", "guid": "bfdfe7dc352907fc980b868725387e989f102208583136bc2ca747b737f82161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd533bdd6afc87422b99e38b34fdd2df", "guid": "bfdfe7dc352907fc980b868725387e98da6e5b6201136a1a4a54f85b01e51858"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876bf0b129f2e2f9ef03a3c67a84f726a", "guid": "bfdfe7dc352907fc980b868725387e98bf5d25c27a6b22cc2a18b58f0ed5a677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981efe743c28bf6446d89bd76969532aa9", "guid": "bfdfe7dc352907fc980b868725387e98aaf5f57448ab40ecf305d08986832950"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed6370de002a73c207f54c00e06115ba", "guid": "bfdfe7dc352907fc980b868725387e9893f7c8747c5dc322876a97c7a61d304a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb58fabee0f6e303c6dc7b09b6e8870", "guid": "bfdfe7dc352907fc980b868725387e987fbe8d9de2f82076bfbafe89099f470d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ebef88618fa3f1c25fbb3b07310627c", "guid": "bfdfe7dc352907fc980b868725387e983414c5d303421ecb23b56bb5609980f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c9300e551f56d7daa01eaad8c6d1996", "guid": "bfdfe7dc352907fc980b868725387e98920d051b7d066e2f47ff4dc4780c1990"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0c1541aa3d3a2aa883f50527cfa4dd9", "guid": "bfdfe7dc352907fc980b868725387e98d4f65784cb1be762168a97519d9126a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f716df88eeab8a547ad76a391125c32", "guid": "bfdfe7dc352907fc980b868725387e985dafb60e7b3977fdc2d7453a83b45efb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980345dcf3ffb0253bfc968cb761e07608", "guid": "bfdfe7dc352907fc980b868725387e9893462a033bfc934dde1402539d7cce49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804bda9395e725b9b8494466da60ce427", "guid": "bfdfe7dc352907fc980b868725387e983e7136c63ea8c6d1de0e25ab4ecd8e0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806066676f658d17ed96f9db91ea3cf27", "guid": "bfdfe7dc352907fc980b868725387e9818dec49185a5025c48ba5cd6a7195d0a"}], "guid": "bfdfe7dc352907fc980b868725387e983eb1b84d0767efa48622c4224c31e20a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b3660ea04c3363bb1efd95923762b7a5", "guid": "bfdfe7dc352907fc980b868725387e98fe67d2b22d4284b2720ea138518a5bc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897bfbf3410c779f9c859ddeee5e83668", "guid": "bfdfe7dc352907fc980b868725387e9872db555278251183e531586524c0ad65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e9891bf3f8550311b2dab87eae08993c56a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a67f8030f2d17138a1c1d4f723d2f69", "guid": "bfdfe7dc352907fc980b868725387e98d373352c2cf599241db8a030f84d7751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e98ed005df116afe05001bbb6ec528d8ca5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503ad53dcdb8bc9902dcc103f84d66ae", "guid": "bfdfe7dc352907fc980b868725387e9897fb21d2dff53f953b1b10fa2f447633"}], "guid": "bfdfe7dc352907fc980b868725387e983bdf0738ed7001488e3dedd9184083f6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984fd0f80130b8b3ecde2c05b9b3acad63", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "StripeApplePay.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}