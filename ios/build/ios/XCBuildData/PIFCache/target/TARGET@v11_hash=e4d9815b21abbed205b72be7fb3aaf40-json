{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983779570fafaaaa2554fb73b8b8a224fb", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da142b0aaaebdfb32102d4aa93f64074", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866046b25d593b7a860d0176237bb0a3f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0a34356d20545c3a9035abab3b43fa3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866046b25d593b7a860d0176237bb0a3f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9857b30c0f138b1bc2bb4ae870f186d2df", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987f8303d876e740bc02b10f8d3d064f6e", "guid": "bfdfe7dc352907fc980b868725387e98dd357bae2702bbc61a439697d7b50ed4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797648db15a3c758096795e43b42e9a6", "guid": "bfdfe7dc352907fc980b868725387e98aedfd6e0702df39b7f9dbccbed7c45be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858cd4556e3aea5f15a30f20249b681b4", "guid": "bfdfe7dc352907fc980b868725387e9826a9fce9f3ff5e0d40939b638ec72a89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44f2929324dc746890adab79c9e0e72", "guid": "bfdfe7dc352907fc980b868725387e980cc19846964ec362a598caea78f00923", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e49aae80445880bbac685b340c2e11", "guid": "bfdfe7dc352907fc980b868725387e987a451e8b4653c6b8355c50eb22db2cff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b635a81db34ddf9266d2ba34748a739e", "guid": "bfdfe7dc352907fc980b868725387e988335814b96398cf555cd7803320d0448", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891c09c87972c1345c638b89ee6197ecf", "guid": "bfdfe7dc352907fc980b868725387e98f629f9b16ef0353004204f45d9bc8471", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ea2498ced0994ebaa93bbc3a42afed3", "guid": "bfdfe7dc352907fc980b868725387e981084db4e62b707926ba31635d755233c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5cd053579018aa5a46b9ee9a00106dd", "guid": "bfdfe7dc352907fc980b868725387e988b7c198dfed0f84d122ee8418b35b1b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef7f40b91f724730cc937324e084a79", "guid": "bfdfe7dc352907fc980b868725387e987f2d48a2d84959ad7259a3feaaefccd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b0d762ee31d9aacfd2a6f7d4bb30a76", "guid": "bfdfe7dc352907fc980b868725387e98b572b43c6e7e32a3acc491ee39f671b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d50202dab8f889e10f760ad7095ba8e1", "guid": "bfdfe7dc352907fc980b868725387e9876442d7dd507cc54c1d092c53aa497b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b1e5f660b95f9b8258e97fb547db90", "guid": "bfdfe7dc352907fc980b868725387e987f07cf13d7a66ad226713518f76d364a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee9e5ae514ce86e5cb40b4136190e09", "guid": "bfdfe7dc352907fc980b868725387e981910694a6a9ade11aba187e43768ecaa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961bf2bff9cc91d5d23a4578f2f12170", "guid": "bfdfe7dc352907fc980b868725387e981d724e6f198508be5131796f63cc3a8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33e4180bd9c5e6a3b787ae527ae2a36", "guid": "bfdfe7dc352907fc980b868725387e984e483d24e1a32eb6ac8e384299fa4728", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ea28bcad239c01032107de02b9b418", "guid": "bfdfe7dc352907fc980b868725387e983c4c99c00774816588a32be60353f410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffbcbdb1c8802b97ef3583d3bed6c759", "guid": "bfdfe7dc352907fc980b868725387e98d8dc9928b95f4e17962f02230f7d3807"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5b4cda4a7b2d6f8b45e188356178a06", "guid": "bfdfe7dc352907fc980b868725387e98178576cac80da910e44e8244e979293a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981032ea36d2223c78fa4959f5d614c2df", "guid": "bfdfe7dc352907fc980b868725387e9868ad34a53c6ac99752cedd53c7a0b044", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875969b194053e4b525a0e814c4e61107", "guid": "bfdfe7dc352907fc980b868725387e989c09a9bbc6869dda9cc0b97e5c0152a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa43d6a6a763d651a297232f0913851f", "guid": "bfdfe7dc352907fc980b868725387e982339e79cd198442ead4e7444140c6bd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d137760eda0a90ad484346ea848ad7", "guid": "bfdfe7dc352907fc980b868725387e98d14f52c6ec8830eded50e4c4892b7918", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811fb5a4d29cb22233e0243743247ca28", "guid": "bfdfe7dc352907fc980b868725387e98a315fa28e5f4d9cf9f653024b1c67ad8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854945f1fd74d3bdcad39eb621c78e358", "guid": "bfdfe7dc352907fc980b868725387e989266f155f627654e4eb364e009c99e4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f540999bce4d72c97ecd69ec686e69", "guid": "bfdfe7dc352907fc980b868725387e98cdeed2d9b14856a0e09ca2a895f06e11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839780faea07631f6e99aec1072a27fb1", "guid": "bfdfe7dc352907fc980b868725387e9801d30f1db06a1620b8f36a5de4795219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053400079b345c1373147c3bd8494bbb", "guid": "bfdfe7dc352907fc980b868725387e989309edce2d188dc70c1d15bbfb421099", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a43a6ff22a45bec5ffcecf083b30c4", "guid": "bfdfe7dc352907fc980b868725387e98050f01cda1290566162bd1e87f8a5a6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985148834296788f8f8dd4a43f1adcbce2", "guid": "bfdfe7dc352907fc980b868725387e9829ded21f380d096ae35ec69cdac18e0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a9a29c82f100cdb14f6e47b3e5e892", "guid": "bfdfe7dc352907fc980b868725387e98462b7a25d8c8107ff20c0c9fa8948d36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b5264ef793250aaaf81e6cab3ce4e0", "guid": "bfdfe7dc352907fc980b868725387e983dc2340aa50a4f31d081af73a3a982b0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ecffc91f775f14b70ff52bfc0c2cddbc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e240337faa9e4c0ad65a26bd5f135c7", "guid": "bfdfe7dc352907fc980b868725387e98dc3bb52ad3fc37d3010fdfa8c691fad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d086947ea951bd309c04a0c60de5c6a", "guid": "bfdfe7dc352907fc980b868725387e9828ac686af9747bc8b73c098d51350d1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9615f5b6aa5bb141f615704b1f7d8ea", "guid": "bfdfe7dc352907fc980b868725387e98d666cdfa866e3960ba7f2d01a2cb1859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf861553656763927c0dadcb75839e33", "guid": "bfdfe7dc352907fc980b868725387e9862388c8d892c9cb12811365c938107c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987635f487198a6435d5b11d3ee382d7d4", "guid": "bfdfe7dc352907fc980b868725387e9810b133e6bd0768f6e6264903ff1ce557"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98078f02df468b9d13aa75604e6852df9d", "guid": "bfdfe7dc352907fc980b868725387e98db682a0861ffb2bdd6e2fa25fe44472f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981777b85f5ee80e6e8d306687c23106da", "guid": "bfdfe7dc352907fc980b868725387e98465e29f671346926263be6bb59b12a7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4ea4a3766e946a12be490309eabfda5", "guid": "bfdfe7dc352907fc980b868725387e98032b8930a25dea702373d1603bf5a4a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d3b170bb702304215668894868eee2a", "guid": "bfdfe7dc352907fc980b868725387e9865a0233292ebef7984ce0413483cf32d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989291b03b88d182e79b808f0a1b1b4c16", "guid": "bfdfe7dc352907fc980b868725387e9827480c7fb1e7d8cb5898da150f1c7f8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840dab3b20601af29fc8bb56db0f4ec18", "guid": "bfdfe7dc352907fc980b868725387e986c68c660f48cd89693a3b2069b238396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f4b7d15535886f16d0e14e5772a4712", "guid": "bfdfe7dc352907fc980b868725387e987772e16033dbea976f3b1b5deede12a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ed101d65262f8e3de07bd5f9a4ed14", "guid": "bfdfe7dc352907fc980b868725387e98917133a6d30829ce0dbf375156f19cd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986778f95ab3dbdb84666a5acf10f38230", "guid": "bfdfe7dc352907fc980b868725387e98daaac8c23d26ee20054a49a16ca200ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985355a04daa3d080c0ee17d4ab2594056", "guid": "bfdfe7dc352907fc980b868725387e98093b397639dc78f6e05406bb28a8d848"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98557a8a374c6ac92538badc6008fac93a", "guid": "bfdfe7dc352907fc980b868725387e9829314842a75184fd405c3083902b288b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c6bff5735fc769cd94bc6baba3160fb", "guid": "bfdfe7dc352907fc980b868725387e980fc2f53b6844ba24cf842c531b3e7f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836d9c166cc55fa241ef8800af0a8df65", "guid": "bfdfe7dc352907fc980b868725387e9825df8acd25dada47e3b85211d1cf55ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1208e226a88d9f7ab289dff68a773e", "guid": "bfdfe7dc352907fc980b868725387e98a6d73f7a85e95e0190efce1d2d6962fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b799d110403efe4f2b435ea692be9fd9", "guid": "bfdfe7dc352907fc980b868725387e982b7e6ba6cd3aa2e92313d0607dffd388"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf7e46449a19271fadd993493b0d984", "guid": "bfdfe7dc352907fc980b868725387e9808dfbac8977b520eb7bf239efd125f69"}], "guid": "bfdfe7dc352907fc980b868725387e980b157e106108486d83fd2223b617ee8d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98f170e2d076a69b994fca2d2b6450fc78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e9833b2b73093810f8e80733798196718d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e736fe3cd34dacbc42b6af920b1000bd", "guid": "bfdfe7dc352907fc980b868725387e98506d60f84b5ef0024774b920dbc1ac94"}], "guid": "bfdfe7dc352907fc980b868725387e9883974ffc05cf0b9693d326135a5adab9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9824f4c409d2ef8f95bc1e26708dc83d7d", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98264974616763b0ceb48403ec0831e111", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}