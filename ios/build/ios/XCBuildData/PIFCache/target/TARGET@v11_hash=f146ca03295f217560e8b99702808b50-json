{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868985ed7fac9bfe9d371070dc34c8c8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98701f1e15d295614f752087abbb2156d0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876864a8d2ed4320ade90c7ec2b0aec74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988b519a1ad1115fedd4b4bd142bfc07be", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876864a8d2ed4320ade90c7ec2b0aec74", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db2530ffa25418f6fb91af14aabc59f6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98237e051fbf1ef0ab3b680141f56f2988", "guid": "bfdfe7dc352907fc980b868725387e98388ea8c2532944a359146462fb76eeae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b63c0cdbfa3fde7028ec29667bef5ff4", "guid": "bfdfe7dc352907fc980b868725387e98ce6e47d9e2ab8c3a18f14a26a58883bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbff24adfcf67c5a222e5f196657d0d0", "guid": "bfdfe7dc352907fc980b868725387e9839b64fecc8d3cf695ca8884031c9f5de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9a4e80ceecf0456099d498a39215b0d", "guid": "bfdfe7dc352907fc980b868725387e98049e8f69edde3b96c34b0d200a92d3b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2492cae2030dd1d837d8050e077b25", "guid": "bfdfe7dc352907fc980b868725387e98b66117c2d561d9d6ff52abe9df081a1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d2d471d0d1fa536f28ad122eeac250d", "guid": "bfdfe7dc352907fc980b868725387e98cb0b119ddb71bde36f489208c4050931"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f736c6fe24a04f346955dcbc26843100", "guid": "bfdfe7dc352907fc980b868725387e98caa71507f06dcbc6b1daa67220c5e297"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980449d368c6e0c63edfd3b506b9c8bc32", "guid": "bfdfe7dc352907fc980b868725387e984776216a62c35605765ad0021ca6d918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe05c6aab6716a9f4953e8f31221b843", "guid": "bfdfe7dc352907fc980b868725387e98f619ac64820663fc4705e9837de0f244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ee50a5f06d5370e977534660bd7bfe4", "guid": "bfdfe7dc352907fc980b868725387e98d82f143ed0405a029c7c0ba16c4a81db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e54fc7ff1b86101f5fcfdcae498eb39", "guid": "bfdfe7dc352907fc980b868725387e9837d6f594b70039d0e14971e28f348c6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e5db41ae62400af37cbf9d70fc4dac", "guid": "bfdfe7dc352907fc980b868725387e98e64ee182abc3d00986a0f41b0a7d7efb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab687c5e240b0c754070d7d0c79fcc3c", "guid": "bfdfe7dc352907fc980b868725387e9882887d241050f61552102fb8923b6c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833db6874521949ef1e7881a1f682bfa4", "guid": "bfdfe7dc352907fc980b868725387e9832549d954307e886e502805b941d6235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845aae97f5a936ef7d3423b799355f233", "guid": "bfdfe7dc352907fc980b868725387e98b8ed424dbfa7486d74c016b55b6a3c4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2d54a700117729f0c3612948bd3497", "guid": "bfdfe7dc352907fc980b868725387e9855798e6893e69b780b945216c4431cbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1a310a038ce74b02286e9e3f08d3abe", "guid": "bfdfe7dc352907fc980b868725387e98f1647ae171d4bf9eb47b1065a11ef1b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c074d871e8e62ae1f6ea8a0634a10c1d", "guid": "bfdfe7dc352907fc980b868725387e980994816d01ca8e5ec55579d61910c9b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dffdef8922e86d2d04d330a4175adf3", "guid": "bfdfe7dc352907fc980b868725387e98572d1a51ac6a96ad978be43cdabc3cbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984efd508502e6a7025e8a408e563f158a", "guid": "bfdfe7dc352907fc980b868725387e9804d6071b668fe2001dcc35bbfed6d65f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bff121011c78980594cb0eb7fab7ae1", "guid": "bfdfe7dc352907fc980b868725387e987364cbf61116ec1a67a6bc69721d635d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812244d6b1d40fd1e698445d1814a0b29", "guid": "bfdfe7dc352907fc980b868725387e981574d9ff684199862609acbeeba59695"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98492767e164e8e4e3594445eab121c27b", "guid": "bfdfe7dc352907fc980b868725387e98272bef30358dc09d8607290665fa48cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da08657d5065c7189d66ce280bfaf191", "guid": "bfdfe7dc352907fc980b868725387e9841fd968f46f39362a616ed3a111a208c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38c5a009ed0e15e49d3332f8b2736d2", "guid": "bfdfe7dc352907fc980b868725387e98752641598b7dcfeeddc1e04adcf36e74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0c0fb1259ff6b74813f05c478d500e", "guid": "bfdfe7dc352907fc980b868725387e98f9e40bf228150520ed0275ca6b770a31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898352d7685f7cbd9f6db69a7a7213b75", "guid": "bfdfe7dc352907fc980b868725387e989d36683e327b7284a19b8dbed4ce9799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe15d8a1d104903b0acaf58d9ffdf916", "guid": "bfdfe7dc352907fc980b868725387e9816d757a86aefc066b48a47ddc3a17aa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984402597fd01da530ebffceac747643c7", "guid": "bfdfe7dc352907fc980b868725387e985f4b4260d72ae8fd427daf2044273701"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980865c493d2cf4bcb213dd2299378b9f2", "guid": "bfdfe7dc352907fc980b868725387e98bda60f3398d2e8801a959479f2797a5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981826827a3aba34ffb9d453d1d3483dde", "guid": "bfdfe7dc352907fc980b868725387e983d1471a38e058ea3733610c24750b449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8531eb1d99362f5a3c04bc4475f48c", "guid": "bfdfe7dc352907fc980b868725387e98388d9df3f9606b617f0861d611d817f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ecae398993841f7e13ca07832effbb", "guid": "bfdfe7dc352907fc980b868725387e9814754acc2dbdc17d52f968e89a880106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d9f61dfb093a774cad3bcc44ddd7a6", "guid": "bfdfe7dc352907fc980b868725387e98e2a162abff79c5cf3800ac36d4d5ca20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2429e5b47da1d0c1ec8bf347ae5a054", "guid": "bfdfe7dc352907fc980b868725387e985654c2f19944b9bc33c972c44625596d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1c766c1864fd07432a87a2653c9a381", "guid": "bfdfe7dc352907fc980b868725387e98c823a222f74cc6459fce569589e046b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dc73e5de776984200084289cf80ba6e", "guid": "bfdfe7dc352907fc980b868725387e98bc50936f911a47d43ac6afac40e5ded3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a26852ef0534b6e7197729c83b00f4a8", "guid": "bfdfe7dc352907fc980b868725387e986f986d4c3d53e9b82889296b99d9ec7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d840ce4aeeeb873ad4c5dbfb4498019", "guid": "bfdfe7dc352907fc980b868725387e98cdcc27e91d5c52ec9cb163b41ac1456d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811546a65ae41d4395b7210d180f3b7b1", "guid": "bfdfe7dc352907fc980b868725387e98fa9adc96a260a70a90a120d6cd7f4e5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98590c56bcf9d57ee74ea8e90372624f0c", "guid": "bfdfe7dc352907fc980b868725387e98cad27faa09f85a792df6e980cfd58a11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8b6000f0afe423c109b90eb1882d45", "guid": "bfdfe7dc352907fc980b868725387e9854320cff43aa0b1307dc4faea2923e9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f431d17b99576827c92f7792acc63139", "guid": "bfdfe7dc352907fc980b868725387e98d26aaf28c8abe5c12c91ccc9608e776d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98138ef25a273891b998531c82266863ea", "guid": "bfdfe7dc352907fc980b868725387e981af35db494d5c31c33a28bc9cd317302"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988801d17562b3c10cb60f17a2d6ff2515", "guid": "bfdfe7dc352907fc980b868725387e98c5f6380c2b1c7ba8126b4982e5a17dc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984190c73667a9f08c629803d7da1f91e4", "guid": "bfdfe7dc352907fc980b868725387e98cc027792c58782e7c1cbc0687489b840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b843ab1293d37be947be74b1548911", "guid": "bfdfe7dc352907fc980b868725387e98181aea22fa6898126b9a243f705381f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a34649a867c1c8b7a2a490257a04cce", "guid": "bfdfe7dc352907fc980b868725387e98c97d40f930079e69af734afde23a203b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e93f43cece25e94fe37142ccfffe5881", "guid": "bfdfe7dc352907fc980b868725387e9883ea42803df17a70f6add468e1316975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e98b91e9709b7a92da336af2d55cac14", "guid": "bfdfe7dc352907fc980b868725387e98abbe60cb0dc1b7301a56df4312803e19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981595ab6560b91ecbdf443dafa37d1e16", "guid": "bfdfe7dc352907fc980b868725387e986980fca4e234e02c8373f42e0a6f8240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b776447eef7b80a38ed65e0f7dd1a41", "guid": "bfdfe7dc352907fc980b868725387e986d82f1e2621cb02703f4c9fd9efcd405"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875abb7ce4e45b692c1f97f165925ca7a", "guid": "bfdfe7dc352907fc980b868725387e985071e389c4cadc7abeaf64e6a83e88bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a9474d7e7d59a231cb554cce087f9e5", "guid": "bfdfe7dc352907fc980b868725387e980f97b5760977304e2890e7b966140168"}], "guid": "bfdfe7dc352907fc980b868725387e98c626d0dcb24842acab1dda0831b645f6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98db261f9963fb907707d7ef884ab471ff", "guid": "bfdfe7dc352907fc980b868725387e981b1c5066192063cfc6a36ed8b3207db0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983959fe369bdea0d2ff931a2ace0f37b4", "guid": "bfdfe7dc352907fc980b868725387e98f2ad1a7272c46b346ef84337c50b3d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd82f1f5ca79512e5142ff26302548c7", "guid": "bfdfe7dc352907fc980b868725387e98598fb876fd3985bdcb3dee212ee14afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1719299afe91e2ea1d288374db76ada", "guid": "bfdfe7dc352907fc980b868725387e98830a5ba6652677fa22bc982bdf63d241"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8b9d0e272c18716ad28ce10f6e8976b", "guid": "bfdfe7dc352907fc980b868725387e98060a800b9e7b4e849a604636ae7aa415"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f5ac4b06c0ef8f2efb2c3745779f21b", "guid": "bfdfe7dc352907fc980b868725387e98b33341d43f78b21c8d1cb5230b49870f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bfd12c62f2e63e538cf15a5e66f3295", "guid": "bfdfe7dc352907fc980b868725387e9862035bc1cb418f56006dc5a3db939435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4919bd6613bf977a33dc9f5d13f6258", "guid": "bfdfe7dc352907fc980b868725387e98d1068aafeb4c2f0982567163ae0f20b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98118fc3c2e95c8a1a3b318f05ff84ee9b", "guid": "bfdfe7dc352907fc980b868725387e983eb7c8e747c4c53dc0c3948553ad222f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4be0b428d2aaab2d77b4e2ec26f806", "guid": "bfdfe7dc352907fc980b868725387e983962d727ccd441fcd3fbbbf09de1de89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838bf0992496c04f9018f1195226e5388", "guid": "bfdfe7dc352907fc980b868725387e986b06b5c1d95a2c1e0f565b2e77f538cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef3bad25e2221e395a740a845acd5984", "guid": "bfdfe7dc352907fc980b868725387e98e0cc2c9422c5906a41d2c84031d41a4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988112a4180eee33f423eddf8a8ff0a96c", "guid": "bfdfe7dc352907fc980b868725387e98d99a080460567fb2ec93646d7c9baad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898567c1a65943a6787c9302f216be887", "guid": "bfdfe7dc352907fc980b868725387e987575acd112ea61640e38c967f25c75ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c1a2004a2143b0cddb2c43c6acacf03", "guid": "bfdfe7dc352907fc980b868725387e985c666a41c9f1a87bf206867818bb281b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb8a06b12434cfcb65afa4f669a3c88f", "guid": "bfdfe7dc352907fc980b868725387e98f29d5d7c83bb67e7183236a1ef0f6014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810fd4e182f1887632d82738bb80169bc", "guid": "bfdfe7dc352907fc980b868725387e98a3e81f9e11960e151a40e06e02dce294"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d3470d32db9223d254dbdfe93a4da17", "guid": "bfdfe7dc352907fc980b868725387e989639481fc43ad8169e07d37dc40a66c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bb73fd29a0fa344b6258f7ba5c0e083", "guid": "bfdfe7dc352907fc980b868725387e98a9e76a7c047a7bb4957353b4f84d3c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e50d8cdaf16b086b74f003c9a12fada5", "guid": "bfdfe7dc352907fc980b868725387e985805bf091fdf45ee598488ff33608112"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f30d16508b02a84ba27ee0b938e0e5", "guid": "bfdfe7dc352907fc980b868725387e983e2880398863067839475c4b4ecf561a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801a7d2dc7604e86830adc45c725fe0bb", "guid": "bfdfe7dc352907fc980b868725387e986a70047922e1ac459ac4ec57386b5d86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d55a25c0fb332355a0d1862bbb07da5", "guid": "bfdfe7dc352907fc980b868725387e9884f3d49788b9949e28a174f0e6ecac31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afd757bc0113d76e26ecf77505745355", "guid": "bfdfe7dc352907fc980b868725387e987760cc46ae29b8d0c57a9bc202deaeb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5b5adefd24f7aab26702f91ff81028", "guid": "bfdfe7dc352907fc980b868725387e98d6209cac5f386cda16398f4a258bf99e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb4b0bd809e83472370cd0576625ee49", "guid": "bfdfe7dc352907fc980b868725387e9831b1bfff24f0f3cbd5031c6002ac7b7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883686edccc4c368993911087fa7960ba", "guid": "bfdfe7dc352907fc980b868725387e985c24d651faaba806818553cdb8811571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983191bbcdac38d20148e8c51b4e0adf59", "guid": "bfdfe7dc352907fc980b868725387e98e6dea383a710e5de76964b29c2fd07fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d922d7cc310c3ba4f8757cf525038160", "guid": "bfdfe7dc352907fc980b868725387e98a9c5c0ea97c4620a30ef1ee9ad0a7a45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509df1bc67b08aa119245a55d2f512bc", "guid": "bfdfe7dc352907fc980b868725387e98b48d97a94c1784b48408e62fc056fe0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989284f54975abc9c75cffa0156f6c37c7", "guid": "bfdfe7dc352907fc980b868725387e98e15cdffc654a68c8ac34e71df16a9692"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986246aac77bdfd6d222383bdc72f74262", "guid": "bfdfe7dc352907fc980b868725387e98d9013df434ca1c48fbdded0e053c5652"}], "guid": "bfdfe7dc352907fc980b868725387e982e6a1323fa6e1c1791386aaeb9eb8373", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98f473f3c18317a3ab068960ee6d43d936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e736fe3cd34dacbc42b6af920b1000bd", "guid": "bfdfe7dc352907fc980b868725387e982800f855f40e2d660fda14b26c9bc5f3"}], "guid": "bfdfe7dc352907fc980b868725387e982de282948ca60b154c86c266892bf1d8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9809d5cf5cdb83db7c0cb575e69c438cd9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}