{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9814f65698f703e662ffcf7e1567a3e2e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b3d41775dfc3344de05fdc6b05259b2d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9725d2d8c0d0342186c75bacf2e78e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f9a5b9907b23f75f8e777b66c30a5c5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9725d2d8c0d0342186c75bacf2e78e9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98be3e7761b460ca93d9062ad4ad5acd62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c5a0afa96d81352853e2d65d6bac7da", "guid": "bfdfe7dc352907fc980b868725387e98b1efb940428355c05b10b855b536f0da", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ae20b087a550f7368b67882e4baaa596", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a4c44ef9aa2a25ad8eaf0968b6b1f84e", "guid": "bfdfe7dc352907fc980b868725387e98da7c452b050b2fae2c9e43ebb93abc1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8974f93a6365f6fd0af7db34d6b3b7e", "guid": "bfdfe7dc352907fc980b868725387e98447c47838acd104d6924051b282dd7fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878215102d89d25781cad60d7e146e448", "guid": "bfdfe7dc352907fc980b868725387e9872a8e08db85385bd10102955bc13c43a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d38dd5768b5cbd92de3df779755a5b", "guid": "bfdfe7dc352907fc980b868725387e984b5e66d9f50756993fb3818701affba8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc9f7227bb5a6ec5d11b099b3373fd71", "guid": "bfdfe7dc352907fc980b868725387e98d93f55e277fc65fb21fea525d9d2a677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cf65653a039b997342f76666155b94b", "guid": "bfdfe7dc352907fc980b868725387e981d19f44b5f4932aa3f00dd8ad91c3b1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f26587b107fac0ebfdc40ece906217e", "guid": "bfdfe7dc352907fc980b868725387e983dbec19200da2fe7d2c2ea3a3336407e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985011522d9698f563270ec96d67980cb0", "guid": "bfdfe7dc352907fc980b868725387e984439d6a6e948924566605912ceea499d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c76627858b76d0e0689882bf2b82771", "guid": "bfdfe7dc352907fc980b868725387e987c7ae5246840ed3499131d0b4eedd143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a9c83128f087ed73282cee708aab62", "guid": "bfdfe7dc352907fc980b868725387e98de59806ccc2a0b3cd523f11bebd1db92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab1e69012fd35140e7ca67c6f43346c5", "guid": "bfdfe7dc352907fc980b868725387e98b314f7224d79eca7d9fc5cef1237bdb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdf4f4fa4beff68fd1b80a55dc3043d0", "guid": "bfdfe7dc352907fc980b868725387e98494b54a3b055145f31d71112f3bdd106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873c4a5647aecbb1812a30c2de62d9875", "guid": "bfdfe7dc352907fc980b868725387e98dc8f6145760db41a3f8da28384b6fbb4"}], "guid": "bfdfe7dc352907fc980b868725387e981786841036b1434245b78ea3ef4d6689", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98570dcb02f338899e7789ab32269c0add"}], "guid": "bfdfe7dc352907fc980b868725387e98487cc48198678193fa9e8f7427533396", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983bdae24a594a0459df0cef86b9daa9bf", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98e692f1828c65cef74cb5927988052a08", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}