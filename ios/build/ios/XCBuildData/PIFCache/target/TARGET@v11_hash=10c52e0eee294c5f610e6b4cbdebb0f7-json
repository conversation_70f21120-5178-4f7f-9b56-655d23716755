{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860a622f7807fce8f920ee972fd5c1beb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeUICore/StripeUICore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeUICore/StripeUICore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeUICore/StripeUICore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeUICore", "PRODUCT_NAME": "StripeUICore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f7411a72a72687d077bc370d73ca3400", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c540b820b3dfcb6763f693205528754", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeUICore/StripeUICore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeUICore/StripeUICore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeUICore/StripeUICore.modulemap", "PRODUCT_MODULE_NAME": "StripeUICore", "PRODUCT_NAME": "StripeUICore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9855c68e55dacc0bd7eb3f08aa950d5e44", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c540b820b3dfcb6763f693205528754", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeUICore/StripeUICore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeUICore/StripeUICore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeUICore/StripeUICore.modulemap", "PRODUCT_MODULE_NAME": "StripeUICore", "PRODUCT_NAME": "StripeUICore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fcfa1afc233f159a8a1977d4f3d67a66", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b06db66b6acb7bb16962a41f8479827", "guid": "bfdfe7dc352907fc980b868725387e98b10a7e7ce418bf640f76963bba2286d8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98dab548ae725998bd58ef63347e6dfe38", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9882541311ff7495a29a27d8e1500076f3", "guid": "bfdfe7dc352907fc980b868725387e98e366b8158e294343cf086797e2269105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8dbbf55e4fb5e32bbf1a27cc5f3ad67", "guid": "bfdfe7dc352907fc980b868725387e98e7dbcb4a7c432d3bb7a0c68e6888a5d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881263c72cebf19d408d695e859888896", "guid": "bfdfe7dc352907fc980b868725387e9835213d80cd8b90c310763d89081bd3fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c4f0b90455a62e65f839d68288e291", "guid": "bfdfe7dc352907fc980b868725387e98acf24cbb4dc096f87a39c891fe788a09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ef6a35fc09336783af36448f275381d", "guid": "bfdfe7dc352907fc980b868725387e98c97aac40b6411a21f820b28dbfb85f29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98297fa0a00d0c3f0dd6992f0b8c4bac9e", "guid": "bfdfe7dc352907fc980b868725387e98d6c367dbc76c1f9b6dee741d09c8b74a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d530ea09883336754cb48d263a80bcda", "guid": "bfdfe7dc352907fc980b868725387e98be03755b1c6fd35db3ca61203bb79297"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98592c85704958120001a3f14db135d568", "guid": "bfdfe7dc352907fc980b868725387e9855281e6f1018385a4b54cb5ac775aeed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f7490f30139ee6e0d50f57121923abd", "guid": "bfdfe7dc352907fc980b868725387e984223404b9eb75733ccbe3ff03d5f4935"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459c829700fa9c4c6f997b2cebe5de86", "guid": "bfdfe7dc352907fc980b868725387e98a3fab2ec9dd4e49c6ef33aa80dafedd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bba1605f311182ed8dc09df2f6637e9b", "guid": "bfdfe7dc352907fc980b868725387e9892d7ca8c9e59e5d160beeec0a5e3a885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987475ff34afa0bc7e409d37cf4ff03ef0", "guid": "bfdfe7dc352907fc980b868725387e98733b69f7d7ccd71249f759b59cd30def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf4e9a42f14494b07c6d2e8551b926a9", "guid": "bfdfe7dc352907fc980b868725387e98ae0569e7340cfb1b2549c8ef156114e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee8bc0ed593ed17d94edac1ba72463aa", "guid": "bfdfe7dc352907fc980b868725387e98a14ca3d345b5a2d27aafb6185b94fc6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e1a7b681e4ea719283d656b6bf7425", "guid": "bfdfe7dc352907fc980b868725387e98c6be702444773251992c3f63826fae32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e00b9d3b85ff998aca8ec7b46cf6b0da", "guid": "bfdfe7dc352907fc980b868725387e98f1f28e55f312bd3eff233da465dc891d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e9ecc9a7984d323060842e1705b38d", "guid": "bfdfe7dc352907fc980b868725387e98eb8d3b7dd0eb6e0db182587b06a87161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878bc59cbb858202cc834cfe3f4bfc572", "guid": "bfdfe7dc352907fc980b868725387e98e37e237666a794ee119ea0eb41b2d1f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c162698f84fcedbeab49e597dc83d9", "guid": "bfdfe7dc352907fc980b868725387e9822f296a4127e4ef6559ae9899079858a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e32eac8bfd5e382bf9a9b1fa343a2c", "guid": "bfdfe7dc352907fc980b868725387e98b277278a87dde9d1abd3696a21c8fc48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f3b71174cbd2ee24738d3284d23e269", "guid": "bfdfe7dc352907fc980b868725387e98414fbd96f3f58e5fe84fdc785ebe8599"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894e1611721375a47ac6970631c17af3c", "guid": "bfdfe7dc352907fc980b868725387e9847ccfd5b02b58cffe5805a1261cb4f9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc360c13656eca79aea03dc5faf9304e", "guid": "bfdfe7dc352907fc980b868725387e98f9e171cb80e082eda2604d1995bd2a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e19ce5e1c2e56211395479f03bf2eeb", "guid": "bfdfe7dc352907fc980b868725387e988647cb4ce22c8ec8c2ed17ea0d0492a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d072b32d0c571e6e88514f3e970368", "guid": "bfdfe7dc352907fc980b868725387e985e82db00828fd21b15e6639f49feff8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553994b81ab796c4e7271ea2ec683db1", "guid": "bfdfe7dc352907fc980b868725387e98999f439fb4088d06bf1074babdd485ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986378f48e6e10ef7d5a3d28771669f2bd", "guid": "bfdfe7dc352907fc980b868725387e98cb41cbd4bfec9246af9e3cd1c445fccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c7a0cd6e20c4f4e6b998ae3d554eac", "guid": "bfdfe7dc352907fc980b868725387e988b8a8baf00133b1f411a585a0f50a87b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981320818d54fa7fee8cfecc6cb6670b77", "guid": "bfdfe7dc352907fc980b868725387e98c516162a0770cbacdc02d5158a53d2ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd956226db2d69c3eb17331e66d62dd", "guid": "bfdfe7dc352907fc980b868725387e98472bb28e936d4ea7aea39d5ee7c42dde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982056e479c3d6c4166cb1b7d3553efe7f", "guid": "bfdfe7dc352907fc980b868725387e983ab92179324d7860ee4b1212d7002f25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1fcff8e26f7f82f70da9c3da2ac98b", "guid": "bfdfe7dc352907fc980b868725387e982871ee264fda7578da68d849af51f2d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98febd469d5bf9cb0afa0b49bd4875e2a4", "guid": "bfdfe7dc352907fc980b868725387e986729341e673077f8c21cb0032cb8422b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506f5eddc1438a5241ed594359b5d9ac", "guid": "bfdfe7dc352907fc980b868725387e982f3ef1474f8dfc3502afba318a121699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc1239efd908d0f0ec42dd9eca7948f", "guid": "bfdfe7dc352907fc980b868725387e98b16e7aae885add4a35f3b348093a12f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feaaf053e6f5c0c8ee3384664cb4e156", "guid": "bfdfe7dc352907fc980b868725387e98e920841b8c63584552e66ab60bf72efd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1dfb15c9321114d09b7fcf7dcc1ac03", "guid": "bfdfe7dc352907fc980b868725387e9811e03ea703a2ebf720454ef53033ffc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f20f98e753efd3ff40494f5b030e6be6", "guid": "bfdfe7dc352907fc980b868725387e989721a91effc75ee3acd8b01641472336"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4f93b36ffdfe58cfcf5874741cc3ec", "guid": "bfdfe7dc352907fc980b868725387e98f182cedadf5fa426f9ed7b5c09f7449c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdd2d0592763b9da5b4274dd03118b21", "guid": "bfdfe7dc352907fc980b868725387e9890c221576511286f832b8ed101ae93ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8ec7109d582f831620075b9e4014727", "guid": "bfdfe7dc352907fc980b868725387e988138e256d6d3694b01d1a2281d8d22f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f81ed83b5779c1c120c2dbf018046de", "guid": "bfdfe7dc352907fc980b868725387e984de91109f1e25335d538e7552bbe0a30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb91ba4a106068d1ca8dfe9bac078f71", "guid": "bfdfe7dc352907fc980b868725387e983f33bbb7e57ca96fda8a5832f993692a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdeec6ebbbacf7cf32b668db563bc926", "guid": "bfdfe7dc352907fc980b868725387e980f912bc5efe30be2b232a447b7f94230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2eb24e249f834acce0a591756b1f09a", "guid": "bfdfe7dc352907fc980b868725387e981c5afcb229ff78edc29a2fb00d520aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98131fbf5129ee749e9010616b7a066808", "guid": "bfdfe7dc352907fc980b868725387e98d815336b672f8ebe1e7f09548e581a0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea5ffbc7f47c7e5c163b2b8aa234373", "guid": "bfdfe7dc352907fc980b868725387e98141a1710ec7bccb6eab969902723636c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc8fcc6a17809267dc85c21915eaea08", "guid": "bfdfe7dc352907fc980b868725387e98edd0a7c212b03a23bd32f7a10784ffa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df0c060d72d546a615344256d00dda7b", "guid": "bfdfe7dc352907fc980b868725387e98690d9e309a6ef657753984914bbaab2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869f0e6e8ec22cac706371dd16b63fe66", "guid": "bfdfe7dc352907fc980b868725387e984b3ab0e8da1e1037ae5c3f8db802ba4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984451a161af8b3de1c1ba6e88e0c54ad3", "guid": "bfdfe7dc352907fc980b868725387e98997057517125e25df2e715cafbcdb871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a5cb6524d0c2fee7bb329fff584363", "guid": "bfdfe7dc352907fc980b868725387e9811122b905d675ed6688b5a30b7958235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8e58efa8cd96270adc7b043609e037", "guid": "bfdfe7dc352907fc980b868725387e98cabd9f02f4e7542e2b50a03a11b239be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f2afd5639e04f8645387d1bf29465c", "guid": "bfdfe7dc352907fc980b868725387e9899eae7e627d55fdc6f81383d48fbd2eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c726ab4f792b095ebc821f967e5125", "guid": "bfdfe7dc352907fc980b868725387e9880d3db208c81a6157025e1080d72a5aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b03f67ab373389dd1b687c7a1a5150d9", "guid": "bfdfe7dc352907fc980b868725387e98554879be12ed254a61792e85ec2d8638"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98528f42bedf00a6125b3bdc2e6e3d746e", "guid": "bfdfe7dc352907fc980b868725387e98d41bee341e2629b19ad831083afc6d96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168200c842bd7e0de845546c2b7ceb54", "guid": "bfdfe7dc352907fc980b868725387e98eb3903a70b4a0afa8f82defe1a3ec5ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ce4a6abc7667512688b6b0dfade6f74", "guid": "bfdfe7dc352907fc980b868725387e980e5b417e020f84bad0703e114bad3e4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807cfdb746102b8095e30d9ee507c0580", "guid": "bfdfe7dc352907fc980b868725387e983ae34f16803a0207e94e7ed3c973e0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a64443fd5348ff8241de31ab1038b8", "guid": "bfdfe7dc352907fc980b868725387e98aa58f6c340f6c39c9de7355bab8e2006"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb51c9259a60dbb92e44576600832a8d", "guid": "bfdfe7dc352907fc980b868725387e98dc20d42fb715cb9d8483143ce0458589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4283e0211c75c17c5d496cb0d0d4ff", "guid": "bfdfe7dc352907fc980b868725387e9808017e153d7ded0c28260d385b481f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883ea2be707bb94ed878dbd241b3006dd", "guid": "bfdfe7dc352907fc980b868725387e98b6228fb9f1a6a2e41030bb78fc8bde04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98180cd2a1d19de8f0f510a00742d60d7c", "guid": "bfdfe7dc352907fc980b868725387e988d00d1509e650d8a1d747696b5d14c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989445dfdae38b943c85dc6043907d7660", "guid": "bfdfe7dc352907fc980b868725387e981e217afe3ed1f92870d53bd1389fc1b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4ee36a67835f1a257e1001aec052eb7", "guid": "bfdfe7dc352907fc980b868725387e98740d22a57b4f6c9ef0c2ad69a8c4dbfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0e1fc8a43f2e01280f2042628632f5", "guid": "bfdfe7dc352907fc980b868725387e981d3ab2c6ce22eee1a43f5fe9bef2cf2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccc4c0967f53f3b6d6cd5695e77120fb", "guid": "bfdfe7dc352907fc980b868725387e988ba0feaed06516787839c27fe91d7f2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804cff912cb675e1f766744cc5a70e8e2", "guid": "bfdfe7dc352907fc980b868725387e9832256f90e1fcfecd1846848be8d8c2b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897cd59b402e160c1938da261c1af2123", "guid": "bfdfe7dc352907fc980b868725387e98090592718147e2ff595a018cce2ef118"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eefe775ee266cea0c6ce401baed4514", "guid": "bfdfe7dc352907fc980b868725387e98758d2036c89192fb85b5c92d024f4bdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfaba300bf477da3db4f7c8909ad9ed1", "guid": "bfdfe7dc352907fc980b868725387e98313ce79d977a19a6a958122e54de8866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98835c9382324da4801f70d8c320addb52", "guid": "bfdfe7dc352907fc980b868725387e9855387a2a4312c909adcc8a9e6b782bf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e94018d21bebdcd8d08bd7fcad0132", "guid": "bfdfe7dc352907fc980b868725387e98ec7be56a1ff8ac8c6e8e26294f4f59a3"}], "guid": "bfdfe7dc352907fc980b868725387e981180a0fe0ff48e6b83cf4a42205d3229", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e988617329a953464f752fd48603772b44e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a61d761249950157b7def56ae6875b", "guid": "bfdfe7dc352907fc980b868725387e98537cf2196193520d616953d4cad4cf95"}], "guid": "bfdfe7dc352907fc980b868725387e98b0cf78760284db86545035beb1a0c591", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983a94062893ff002226d0dda1751c23c4", "targetReference": "bfdfe7dc352907fc980b868725387e98c2a1c2faedbc5c4bcc7c7e6fc87d379b"}], "guid": "bfdfe7dc352907fc980b868725387e98e2aa8f74b2be740959044cf2dcabb704", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c2a1c2faedbc5c4bcc7c7e6fc87d379b", "name": "StripeUICore-StripeUICore"}], "guid": "bfdfe7dc352907fc980b868725387e98f17b608a8faeb01ef6ec76d52489fd0b", "name": "StripeUICore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9806d56e69e7598ecab3f1c2f2b4c84cf3", "name": "StripeUICore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}