{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9817bf229f124a40126bd693263f883079", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989daecbeb58b40f1bcfa4d139d6ad2b9e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f70cf978f2878ef4be06ab18bc1dead3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a998bdd3e45b0ef62db804b76ae33a5e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f70cf978f2878ef4be06ab18bc1dead3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9896a3d85e8c4649703f6a94e133f01d07", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988a4fc54d3c02a757036d6b148c836d0d", "guid": "bfdfe7dc352907fc980b868725387e98f450afb42956be75bbf2b4c06521af88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824f0e93df543dc4f7fd911f199d1c3da", "guid": "bfdfe7dc352907fc980b868725387e9852b14866ad351c53fa773192091ff5ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988540180b4e54cd3c55047d01449c7771", "guid": "bfdfe7dc352907fc980b868725387e98cca22ccffaeae5c372bc200ee2b5d20b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad8b56f8320e3d768a31e9b94c1cc0b", "guid": "bfdfe7dc352907fc980b868725387e981a51f6112c98ff2379b227e02c971aeb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3c35406cfb334380a5efdc3ce0880a", "guid": "bfdfe7dc352907fc980b868725387e98c1b86d5ac1014f5ca8381117fbdfbb8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff42a47cf55567f93a85c766127afd8e", "guid": "bfdfe7dc352907fc980b868725387e989dc32b5ea4bfdf1c63060d7dad75058c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e86b6f56829e1ecf3aba547eb346757", "guid": "bfdfe7dc352907fc980b868725387e9819d4c73a4e59e1276f244a1e2e09d107", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853abe5288c04123409deb2ddcfe86a56", "guid": "bfdfe7dc352907fc980b868725387e98941fa1186c3ea16e88fae2a2174c8d13", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2d9a7d2df2c62b67f94e96f56479e4f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d2a161bb33dece94f63f5fd54ec649a", "guid": "bfdfe7dc352907fc980b868725387e983daaf90c1af6063930dc8148ca38801f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e951256f919ab8c81f0132e8a8e75c7", "guid": "bfdfe7dc352907fc980b868725387e98432b2f1a6fc8678e97bafabb73568c96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a928cc6ad78e2580be2e1ed9b35b781", "guid": "bfdfe7dc352907fc980b868725387e986d2680a16bcf1968bbe70aa5a22be113"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891fc792152e111b387ef17dfbf6a0285", "guid": "bfdfe7dc352907fc980b868725387e9896677cd8999fa3306d4e1ee74175de2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886c56319c0f305e3c42e4a98de4eb005", "guid": "bfdfe7dc352907fc980b868725387e98cec821b468254f922f91487a6c291b85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806d6d18f0cb65528dc29c071cc76c727", "guid": "bfdfe7dc352907fc980b868725387e98e6366aad00e4793b2433c9ef2a35fdf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fc309431856c48d83a1a4bc7c804abf", "guid": "bfdfe7dc352907fc980b868725387e98409b2f6c37231c840d3209d6b4f85ab8"}], "guid": "bfdfe7dc352907fc980b868725387e989840e511dfdf6a3f4b2aed6486f0e664", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e983727b0d044be61efe5e23e3c04d805b1"}], "guid": "bfdfe7dc352907fc980b868725387e985e5a22f8a8b0575957086b10ff09a4a3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e663c4dbf4cc8678d6aa67a178cb6e60", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e981f000f066404b97b12e9c4ca84d38d0f", "name": "image_picker_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988e06e8c3685b7c12032d8059f412f4cb", "name": "image_picker_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}