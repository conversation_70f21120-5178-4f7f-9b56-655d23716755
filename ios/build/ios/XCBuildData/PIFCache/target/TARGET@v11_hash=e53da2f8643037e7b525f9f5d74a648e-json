{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983e646efb39c66cd9cadf1b46cf28224f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873a8ff75872a91381df4473820156fda", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ac3c758e31d7d45b7f64bf6614d2751", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f0d1f7953d00ef712daa1f0f90b1fca", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ac3c758e31d7d45b7f64bf6614d2751", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838a7b9c93df9757bf97906569604f0a7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bb85a9affa7c49ac482f6f13594de44b", "guid": "bfdfe7dc352907fc980b868725387e9884ba53290ac74fc07ee8ff0585252df2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988785d8f74818acc1bd093065084e3e4f", "guid": "bfdfe7dc352907fc980b868725387e98a25931f24e73403a9c55d66f7ccad390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ceb0110a9ed6cc9a83e62c8a48c4578", "guid": "bfdfe7dc352907fc980b868725387e986dd553fe74fdd3c7760de0bcc16a704a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ed6848697003f2c1faf2dead0a7912", "guid": "bfdfe7dc352907fc980b868725387e98532cc99f0164168fecb7072824c5ec76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c151f4624d6e66a732a98c4d5cf53d4", "guid": "bfdfe7dc352907fc980b868725387e98daca6c3f2039396d314f62885b08ab83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e0408b4f8c5a2487e4afcb46e40fd86", "guid": "bfdfe7dc352907fc980b868725387e98dc68fdd459392ed05052417c4b21c37e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba430b5fc34b02d44bb8d156bb00045b", "guid": "bfdfe7dc352907fc980b868725387e986562bfb3b9d033d60a455066222932dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ff3dff2a22aea1d2871775e89b4f7f", "guid": "bfdfe7dc352907fc980b868725387e98d67cd84743b2f86e8e193f0c8628809d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bbd7088013c007cc1d0e3a15747ba43", "guid": "bfdfe7dc352907fc980b868725387e98755be7bf92c15989330f818b72780d5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af74b934c7d8cd547e2c4ab1cfc8fd79", "guid": "bfdfe7dc352907fc980b868725387e98d597e629cfde9c4cff6ef7bf7e2b26b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98806062b8053e608257ade753869a996c", "guid": "bfdfe7dc352907fc980b868725387e98dbb2519b0ae8b1df9b1cc2d102af7e00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc39455915608d26805449ec0bf5c0b8", "guid": "bfdfe7dc352907fc980b868725387e98b4c8cf4041781b8703e39f67cea15413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed0acbd05c41af19fad891dd2750b7c", "guid": "bfdfe7dc352907fc980b868725387e9880e920215bb1208b9909f97424d4cea6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98514a309648eb2cb58829287cf7348a96", "guid": "bfdfe7dc352907fc980b868725387e98bf6b84165fbab9355a3ce2cb67ff228d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c48288a045f04b3515181d5612df08", "guid": "bfdfe7dc352907fc980b868725387e98ff0e320effd243df907ed90f7dd9602c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988009d2ea2df80891c20f160a659f2c47", "guid": "bfdfe7dc352907fc980b868725387e989edafe238a94105fb8e6f360d3e2ee49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98498de8590f25e8ec7df8fab331e1b423", "guid": "bfdfe7dc352907fc980b868725387e985fc5a207d851ac1704f11e307732f10f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579cf7816d93e157063cb7abc1767be6", "guid": "bfdfe7dc352907fc980b868725387e98b8c3546a7e682e8f103655fbe11fccd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a81ef2384b021f93a5d04c92565190", "guid": "bfdfe7dc352907fc980b868725387e98880d932bcc01dee25256c746188902b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869a001fa0791bc6adca56701ad416aa0", "guid": "bfdfe7dc352907fc980b868725387e98c31f8327f8ce36923c17f6a2fd3979c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b65a668c7fecbd809dca4b6fd6f4036", "guid": "bfdfe7dc352907fc980b868725387e982220f5d5e06964806c71cbf2b141f55b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889c7f4393617e858755f8ec023eaced3", "guid": "bfdfe7dc352907fc980b868725387e98084b0eb3a89c8f826d6afb38f7e81a26", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981308740c791a1ecc529c21e1a786cecc", "guid": "bfdfe7dc352907fc980b868725387e9824f29743e1027068e9dea3ec7871ecbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1e37885780dbdb5c01149777f0d77d0", "guid": "bfdfe7dc352907fc980b868725387e981d0406ed6e1fd2ef305d1c6878f2249f"}], "guid": "bfdfe7dc352907fc980b868725387e984d4e28663a379cbcb67d87ce72be94a6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9893ee964c1829a40dfac75b0433b36503", "guid": "bfdfe7dc352907fc980b868725387e98c13052d9b53ce9d348ad861a9195a986"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b887883fa4fdd6baa0df780e3dada62", "guid": "bfdfe7dc352907fc980b868725387e98ec6f0018b548d4405a17e3b55823c877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d819c31f13d9a89ddb410bb5821bccb1", "guid": "bfdfe7dc352907fc980b868725387e9887c2f68ffd66a412d65d6e348890409f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e35724bf7233c05fd215fd15b44246b1", "guid": "bfdfe7dc352907fc980b868725387e9826c0771b16bc9b02381afe6388bb7f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872f4daf73dd3b1d9cb1e3393df9b68ef", "guid": "bfdfe7dc352907fc980b868725387e98f3122c97f7fcd697e000de24efbc1f84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b0492de4a09595c5e5d3cd6ab1a5af", "guid": "bfdfe7dc352907fc980b868725387e98b0c94add4b2da42e91b5fa4fcc74ec3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfc4cef2ef8fb166a5a1f573d88fc567", "guid": "bfdfe7dc352907fc980b868725387e98fda55a9df24015388c4bb323eb8164ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea2209b2e966016bc688998c50ce0eb1", "guid": "bfdfe7dc352907fc980b868725387e98228c17a6129b0c6ca9c05f639579537b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f7df73b5d0c5c4c942d5108e8dda13", "guid": "bfdfe7dc352907fc980b868725387e981de57bbde9ba5ad8285f223a038a0af5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fb5637c59b716f9f955c17f8fdb9c19", "guid": "bfdfe7dc352907fc980b868725387e984581dee61583f31fe49965b54694bf06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987020d695f94427900b61050ffa760766", "guid": "bfdfe7dc352907fc980b868725387e98a6af134992532e2835ac6abbfd39e718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f36f4b68b5e631bff31be3d6deaf36da", "guid": "bfdfe7dc352907fc980b868725387e9815ed0b2c4e640fbe5fd52fcd8049a20b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98771548247c9faf8f9ac0647acdc90ca0", "guid": "bfdfe7dc352907fc980b868725387e9800cf2ea6e56d82d1d965c68c2075c678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a993217babf6803699a7a9557d954e71", "guid": "bfdfe7dc352907fc980b868725387e981c8cda52a680f5e2f6f94812058d3845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d5b2e1090ea1fb6932d46bcbaf2d975", "guid": "bfdfe7dc352907fc980b868725387e980693e913178ab94650e4049f0415607e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7957e4829fe4d5dab090af1e931c530", "guid": "bfdfe7dc352907fc980b868725387e98ea464273beeef5b7e3cd3b8b7ca8ccdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e30b96b49c5c1a88a1cb354c11205c3b", "guid": "bfdfe7dc352907fc980b868725387e9895c0667c0ace503d8c832738733ed761"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d1a6ec7f97be5bb2bdc3544a093cfc3", "guid": "bfdfe7dc352907fc980b868725387e98de2095227c0ecad7dd0171995cda5d1f"}], "guid": "bfdfe7dc352907fc980b868725387e988dd45fafb15507fdf58f51ad8fccfc0e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f938f9a789e5139cac52de531a14aa2b", "guid": "bfdfe7dc352907fc980b868725387e9867c405f976fef074b2cf3b7104b963fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98461c1b1a90606ce6938e502d5ec61b0f", "guid": "bfdfe7dc352907fc980b868725387e981703f1d5abb3e75f6a1865eb2f546957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98c77af896ef63a729e8f40effc0951789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842ebee70e819cfdcec40865ee79e56ab", "guid": "bfdfe7dc352907fc980b868725387e9809d124a01ee7351de5dfdeba841705e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e98a0d19b172b27ca2587fc77d14b408856"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a61d761249950157b7def56ae6875b", "guid": "bfdfe7dc352907fc980b868725387e98ead4ee77a51f748f1364783791550163"}], "guid": "bfdfe7dc352907fc980b868725387e98cc87ef096041c524438762df8de43633", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cead752b43efcd0d5266992ccd0f93bc", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98ff21c6f1569fa293fe4177033d000bc5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}