{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e40fb8fdcd832acc758965ac91d7d347", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9870aaeead79c7fefdf64eb7d3e4af3784", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a8db1c8f09ad515f46a379fd54c627a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846dbf17c909baad90c899981bf7b58f5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a8db1c8f09ad515f46a379fd54c627a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/stripe_ios/stripe_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/stripe_ios/stripe_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/stripe_ios/stripe_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "stripe_ios", "PRODUCT_NAME": "stripe_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e1e82d4250cb8a0155cd2e7a563dc21", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982aaf43f667b5a3a5327d90c457a7c427", "guid": "bfdfe7dc352907fc980b868725387e98751c5cdb45eb6aeb5ed678892976cee1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d748b024c10452ed62fe0f685d586598", "guid": "bfdfe7dc352907fc980b868725387e98cb131a3adbda17ec7360ac4fe8c6702b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836ec01d73dafc534c9e41a617b298c2a", "guid": "bfdfe7dc352907fc980b868725387e983379b2dc78a819e87c059e3c070dbd6a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9881e7e86a9bee926e1c244acee5fcfb32", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981f0c2f438c6f4c40261856b2347f720e", "guid": "bfdfe7dc352907fc980b868725387e9843efd10bf77fe59ef80f6e921a7df14d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b236ad524e41bb5a6a1554d95ff45d", "guid": "bfdfe7dc352907fc980b868725387e98e81334de4676546946004bf7a0867d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df923fe673fdc5c316b1f3c7b42dd7a1", "guid": "bfdfe7dc352907fc980b868725387e98c5633d0aa82a27dc3a38d80ecd9c8c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984443d425e03151cf320224c62c44f8a4", "guid": "bfdfe7dc352907fc980b868725387e989d05d3c70cc0fffa2bf2a97c343169fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8f8191280b94a235303b56fb68b88b3", "guid": "bfdfe7dc352907fc980b868725387e980f5455780ca682570c6b51f66f46a714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f00012b230bd9efbccb19ffdd01fd3e", "guid": "bfdfe7dc352907fc980b868725387e98dced7fa2bb5ec76002029d0294a0db74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e5e31d23e6ce0ad03900cd327a1401", "guid": "bfdfe7dc352907fc980b868725387e98bd2f5f98a5f9880f4040ca2b63ae80c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98780a1e87ebc075c35158c45e16b4a1f1", "guid": "bfdfe7dc352907fc980b868725387e98cf2c23b979e169546ac190518135208c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877152f14e7861f39d9c1e3f721c0aa60", "guid": "bfdfe7dc352907fc980b868725387e985d7609879a75567fdb5f547de7162e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b4ffb79f16ddf3229242a6e6e5dbe19", "guid": "bfdfe7dc352907fc980b868725387e9875232ae3a2419daa7bbe8a5e5b92abd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e861c264a23ffcd5a6e751c449dec662", "guid": "bfdfe7dc352907fc980b868725387e98a489503654ac586a96699fa41d0b3cff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886ae40bb809f660f84a5860a87447393", "guid": "bfdfe7dc352907fc980b868725387e98725305bb6dc27bf12e8714474fec18c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d16108a36b4623adc1ef0d10776e88e", "guid": "bfdfe7dc352907fc980b868725387e989e1f202eb8b211eaaf0a5292169015b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4156a14980ff15aef284d207aba554", "guid": "bfdfe7dc352907fc980b868725387e9830692b7baec36e6ed6e0bbc669887c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5415a3338a9dfb0ab0638c5f92c2f36", "guid": "bfdfe7dc352907fc980b868725387e984c4556807f1f60b1d2773bee9d46af57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98423fd378919c4012a4413a8200d4e7b8", "guid": "bfdfe7dc352907fc980b868725387e98418adabf4ab656d3207c52fdfc7f4527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989feb574a7882cab42d0be23e0520547f", "guid": "bfdfe7dc352907fc980b868725387e987b5bdd9f403a44e6b9cfa61ef56c7d0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a7d795b5ac01fb24782d5f93656486", "guid": "bfdfe7dc352907fc980b868725387e98ed3eeaf7a951401fcb5c1b3c954d3e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abdfd1b4c664921b0dcbfb4136764edb", "guid": "bfdfe7dc352907fc980b868725387e986a789c431975b53a4f736ffcf2ea07f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985712f020fdb657fad602d4ca08fdd8bb", "guid": "bfdfe7dc352907fc980b868725387e984f0d721b36c583edfdfd1b30163d86bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985107a2543f0eea7a1a4a480ca6c068a2", "guid": "bfdfe7dc352907fc980b868725387e9852662ef227d9e8c0c836f71d477db1f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984496722a6dd1e0ed42e15037358ae13c", "guid": "bfdfe7dc352907fc980b868725387e9874ce4504c4145cce9a110f814ce1faef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820c121ffc8f5606360d5479046846974", "guid": "bfdfe7dc352907fc980b868725387e9833b81cd54cce766f034630635cdf4ea5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eca8b40acc334e3afb81792f7c3c96e3", "guid": "bfdfe7dc352907fc980b868725387e98f948b68d3aceaa2d8d441b2eb8d82eb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800dfd51e653d1b2a10d3e0b4cf9b3798", "guid": "bfdfe7dc352907fc980b868725387e98da525e25877d26b32681bc44d37bfba2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0cef3d8e0c34810f593dd10f27bcc86", "guid": "bfdfe7dc352907fc980b868725387e98bff1a5bb0392fbd51c60224c422607e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e577dc3bce5c01af97d60cd617b8c6", "guid": "bfdfe7dc352907fc980b868725387e98d829655d158b97c454401594e4d72080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888e99044b3525e31d38f57541e599c44", "guid": "bfdfe7dc352907fc980b868725387e98276368c3a17c2f86743ed0cecfdcde98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0ffd7b979a292661ee507565b48f3c7", "guid": "bfdfe7dc352907fc980b868725387e989e4672da227867fe39a583c6b16fe893"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c3bbf49423b4d70b9f55ab9a60d9c94", "guid": "bfdfe7dc352907fc980b868725387e987cc86d0e1534a348c5bb2fbacd6b0d84"}], "guid": "bfdfe7dc352907fc980b868725387e9806489f7107955a60c6edcf745d40b9bd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98c76e70811ada38d59366c746867b297e"}], "guid": "bfdfe7dc352907fc980b868725387e986d4c4edb50e576d2946bc0c57d4b90ca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a643ee818a22023d4c33d2b5d01f4801", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802b8a2060b8f4c4f36a50487027e7bca", "name": "Stripe"}, {"guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay"}, {"guid": "bfdfe7dc352907fc980b868725387e98528ce098433a66c9a9c5c097217013f2", "name": "StripeFinancialConnections"}, {"guid": "bfdfe7dc352907fc980b868725387e989814132af5b72ab87e6f6046cac2a3cd", "name": "StripePaymentSheet"}, {"guid": "bfdfe7dc352907fc980b868725387e98caf0f30362a7eaf9b8b7f5ba71771d54", "name": "StripePayments"}, {"guid": "bfdfe7dc352907fc980b868725387e98bfacf038ceaf928d957d7e7abcab2e3b", "name": "StripePaymentsUI"}], "guid": "bfdfe7dc352907fc980b868725387e98f20376386b7fdaf72e36b18058deba48", "name": "stripe_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987ca3631b039050a781963bb810e6746c", "name": "stripe_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}