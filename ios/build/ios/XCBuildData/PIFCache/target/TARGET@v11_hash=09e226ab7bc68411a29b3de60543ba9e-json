{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e61394dcaabca51093320f3b2ce669a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d4d818a59f62f9ee0b720bd3ed6a87d5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98970eed6a0afc8e2bf7b18893abe0becb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98186798df3fb2c4b8ea096947bd405b8c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98970eed6a0afc8e2bf7b18893abe0becb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9813f99762bb05ee3a4e25edf6fa63bd54", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c3d432e4810a9bf7a9e765bbcd2f88c", "guid": "bfdfe7dc352907fc980b868725387e98304a8ef520f1f5cf9aa28642348bec5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de1d80e07d8d9de40aedbcb53612afcf", "guid": "bfdfe7dc352907fc980b868725387e988125a20579b3fa5cddc2ad90f63481a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ab036fcd9db7503f8b30828074aeca7", "guid": "bfdfe7dc352907fc980b868725387e986580a5d0904272ea281b8f8839f5e994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae1387017332963412ee9897cc3cde8", "guid": "bfdfe7dc352907fc980b868725387e98a642b4d9fd92b536fe3399a7b0edc82c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c31efaeafb6c4b56b3715c8d068d2b4a", "guid": "bfdfe7dc352907fc980b868725387e987d3148744d1153787ec5c085a4b850da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899935df78d00db34257b1c59c8d6be42", "guid": "bfdfe7dc352907fc980b868725387e9890b2926319dc933565fb4913326e81cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abcd02cb673e47e54abad6c80a0afc02", "guid": "bfdfe7dc352907fc980b868725387e98f9f580b37aaf6c6987af062a306435ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98159e6f08de24d4c63deb3d2774596120", "guid": "bfdfe7dc352907fc980b868725387e989924ede18c4e34bb4ad29bf2ff20c508", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888501f24122d02a2f3579ce35da39b91", "guid": "bfdfe7dc352907fc980b868725387e98cc6990e2819b6ca10e8f773c86d2d2bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886026155305d640008900793c77192fb", "guid": "bfdfe7dc352907fc980b868725387e98e7a9c988f637d3a766598d9ed1287e51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f6737f9a0c205e020a1d2ac475873e", "guid": "bfdfe7dc352907fc980b868725387e98bff65de80cdd424d4950e3a7383fb837"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7ea5aae10696f67762ff3b4cd2a3bfb", "guid": "bfdfe7dc352907fc980b868725387e98e5d0536722a3321ec5c26ee6f99c04ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841a6bf34742d05b90b16e0aa310a68b1", "guid": "bfdfe7dc352907fc980b868725387e98f218d534320a4f8f40f29b97545d6219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899097a0b397a895b5f0c7ca216d06faf", "guid": "bfdfe7dc352907fc980b868725387e989b4f2d901006fff04a1523f85b1492e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980afb90e76cdd21dcf7680a5ec8e04308", "guid": "bfdfe7dc352907fc980b868725387e9819f135ec7874befc8a406d35aab2981b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98573227909bd0ff3e2c8815114d78c1fc", "guid": "bfdfe7dc352907fc980b868725387e981d2d753d194017958fe2f92c4f412ea5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877f15cf87cbf54a1700c965e4949adce", "guid": "bfdfe7dc352907fc980b868725387e9822d9a663fa651e198da4c7a5c23b88cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe945505d266def540535bcdf9e6e8a6", "guid": "bfdfe7dc352907fc980b868725387e982bbaa964ee7786007168382a4e0227df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a60a1081cb4ab85361fb6f3cf0553dcb", "guid": "bfdfe7dc352907fc980b868725387e98bc817412ec32cfda9a86ecef4bf78fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a859336fdc3d6a2d5a5e7c3f599c4b92", "guid": "bfdfe7dc352907fc980b868725387e98b9211f2bcd6e3e3ebcc25d33ed1158c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98619c36e14e546a6f162f56f570ef49b9", "guid": "bfdfe7dc352907fc980b868725387e98c165682618e85e85665a70ca8581d0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897441b7477a84bf0a1aabbc1c1ec43c2", "guid": "bfdfe7dc352907fc980b868725387e983647563fc6997768d83992f8e24fe76a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae7eedf584cb04561df5ea922b7fbf2c", "guid": "bfdfe7dc352907fc980b868725387e98f50f4cb1d34ad426d6984eb53f6316b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6c922dc108f48f0d9f330cc84fad39d", "guid": "bfdfe7dc352907fc980b868725387e989066c263bb8bafd3723263c232b10ddd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be2d21df325af1912dbc55665cdf94f1", "guid": "bfdfe7dc352907fc980b868725387e98167c5d5704a73d5725cc8b7678edc435"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4da8ecc3e0d7632cb2321e861c00b5", "guid": "bfdfe7dc352907fc980b868725387e98539d966252391e160ec45a7dfc326fd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116fe5a32dd0230a507e395be07985b2", "guid": "bfdfe7dc352907fc980b868725387e981b47031bec164284e2597446b8ad9147"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdab65db9a0f47c2f4cf8aabcfb0b219", "guid": "bfdfe7dc352907fc980b868725387e98ecfe843415827fe3738a2ed8151f76b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde68678747c15ce9e7c0719982a0c26", "guid": "bfdfe7dc352907fc980b868725387e98734d6b36d0b8b8d475d96cbea06f614a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e0ea75538c9bc12afbe24d8954f0bd8", "guid": "bfdfe7dc352907fc980b868725387e9851684237eabaf2085bf2548fa2cd93bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832824dd96086ea5b004681502d4d86ba", "guid": "bfdfe7dc352907fc980b868725387e98e9af9611cea50b4ee799db56eafb9c1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98182e130468b8f45a91bbcc11bacc3cd9", "guid": "bfdfe7dc352907fc980b868725387e989b944794117510d6eedc9d42f0015676"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4f02f3bb1ea99f2144222f7e60dddc", "guid": "bfdfe7dc352907fc980b868725387e9849c20a7a2ae91ecfce61266ae061e044"}], "guid": "bfdfe7dc352907fc980b868725387e984e67f2759f08138b2612d2f2ae1fd69c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988db728f1f2b3cd2781ee737bf247868e", "guid": "bfdfe7dc352907fc980b868725387e98abc3467b02bed28b6c8d92c7bb5b076c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bed4182f05d7938476e0ee9629720df4", "guid": "bfdfe7dc352907fc980b868725387e9870796e407200ada8c63e9782fd5f193e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98425ee0ab9ffd7bf9ad9ff55c52b13132", "guid": "bfdfe7dc352907fc980b868725387e982fb4d0c1d9783ec4d431534935d7dee8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852be1557908aff88889cd13e767dc721", "guid": "bfdfe7dc352907fc980b868725387e983d984551e5fbde26e232617018dc0a18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51be0c9d32fc970770ceda898b5d8b4", "guid": "bfdfe7dc352907fc980b868725387e98a0f7ec83a1ae3a95db3cd81f867e0c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897576afc59d6fda82c73bb41cfc0b712", "guid": "bfdfe7dc352907fc980b868725387e98716932af78af3f7c64f9d9c40590c34d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda5ec33023b56b248c16033d7eb1951", "guid": "bfdfe7dc352907fc980b868725387e980e49c1d4e3aa39a6c258df71c837ee45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98560f7c1e0a13e1f3239b0178d36d5082", "guid": "bfdfe7dc352907fc980b868725387e98d79b9436974d918222ac914ca26f953e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883def2b75ef63b1b3df110abc9f1c354", "guid": "bfdfe7dc352907fc980b868725387e98164a31be1d9e5713b07dd3aa2b09bfee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984229b43facca620ba67886ef8d74b776", "guid": "bfdfe7dc352907fc980b868725387e98658ef85a05264a8283101981cf764648"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f14ab9795eea724f81d803d0471104d", "guid": "bfdfe7dc352907fc980b868725387e980f6f2b016a117a55989fd8b6bf4dc076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826889a738fadcb044aba513dec3b720e", "guid": "bfdfe7dc352907fc980b868725387e980c120313e73ec4a3385b2fa94e712538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983926ce286c9a1ee83620cecb761a7f1f", "guid": "bfdfe7dc352907fc980b868725387e987984547cba9e8876be191e65b6981f7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed5ffbfd11f13a4186b186a4afd7f21d", "guid": "bfdfe7dc352907fc980b868725387e987994a9b940eea2e68caec7fccd388e60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ecbcfa0892a94746647a9de92804fdf", "guid": "bfdfe7dc352907fc980b868725387e98cae7f95ff5c42c96a4ab89082aaa4a11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837e1a0113b5b1f9c114aeaa9a4aabf75", "guid": "bfdfe7dc352907fc980b868725387e98b8feec448cf8ab59d79226b45589ff64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa2e9ebc21c49537a45d5d3936a975a4", "guid": "bfdfe7dc352907fc980b868725387e984a544cdfced89065530565925a931c91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7deefb5de12c1f3c5182bb5348016ba", "guid": "bfdfe7dc352907fc980b868725387e9849a277dae60808b7a857542e69c7514f"}], "guid": "bfdfe7dc352907fc980b868725387e980f3180ad9835c07af6ed6b64f2c3568c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98dd2bce8b129bc1e44fe9016806cb0001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e986dba74a95a19df21e2dadf1ccc2a5d92"}], "guid": "bfdfe7dc352907fc980b868725387e9896730ad0997d968b1383e0fcc4c842b4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986555e69ab8575be5618894481c84edc6", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e982575d98dd0d8d465af76b8cee0f41dac", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}