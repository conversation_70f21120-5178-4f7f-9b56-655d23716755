{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98513bdfd427e496391a5999ec9ee478ce", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b44da7a35eb8b26a79fdc765bc663b27", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895c7db3ca3ce32581c89cc4de00b377c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d66f7e42b49fabb2050b4dc67c313861", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895c7db3ca3ce32581c89cc4de00b377c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/3.27.2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5c129b7854a20b39565f0a45fe4530b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988ef2a6a00f1aecc6897833ae372a9033", "guid": "bfdfe7dc352907fc980b868725387e988c4e8c8a4958fdba9c1bf00320ca3cd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1ac8e4f9bb1fdb98b0a5d0a6198663", "guid": "bfdfe7dc352907fc980b868725387e9886db73884481fd468ee48f5554d4518c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98355f54a59a94cb78ccb4c5b8ce32d22a", "guid": "bfdfe7dc352907fc980b868725387e987c1e4eee005d4589ecc5afcbdea4ee13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f35d3e103c662fb2db8d90f1db1ff87", "guid": "bfdfe7dc352907fc980b868725387e9804a5b0174f97b8300b74c49b9a4c8483", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1627821bd65a15cf126c2fc8e5c2d45", "guid": "bfdfe7dc352907fc980b868725387e98197acac69bf5ee409cc807ca63e96314", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff728c7368edd3f45c7cffc50ca7bdbd", "guid": "bfdfe7dc352907fc980b868725387e98b0093639dfcb8e045c787a763b236ea2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981218f4eb2aa7b56d60d4faa81d65b250", "guid": "bfdfe7dc352907fc980b868725387e9885c903b42c2094f3d1310d26792b626f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b4a984ec8ee30b5c95039fc50b7fc9", "guid": "bfdfe7dc352907fc980b868725387e988dcc3283e43ea7d8058ad095b6078a7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f611c63fa0dbd514dabea2db2b60d96", "guid": "bfdfe7dc352907fc980b868725387e9864989a1f013e7e76a854a1dafd1b3a92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983880606e9d34065c7fbc0754f8caf181", "guid": "bfdfe7dc352907fc980b868725387e986ac658bbf996ada14cccddd9a9665950", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5417c7b7b5a29d54851cc98132e694d", "guid": "bfdfe7dc352907fc980b868725387e98c87ccf927389744ce531c8fea16f0415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252168f7d1981ed68d3846cde125c351", "guid": "bfdfe7dc352907fc980b868725387e986fff5aa57549513859cf0b23a29d0436", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987527b7d3752ee23c745358cc147fcffc", "guid": "bfdfe7dc352907fc980b868725387e98373b0dfbaef710487a3ba4d58f2245d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6acfe39b19553d65bb65d0c8c2b4802", "guid": "bfdfe7dc352907fc980b868725387e9884ef74f03d2096aac845af5a7c81d8dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc64eefb03fd7594cab771b8ba4e267", "guid": "bfdfe7dc352907fc980b868725387e9800cecab8518f86e0a1af8c341ff56bdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1b9819ae8efa9fd1451c50b3c44baa", "guid": "bfdfe7dc352907fc980b868725387e98bf627475b4d9accb68dc4b4f34194029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c884994c9f2f1b41a8ccf95429c3967", "guid": "bfdfe7dc352907fc980b868725387e98d37884cd0762db075c9ff1b1166262c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5881021978302fe3063ecfcd6b53278", "guid": "bfdfe7dc352907fc980b868725387e9881b4ccd046fc150cd687c7a02b8de69b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9811af2ef61d70014f1ec98968e0f4d3f1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9871e87adb666beabb3a8d8f50f068b612", "guid": "bfdfe7dc352907fc980b868725387e983df20b705c8af97e5e56ee9ebe58ad99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98531101c33f875b20bbfb213a50f8111e", "guid": "bfdfe7dc352907fc980b868725387e980dcde2a46c6be5a5ab0fbe5b4936de0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4af27c20aefa4f0b92378ab1d9ef93e", "guid": "bfdfe7dc352907fc980b868725387e98e51baf0db9005d973d5bcf0b3960ee1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a002dd2405718cfdeae0d1b43fe157e9", "guid": "bfdfe7dc352907fc980b868725387e98d405ffcd0890a6820ca24fa9cf789cdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d609450a2b54e3375a746c2f7b5f47fc", "guid": "bfdfe7dc352907fc980b868725387e9847e44e913c1c6bbafad6f26c8336e0aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4ef185499e9ce839e33be779d5fa120", "guid": "bfdfe7dc352907fc980b868725387e9882cf890d3960e3fc3ae7a455f7bbe41b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecf0c8c05df7aa6fa9e495f8c18bb87d", "guid": "bfdfe7dc352907fc980b868725387e98cfb2cb12c22253cb2aa369cd9e81633c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbbc3a3438d09e416c92dc29d049f769", "guid": "bfdfe7dc352907fc980b868725387e98b890ebffd5b19945b6bb6b378d0713d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983808212a706d00be0a5ae1cc24b5c941", "guid": "bfdfe7dc352907fc980b868725387e988433f47faf06a3c8b965a4f198a6b4da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873571283c25094044ee197e5cdc4fdfb", "guid": "bfdfe7dc352907fc980b868725387e98b83bc61f07e23694ce1d6fa646cf0e6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d44e45721f1185e571ed465ddf163bd8", "guid": "bfdfe7dc352907fc980b868725387e989b02ac18057535966649e96d0d22a7bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a5bdac2c7d54734bc4a3b8dca9ab4b", "guid": "bfdfe7dc352907fc980b868725387e98b1ce62dd750b927a29500e2b08f6b20d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec7e7bba51e6186ada1446eafff8272e", "guid": "bfdfe7dc352907fc980b868725387e9848d85649d6f59fb70bb06f2fb5f404f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f4e21f5b0e4ef8aa3cd1ed29e4c17dc", "guid": "bfdfe7dc352907fc980b868725387e98689c3b271f6e7ae77b1d5086551e34cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ad86a7b79b5b73faf08f71e7650d9d4", "guid": "bfdfe7dc352907fc980b868725387e9869e0a97741f9a01c9c0e363cc312b904"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d90debe51b7ea9acb47a94a04775b4", "guid": "bfdfe7dc352907fc980b868725387e9896ccff15ff222aec35d851a96823419f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e47978a9cc4218a4f234d8c5c11e92cd", "guid": "bfdfe7dc352907fc980b868725387e98baf02d0ce0dcfc50dd6278ad305426d2"}], "guid": "bfdfe7dc352907fc980b868725387e98f058f43412b1cfa2f72676b316aebe74", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e9813ae4d80d7e6dcf3efabadd70f0523ac"}], "guid": "bfdfe7dc352907fc980b868725387e98689e3ceb22785def0e8e3aa4a3f0c5ff", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9829c79da60965f21f0c2875b130dbb9bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}