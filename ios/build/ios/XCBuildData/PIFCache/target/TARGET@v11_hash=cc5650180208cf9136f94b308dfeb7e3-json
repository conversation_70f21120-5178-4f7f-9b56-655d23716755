{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b540d7c52b568675ab689e939210980", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a000f68c2b2f0656be5f5d10fff90bcc", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986921aa71890f8495620edf9689edf040", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab837d9488b0b52cfe2e524ade847a11", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986921aa71890f8495620edf9689edf040", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a81016cd71b93c497f32afe5bc7f06a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98650c601b3d1744900824cea61f4dff66", "guid": "bfdfe7dc352907fc980b868725387e9893c301b47159660e5b2608db015e1a8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3dc65213e3da39cef275a59e2564732", "guid": "bfdfe7dc352907fc980b868725387e982777f842603331007dc0fced67e3fb86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22fc6f51365a079d01f59c79568ea2f", "guid": "bfdfe7dc352907fc980b868725387e987360d066768697463bf49f2e9b6128cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843aa79782701c37a0207246e4fa5abf7", "guid": "bfdfe7dc352907fc980b868725387e98c8f97b7c467ea1c40aca4aed3a7be66d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bdcbe5b7726d8fcdf60afaa9a547fa6", "guid": "bfdfe7dc352907fc980b868725387e980540c58f48fbadbf67a9d07a265a3c4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986030026c720292f683972878be5e40b4", "guid": "bfdfe7dc352907fc980b868725387e9886adb77a964343f1ff08b6869a6cb15d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f102e3b3c4536ef96935d673849386", "guid": "bfdfe7dc352907fc980b868725387e987769fc9495bfdbdc9d9dd9b4c48d05f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce8f988c229420cb547af29ba4dd5dc", "guid": "bfdfe7dc352907fc980b868725387e9838aceb3184c82e731496aca7d9d070d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d406dac1b5d6e37eacb14444e792bdc3", "guid": "bfdfe7dc352907fc980b868725387e98d5adc6fd39226ff897721b6c6a3153d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa2a301f181b5451663557d0215b2c26", "guid": "bfdfe7dc352907fc980b868725387e98bd16918e6155270ec10a68fec75702ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe25f1eb5fb193501f872380b703930", "guid": "bfdfe7dc352907fc980b868725387e98fdef8520b5fb0c008198fde47253ae15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b435a6f9d33468759b17ef50290064d4", "guid": "bfdfe7dc352907fc980b868725387e987d44be7025fe8188c511911f989dfde6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5d27866836dd90bbf3cbf07b5fac14", "guid": "bfdfe7dc352907fc980b868725387e9868134ce403c36ae8a741fe17bf689e0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1e5494b41f8c037634c7b03a5113e53", "guid": "bfdfe7dc352907fc980b868725387e987b80fee53a7569e41835b1a75f33f718", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988161beff5466480777256504244b3f4b", "guid": "bfdfe7dc352907fc980b868725387e98ff62a8e1eca8ff1d80433e961652e223", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3c586f73495926fee3f23159c09eae", "guid": "bfdfe7dc352907fc980b868725387e981780dc3e5c5db454c85689bfd347c166", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc7a88088bd72f10b787722512515d7", "guid": "bfdfe7dc352907fc980b868725387e98515e5545dd5f599bc259dce9fed06d87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987afdeecb95757af6839f4259da25693e", "guid": "bfdfe7dc352907fc980b868725387e98ff970ee0fe18f3c8ce57f987141770ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c4a6cece06eefc6b36cd3b8e108eeb4", "guid": "bfdfe7dc352907fc980b868725387e9821a179d649e77ca733b33d28f05321ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d027eadad0569c6cb049e60ee6273f", "guid": "bfdfe7dc352907fc980b868725387e9898a1fc7f7e19346f7ce1e3990c5adfe4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb78bef09b21800a2933cb0b7ff255e9", "guid": "bfdfe7dc352907fc980b868725387e9842f3156d26c67e6ac1385d13abe07c78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c762866eab68d71856d799785257524", "guid": "bfdfe7dc352907fc980b868725387e9860b0017f4248052ce7e05681f0acb95f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cecb4f7f6f43c1b75c79066d3b7cd811", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cf0c41f30938755a9258f7ddd558b818", "guid": "bfdfe7dc352907fc980b868725387e98cd9ada815c0f4c07ba7d627ea8098c38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa183a221756343c92e6fa8564112272", "guid": "bfdfe7dc352907fc980b868725387e987904f2f3e7791beb53dfc1e745fcc48e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d89ea9007896cd19549803a0d937c12b", "guid": "bfdfe7dc352907fc980b868725387e98f77d509a25ab9a4a6f0d62310e4069a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac7645b050c0841ee97f05fe3d03864", "guid": "bfdfe7dc352907fc980b868725387e98325e6bf8f6daf4d97e75fccd08f8082b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616b7b8947dd249e762ae96cb833a421", "guid": "bfdfe7dc352907fc980b868725387e98e0f41285fad0596415ed0ed66bf14cc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983290a38cb10921d798f0837d0324620b", "guid": "bfdfe7dc352907fc980b868725387e98e617a0c8ca478442378d3f0d86f18eab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f205e25a0463b0545379b37fd047845", "guid": "bfdfe7dc352907fc980b868725387e9818016b0c15c4e00455e1f051d4a10ed1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bf177787a5260ac1f343319301033f3", "guid": "bfdfe7dc352907fc980b868725387e98fb80868a8b74fb0b27ff6b95eaff98eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc90b9f3f2177d64eb99044df67b092", "guid": "bfdfe7dc352907fc980b868725387e9849bd3f69b1759ac2a20709ce47eb7e1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad32f2f0465042da8cf35a05f97afb16", "guid": "bfdfe7dc352907fc980b868725387e983f7d0a0554577d6f70ffba8ed9ab5f9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46d82bfa96fb69109886e497ca72151", "guid": "bfdfe7dc352907fc980b868725387e98db023d0d5dc7ff01a4a0e7066e8c954c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d53d7c2e59239732b9fe9cc923c380a", "guid": "bfdfe7dc352907fc980b868725387e987071e79c78cb2675f3bbe638e439a2ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a39a2cf8f4abf1c7799df3d37673c073", "guid": "bfdfe7dc352907fc980b868725387e9844d1ed424378f86ac212b33a83fb543f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b201ab9bc19f1b5ed88e6f6573f26b0b", "guid": "bfdfe7dc352907fc980b868725387e98e1f6df54adb848c0f770b8c7b32fca1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ab17a86cd139abaad3df1331598907", "guid": "bfdfe7dc352907fc980b868725387e98087c6b0384971c9c618e94ea08afcad6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eeb0515e2fea4179c0b252ba377d7f4", "guid": "bfdfe7dc352907fc980b868725387e98d60704c9f198a50c43670c3098b21ccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984329b2fc58f74c4bdf3893213d3a0683", "guid": "bfdfe7dc352907fc980b868725387e980b5cba17469aab3b53cfc1ac15a84c72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df0c12e16c9482a0c4945c6ed8a8ac3", "guid": "bfdfe7dc352907fc980b868725387e98e6f91157e3aa1ae81cd1a6bd90a19e15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a6e4e5212dab636462428dbd2279c3b", "guid": "bfdfe7dc352907fc980b868725387e98be148011bcce71e1543329670e712f46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e2f5ef8728d7d1ff061cbd523ebbbc9", "guid": "bfdfe7dc352907fc980b868725387e980b710d4e4089e6ac3d70717d9f80f217"}], "guid": "bfdfe7dc352907fc980b868725387e98ba80900838d91304517d96a44b3c8147", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98465ae7802e5c5a829bca0c4da1592361"}], "guid": "bfdfe7dc352907fc980b868725387e9814f0b22c4563f8f6581cf9df749a5aa6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984d408371027e7d56d947334f38c74eab", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98e84c095ee35cc228faa9c05a4b2e21ba", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}