{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986fe280a4dd5e05ff8710952bae50cdfc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984b0ab757438cc3a3b57b2e1bcc81986c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bcc03320811b8960d1e3e5679997b47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f83688a6d6dee03c93a34cfe1200711b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984bcc03320811b8960d1e3e5679997b47", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeApplePay/StripeApplePay-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeApplePay/StripeApplePay-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeApplePay/StripeApplePay.modulemap", "PRODUCT_MODULE_NAME": "StripeApplePay", "PRODUCT_NAME": "StripeApplePay", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a52bc3754917b978601b28bbc3aceb58", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847a646dd1e8883c0def104b524028264", "guid": "bfdfe7dc352907fc980b868725387e98a45157f0c644a5b1ee8cd054b754f78c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2dc21fb56fb3c84ff8fb571a160baaa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a894dd8c203dbfdd39a34303cacd7368", "guid": "bfdfe7dc352907fc980b868725387e98355a987c9b5121c851d589aab50958a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813801daa3805f1c5ff3830b6dcb82361", "guid": "bfdfe7dc352907fc980b868725387e98a74f479e7a0d1b59556f9a3e3544562b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a00b83c4a8ee30ebb172a5d74bed2f", "guid": "bfdfe7dc352907fc980b868725387e9850986f4742293e564568cf9ce287e55e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b56f16ecd5b62235b6bb21a1058d9ee", "guid": "bfdfe7dc352907fc980b868725387e9899d20882bab50f5459aa61afdfa61070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a11bde28d41001bf819eb7612d970123", "guid": "bfdfe7dc352907fc980b868725387e988c4e7a7a46d41095653c77f484dc9411"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f783fc72cb9cddec7d0754c7f136a0", "guid": "bfdfe7dc352907fc980b868725387e980e877fa15119034afbb65e9f2379ed6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827388ce376aeaff9a7fc696da3658983", "guid": "bfdfe7dc352907fc980b868725387e987219733ff14e6eb9b5c3cb681739a516"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ca9eddd9091cad14b47f8323af1b14", "guid": "bfdfe7dc352907fc980b868725387e9802b82eb5c3dc924f8510a2f7b4e4ffa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983911421a961862c8ce1af894a03cbb0f", "guid": "bfdfe7dc352907fc980b868725387e98447a742aae3fa4a5de5e5c5a14bb1612"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b03f9c84a04e0ddc36f7f1f057a5a8b", "guid": "bfdfe7dc352907fc980b868725387e98a854034a7e5fe4d812b66612b654a61c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983766a54e2d4fe2ac545ca04cf62f98ac", "guid": "bfdfe7dc352907fc980b868725387e98caa5a7926240493c1b63ec78efd711c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e5c67cd686f6db2bf84b743a2197c66", "guid": "bfdfe7dc352907fc980b868725387e9829d12636534ece9602d147a5ecded0f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d229d7a8a00bee95cbde487b32653938", "guid": "bfdfe7dc352907fc980b868725387e9832243a3e128196af7fddb66d7e3ed379"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f17b0d3221f90780d199b50045018611", "guid": "bfdfe7dc352907fc980b868725387e98612b1504f1f2c3f5402ef1c7d5105493"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26312bc0e471291beee63f7dc8b6662", "guid": "bfdfe7dc352907fc980b868725387e9870a9fcc123b18809069c28b9d3187f94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd533bdd6afc87422b99e38b34fdd2df", "guid": "bfdfe7dc352907fc980b868725387e986fcd66d1164214c71057440ab69f2b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876bf0b129f2e2f9ef03a3c67a84f726a", "guid": "bfdfe7dc352907fc980b868725387e98b3ebd100eb77a78a8f3bd8c998bf5267"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981efe743c28bf6446d89bd76969532aa9", "guid": "bfdfe7dc352907fc980b868725387e983994253cfe8b75417b63b38e830ec5ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed6370de002a73c207f54c00e06115ba", "guid": "bfdfe7dc352907fc980b868725387e98107428b97988bd29c988b2f9e2d89ccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cb58fabee0f6e303c6dc7b09b6e8870", "guid": "bfdfe7dc352907fc980b868725387e987cf3db37af80cca7db9ff191f3a665ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ebef88618fa3f1c25fbb3b07310627c", "guid": "bfdfe7dc352907fc980b868725387e9842e3f953004f374be63357e8f9836684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c9300e551f56d7daa01eaad8c6d1996", "guid": "bfdfe7dc352907fc980b868725387e987e4bee413357a871489fd1aeddc5ca87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0c1541aa3d3a2aa883f50527cfa4dd9", "guid": "bfdfe7dc352907fc980b868725387e98ecfa3148a252b0b29d2478842052cce5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f716df88eeab8a547ad76a391125c32", "guid": "bfdfe7dc352907fc980b868725387e98a582067625a73870b73f3f15db29148a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980345dcf3ffb0253bfc968cb761e07608", "guid": "bfdfe7dc352907fc980b868725387e98d0b0c699a5bf2ef23178011167fdd884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804bda9395e725b9b8494466da60ce427", "guid": "bfdfe7dc352907fc980b868725387e98ef37c9773f7c2cf8ff04072fcfab5374"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806066676f658d17ed96f9db91ea3cf27", "guid": "bfdfe7dc352907fc980b868725387e98c9f21996aa10051e9e2c9ba35fb216c1"}], "guid": "bfdfe7dc352907fc980b868725387e98a201f9ba62c7419e0aad9cdff7a294e0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b3660ea04c3363bb1efd95923762b7a5", "guid": "bfdfe7dc352907fc980b868725387e98f487f9fa584cec68a9cc2ca589ac4216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897bfbf3410c779f9c859ddeee5e83668", "guid": "bfdfe7dc352907fc980b868725387e9811f2b191541646e7229b64c8c4af849f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98f780bf9af1399b181c56a4ec12f647de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a67f8030f2d17138a1c1d4f723d2f69", "guid": "bfdfe7dc352907fc980b868725387e985cbdaaa2684d7abfeae933f0b16efc6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e9824daf3d0985931f9fbde7f611ad9a4ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503ad53dcdb8bc9902dcc103f84d66ae", "guid": "bfdfe7dc352907fc980b868725387e98e69e3d0991e5cdff964de063c872d1b3"}], "guid": "bfdfe7dc352907fc980b868725387e9898ae2e5ed5afb702a53b33c8cd7ede58", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984759906ef72ea418adb72d927e39188f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}], "guid": "bfdfe7dc352907fc980b868725387e9864c30109ee71434e4e716d99f4166e22", "name": "StripeApplePay", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e48a873b6e297ebc2a87f23eb2fd0723", "name": "StripeApplePay.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}