{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983779570fafaaaa2554fb73b8b8a224fb", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806e64984a9ac6e65b6855b8dcebec6a4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866046b25d593b7a860d0176237bb0a3f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982b1bc0bc84db76b77d8c069e74dbcd5f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866046b25d593b7a860d0176237bb0a3f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983894f951e814179768ebb8184160c8f4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987f8303d876e740bc02b10f8d3d064f6e", "guid": "bfdfe7dc352907fc980b868725387e98f70036d6122c031305aaa3d961a7a1a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797648db15a3c758096795e43b42e9a6", "guid": "bfdfe7dc352907fc980b868725387e983f9e19669bfef2019d6e7203f737bc31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858cd4556e3aea5f15a30f20249b681b4", "guid": "bfdfe7dc352907fc980b868725387e984ffdbd7be957d8c556c03e7a6ed85f11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44f2929324dc746890adab79c9e0e72", "guid": "bfdfe7dc352907fc980b868725387e985c44a6a13452f8d25aed1ebe19bc04cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e49aae80445880bbac685b340c2e11", "guid": "bfdfe7dc352907fc980b868725387e98b6d5b013023643b1dfec1cecce8e632b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b635a81db34ddf9266d2ba34748a739e", "guid": "bfdfe7dc352907fc980b868725387e983729c1fabb43f5a4f8f8684b8d582924", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891c09c87972c1345c638b89ee6197ecf", "guid": "bfdfe7dc352907fc980b868725387e9886d0f03d6187269a6a70268a4b4a236d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ea2498ced0994ebaa93bbc3a42afed3", "guid": "bfdfe7dc352907fc980b868725387e980e2ca8631a6ec5e6b67537d40b47b041", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5cd053579018aa5a46b9ee9a00106dd", "guid": "bfdfe7dc352907fc980b868725387e988af158246774a3d0ed36f4ad6b4235d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef7f40b91f724730cc937324e084a79", "guid": "bfdfe7dc352907fc980b868725387e980bb2eb03071d55b4f8578da65a79ae37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b0d762ee31d9aacfd2a6f7d4bb30a76", "guid": "bfdfe7dc352907fc980b868725387e982578843daabeb0c975b5af46d5830a82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d50202dab8f889e10f760ad7095ba8e1", "guid": "bfdfe7dc352907fc980b868725387e983a7d122fe360c1252cee19460135f9f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b1e5f660b95f9b8258e97fb547db90", "guid": "bfdfe7dc352907fc980b868725387e98d5591846ddd2ca0e4e7fa8ed11354dce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee9e5ae514ce86e5cb40b4136190e09", "guid": "bfdfe7dc352907fc980b868725387e98fdab57faf91575657435e62c5fbef51a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961bf2bff9cc91d5d23a4578f2f12170", "guid": "bfdfe7dc352907fc980b868725387e98d46b1a1c2068c236063c00f61b1caa34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33e4180bd9c5e6a3b787ae527ae2a36", "guid": "bfdfe7dc352907fc980b868725387e985bc9150e2ae14477c9bfd77ebe80652f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ea28bcad239c01032107de02b9b418", "guid": "bfdfe7dc352907fc980b868725387e984a9c89fb7afd20c419138dc6e384f217", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffbcbdb1c8802b97ef3583d3bed6c759", "guid": "bfdfe7dc352907fc980b868725387e9839c7529cab6f598cdea901e3d3d4e615"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5b4cda4a7b2d6f8b45e188356178a06", "guid": "bfdfe7dc352907fc980b868725387e98fa290b41632a9e0f49310860cbc3811a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981032ea36d2223c78fa4959f5d614c2df", "guid": "bfdfe7dc352907fc980b868725387e983659f4791d989c426d4a32408ac2b0e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875969b194053e4b525a0e814c4e61107", "guid": "bfdfe7dc352907fc980b868725387e98e799728a1118802d9e3c15dc31aec1aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa43d6a6a763d651a297232f0913851f", "guid": "bfdfe7dc352907fc980b868725387e98b8575a4b42122a4fb068c3b71fb35bb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d137760eda0a90ad484346ea848ad7", "guid": "bfdfe7dc352907fc980b868725387e9851c7d2a7aa132e23071c5172c0dab4e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811fb5a4d29cb22233e0243743247ca28", "guid": "bfdfe7dc352907fc980b868725387e980c16970c61323dcc670cf12706ca55d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854945f1fd74d3bdcad39eb621c78e358", "guid": "bfdfe7dc352907fc980b868725387e98d216076cb7f2602e50f982b4dcf09be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f540999bce4d72c97ecd69ec686e69", "guid": "bfdfe7dc352907fc980b868725387e98f82e005777c3c155a3f7ae1db5a4745d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839780faea07631f6e99aec1072a27fb1", "guid": "bfdfe7dc352907fc980b868725387e98452cebaa19b4c5850d558aa3bbfdab6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053400079b345c1373147c3bd8494bbb", "guid": "bfdfe7dc352907fc980b868725387e985db33631aef3bad7814a287f45c86341", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a43a6ff22a45bec5ffcecf083b30c4", "guid": "bfdfe7dc352907fc980b868725387e9870f6197305005aa8f0680b4089a22484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985148834296788f8f8dd4a43f1adcbce2", "guid": "bfdfe7dc352907fc980b868725387e98a1937fe0c691d217b3cd090426c86e04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a9a29c82f100cdb14f6e47b3e5e892", "guid": "bfdfe7dc352907fc980b868725387e98d9570796149728095528c31898a09d65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b5264ef793250aaaf81e6cab3ce4e0", "guid": "bfdfe7dc352907fc980b868725387e98adf964ca9939b4a4d4c496c5e272dfc2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989553ee11b492848b218551f704c693ce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e240337faa9e4c0ad65a26bd5f135c7", "guid": "bfdfe7dc352907fc980b868725387e98a75d7031ff8685c56055b57e0388dae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d086947ea951bd309c04a0c60de5c6a", "guid": "bfdfe7dc352907fc980b868725387e98e73fd7bde73a3cde168a86618f6fb82d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9615f5b6aa5bb141f615704b1f7d8ea", "guid": "bfdfe7dc352907fc980b868725387e9816bf85029ed5de74e4ef1c0a173942bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf861553656763927c0dadcb75839e33", "guid": "bfdfe7dc352907fc980b868725387e98df254598d0d5b5d867068a20b511e930"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987635f487198a6435d5b11d3ee382d7d4", "guid": "bfdfe7dc352907fc980b868725387e987ae2348fde22c28b1f12536efae7ea5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98078f02df468b9d13aa75604e6852df9d", "guid": "bfdfe7dc352907fc980b868725387e982efcdd405247b888431cc8b0784c518e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981777b85f5ee80e6e8d306687c23106da", "guid": "bfdfe7dc352907fc980b868725387e98daad58cc4e15a7ffa7ffdacf0e0af7f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4ea4a3766e946a12be490309eabfda5", "guid": "bfdfe7dc352907fc980b868725387e98926f83cfa3f8e7988a79febde7c509d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d3b170bb702304215668894868eee2a", "guid": "bfdfe7dc352907fc980b868725387e98f905507ac3935ff895c5d0e0ee3ebd4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989291b03b88d182e79b808f0a1b1b4c16", "guid": "bfdfe7dc352907fc980b868725387e981919c5bbb0d09bb8c9ca8557e7564545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840dab3b20601af29fc8bb56db0f4ec18", "guid": "bfdfe7dc352907fc980b868725387e98eeca0f51333d1166189a42629b5f9b9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f4b7d15535886f16d0e14e5772a4712", "guid": "bfdfe7dc352907fc980b868725387e981a0cabe751848c1fdb597f6bb99754e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ed101d65262f8e3de07bd5f9a4ed14", "guid": "bfdfe7dc352907fc980b868725387e98940b5a3ba96275c29ca347f34f13f4a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986778f95ab3dbdb84666a5acf10f38230", "guid": "bfdfe7dc352907fc980b868725387e98ef16a4c62b9bf44c73acf55028cc1141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985355a04daa3d080c0ee17d4ab2594056", "guid": "bfdfe7dc352907fc980b868725387e98f767a33b26ae882106d4d3ed1841d3eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98557a8a374c6ac92538badc6008fac93a", "guid": "bfdfe7dc352907fc980b868725387e987530041b2cc5d297422217b71b612a3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c6bff5735fc769cd94bc6baba3160fb", "guid": "bfdfe7dc352907fc980b868725387e980ce6697a3a6938eeed9142f0e5bfa628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836d9c166cc55fa241ef8800af0a8df65", "guid": "bfdfe7dc352907fc980b868725387e98b1d767e7a183b4ba1d0eea9d46cd5743"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1208e226a88d9f7ab289dff68a773e", "guid": "bfdfe7dc352907fc980b868725387e9897034d94b6a7148ac28bce430e741a0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b799d110403efe4f2b435ea692be9fd9", "guid": "bfdfe7dc352907fc980b868725387e98604643ccaccdfd3efb8c841eaf3923f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf7e46449a19271fadd993493b0d984", "guid": "bfdfe7dc352907fc980b868725387e9867ea6780b18873d1a2a06bf24218ec5d"}], "guid": "bfdfe7dc352907fc980b868725387e98ea4f4781eee075b7d97a148b37567825", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e98d32500001b88844bcd5930955158b78a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e9865a791f4ab0e79608623e134e2b369fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e736fe3cd34dacbc42b6af920b1000bd", "guid": "bfdfe7dc352907fc980b868725387e9866c873e0e633695b2ab1d4451fa7f56c"}], "guid": "bfdfe7dc352907fc980b868725387e98f9698433ff2258ca5084bb456f76dd8a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c649fa287cf950696c5dfe68dfc6081e", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98eae6dffce94588f8a795f37fcdf26fcf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}