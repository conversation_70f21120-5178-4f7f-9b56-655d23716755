{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b1f20e7d9179f0240607eba1596bb14", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9861c540f8222359c38412f277e5312eee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9822b568a60c8d9766cd760a965c41fddf", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9895d2516beee56c1a0ff2950d27b317af", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9822b568a60c8d9766cd760a965c41fddf", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMAppAuth/GTMAppAuth.modulemap", "PRODUCT_MODULE_NAME": "GTMAppAuth", "PRODUCT_NAME": "GTMAppAuth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e3c66224a8b47fd7b9621409382b40d6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839509fba597e0f9835a8e6bfc4358aad", "guid": "bfdfe7dc352907fc980b868725387e98f653b779d3aa4b4d616735ec674c955a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98834a38fa82f9243685f6df7020fd99db", "guid": "bfdfe7dc352907fc980b868725387e9801878c34a02e7f52094d77a3a0f29d10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980291dabeba78f03168022e8802f14b1e", "guid": "bfdfe7dc352907fc980b868725387e980f5b1a836cb81ed64d833d9f956d0f06", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e970f9e85e1e3d1b75d26c2232cc603", "guid": "bfdfe7dc352907fc980b868725387e9858729779693e6e5b15e0b7f354cd2475", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875459346f610d53ab1cddd730d6f1807", "guid": "bfdfe7dc352907fc980b868725387e98409bad8192a7e81c49787203d5329c25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983323d24e99b0f99507061986e68fb719", "guid": "bfdfe7dc352907fc980b868725387e980ba6c01e8ab0b8eaa9e9bb5f30fdd12f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a23d22fd8a2c1674df59c40f90231d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803bfc9358481003ae91b42e50e99f43c", "guid": "bfdfe7dc352907fc980b868725387e9851000d9f6dce91c3969a4d900074a38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8a8a355649fa0dd1038ef308ba1184", "guid": "bfdfe7dc352907fc980b868725387e98354dd1b120b7a830c78fc198b9effedf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cad5c53c011c8d5f5440ea218add1174", "guid": "bfdfe7dc352907fc980b868725387e98f52e530a2e647531278e658f4c875b7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ab1692ea63dc482061145479d73fcf", "guid": "bfdfe7dc352907fc980b868725387e984b3c50c88597e9ac9f943125ce4efb42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e73389350e9e078c5ea1d1491faa51", "guid": "bfdfe7dc352907fc980b868725387e98cc00a655df3e7826c633ad2297243f98"}], "guid": "bfdfe7dc352907fc980b868725387e98cd665f13306f0429a977452e4ff1ca11", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e9899b743792cfbe93a06b17fae12f81508"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981399662ba493774970bc5d0e2361ec15", "guid": "bfdfe7dc352907fc980b868725387e98c19a70a8373ebef930ce15c24240da02"}], "guid": "bfdfe7dc352907fc980b868725387e9804f159e44e5b4da5984600a69a0e2700", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9821a2e00833e0aeb0313f22d61ed3e160", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98855fb84830a2ff40ce73a17fc283f650", "name": "GTMAppAuth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}