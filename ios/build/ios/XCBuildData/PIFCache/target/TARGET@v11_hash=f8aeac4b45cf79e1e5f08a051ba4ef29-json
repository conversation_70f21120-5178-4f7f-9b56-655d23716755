{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c2f23505b32bc57b07d04ddecee27d56", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9899da30541e80d637874b8584ca6c3bee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987315b04f39140b12301e3d4851059572", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98189fcba0ab810bd9b03e5c0b49db543b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987315b04f39140b12301e3d4851059572", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseDynamicLinks/FirebaseDynamicLinks.modulemap", "PRODUCT_MODULE_NAME": "FirebaseDynamicLinks", "PRODUCT_NAME": "FirebaseDynamicLinks", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c034f483225db4010a864b48ee505a2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987d7d216e355a2b70728f0b16208a57ee", "guid": "bfdfe7dc352907fc980b868725387e984055ccb39172a8f3bf7af1e02f011d61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868a991f1a8e55b5670385f40f085652b", "guid": "bfdfe7dc352907fc980b868725387e9893474550d18e6b589ed97b852f16c538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c228ab01b530ade771694cfea865fa1c", "guid": "bfdfe7dc352907fc980b868725387e983cf8eb63eb3c4037e6273f6315416c43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ecf181a7eb52cf6e04027b0f264716", "guid": "bfdfe7dc352907fc980b868725387e98e344721febf37deee7ea86f331b765bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84f131f0557e0c161da3fe420415003", "guid": "bfdfe7dc352907fc980b868725387e983cd29faeb7f6aca1966cbc511f95278d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f601fe1b1bbcbe0ee1da3183cf7cf47c", "guid": "bfdfe7dc352907fc980b868725387e98cf2eb21bb4453d0892e571fb30eac0f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef9e872fe20a4fbdb5dcd62f1c56aab", "guid": "bfdfe7dc352907fc980b868725387e988879d5da7d84b3914ab4f719f9995adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984093ef55cddd006ef0661debcd10f610", "guid": "bfdfe7dc352907fc980b868725387e980db8f2e39d1a734dc601dc6ea9644a73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e9fe8f8a055ca34adbf2751818728d", "guid": "bfdfe7dc352907fc980b868725387e981b59ac076c804e9a8ff186aa30ecdbfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e8c77f99166c15bd1f0c6df81567b9", "guid": "bfdfe7dc352907fc980b868725387e98b612b51fe010f6d7c14ed01bf5753cf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ec66f59195a8948b4f706fc9b9df23", "guid": "bfdfe7dc352907fc980b868725387e98f9948212de810a212703e8e01ee673c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e14f3556d431631d4376cdd9f3bc7b6b", "guid": "bfdfe7dc352907fc980b868725387e98626be6cf235cec6bc1e29fe60df48d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a488427c40993a740b9ff820b89ef1c", "guid": "bfdfe7dc352907fc980b868725387e98df40b180763f8f3455e509cc82e03d6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6af1b06d977eb5c637bd890b8a75e55", "guid": "bfdfe7dc352907fc980b868725387e983dd07834bf4196cf091ca5ecdb2a98c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdaa62f69d63a08e9a32355d02732edc", "guid": "bfdfe7dc352907fc980b868725387e98c1f7ad54f113874868235adf6b075f98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6b5f50323288b21636872ef43e25aae", "guid": "bfdfe7dc352907fc980b868725387e985305dc3c0d3012b5f2de35bd98823c4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4acc40b5a4692e79c330798263a3f32", "guid": "bfdfe7dc352907fc980b868725387e9812efa890d0b80be45acf2eec14247af9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f457413435619d6bd6f44197e9aaffb", "guid": "bfdfe7dc352907fc980b868725387e98e8b668d04730126d3099a0514f541b2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986596da5611395d8ff760eeb3245f84f6", "guid": "bfdfe7dc352907fc980b868725387e98e1941e3f25cd4c94b50774a532078002"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899880d5bc5a9c739b0ae429567e61c3d", "guid": "bfdfe7dc352907fc980b868725387e9849f8f74d986e1ba699c102c820e3661b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b35b661f55fe2d460d2c7f7e3d802d37", "guid": "bfdfe7dc352907fc980b868725387e98642cd50857e19320a65baf44bfda5836"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c1ba0937c4f2ea03126db2abbdcb69e", "guid": "bfdfe7dc352907fc980b868725387e989f9da1b4d3cf0deb70547a77c2548fc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aaffd6c22589e57fa6da5fab0c4bc3f", "guid": "bfdfe7dc352907fc980b868725387e982256da5b6e1c0f5dc2e9ad2bd6edb226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acee1284eade76c338bc95cfde075df9", "guid": "bfdfe7dc352907fc980b868725387e98a9179acb65b0a4a67586c359ea5b115b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab54db07e81425756362225d346d4749", "guid": "bfdfe7dc352907fc980b868725387e98a7a489242226fe192d8138aa2b5d55d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8bd56c698a6ee25e038c6155e30e570", "guid": "bfdfe7dc352907fc980b868725387e98e03e21fbe87ded7ed81a829c4f612598"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c413717fe9ee639eb3a9cab1e51ec9aa", "guid": "bfdfe7dc352907fc980b868725387e98aabb7dc54e8edd9fea2d220fbafab112"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c84e4e13ec47327c893f5f10a0084611", "guid": "bfdfe7dc352907fc980b868725387e984ef2a169346ee0a357efbdea0c0ec934", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9689950870a684a8fe6201e040f8d28", "guid": "bfdfe7dc352907fc980b868725387e98f179d598db6ab84b5d22b6df6523ae61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51e9d70baa2827e1511d7894ff7b837", "guid": "bfdfe7dc352907fc980b868725387e98834936ed3e947dc353f5c30f0563a8f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f83912b9803475ac5a486529d5f789c", "guid": "bfdfe7dc352907fc980b868725387e987531837300fc82a8a8ffd9ac6296e6c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc92276e699aab90ab28bd25eade5ff", "guid": "bfdfe7dc352907fc980b868725387e98b362c12dc302de9782331aca3c34e7ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef5d1f085f8a6c4573537f5475666fe", "guid": "bfdfe7dc352907fc980b868725387e98bcf9758f3e9303b541a30819aec26335"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858a665b41a29acba1983572887852e82", "guid": "bfdfe7dc352907fc980b868725387e98bccccd62b800e424774254065ca1bfda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c15ba719bf91740e8304d31c3b87e97d", "guid": "bfdfe7dc352907fc980b868725387e98cea0f67de8d6e42c5309bf27cb6583ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98167e832342c511d4650db287cdfda154", "guid": "bfdfe7dc352907fc980b868725387e9836ea87b4cb607a00adcc7960e9f71126"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ff67cc223cd44f769932f87513c398", "guid": "bfdfe7dc352907fc980b868725387e98b54aceaf094efdb8ecdb2a4b040d12ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af958f9baa7f13a5ac132ffc2b9871f", "guid": "bfdfe7dc352907fc980b868725387e98c84be88e29febaebab93bb5512d5a6f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98661923cded55bbf04a9896893c0bd11e", "guid": "bfdfe7dc352907fc980b868725387e98941428542aa7544f188cd4c54a0cb880"}], "guid": "bfdfe7dc352907fc980b868725387e98dec7cb3ed92d824616c9d91f93facb56", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d1d0a3a67f7051a3a368ebc71b7b6c70", "guid": "bfdfe7dc352907fc980b868725387e98cd2966511fd33ea7e04df9330fb6461c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0190a3de83fce3168c4da1932e05402", "guid": "bfdfe7dc352907fc980b868725387e9873f7146848b0abf915ccc59d2b1d8b27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98270f5414e4979169e96929e6a8c5ce65", "guid": "bfdfe7dc352907fc980b868725387e98a304ae6dd8410bbfd7d6533b3c68e09a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819f82c60e0584a8a02ca510ed4c63d42", "guid": "bfdfe7dc352907fc980b868725387e98029e00351da314abc09f2343d9c0242a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf1209eca8c12f24ea5ff79107c6397", "guid": "bfdfe7dc352907fc980b868725387e98e529cb2dbbd9769b499a2a3c7421926d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fa36e041d7da99f55a2dab77bf0778e", "guid": "bfdfe7dc352907fc980b868725387e98cadec2ac1abd1251fce2a74a2a242f2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981210a64550261c9de13f5efad63269d0", "guid": "bfdfe7dc352907fc980b868725387e981e62b22b0cf7794b5a3504d5e29cf377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe55e76aa229b0a556fe835ec63f212b", "guid": "bfdfe7dc352907fc980b868725387e98dca645496f79fc9f0caef9af2f4cf39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844217b84e1b78bfeff8e31fc76db601d", "guid": "bfdfe7dc352907fc980b868725387e985ad893479e76ed065f8bf08ea41491f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98365ef19c68176f89f438b9082814bf83", "guid": "bfdfe7dc352907fc980b868725387e98d26c813448ae78379686d09d880b789b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98559880a1e75a05367be587152b897709", "guid": "bfdfe7dc352907fc980b868725387e98ff3871859e53a41b91e466e89f197768"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ad03fce028f5ac984d4fe07815c1da7", "guid": "bfdfe7dc352907fc980b868725387e98cc7cc6affa5c5f4a73b114bc79095f53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98856e4855d0490ee82b86dcfee7eb9f90", "guid": "bfdfe7dc352907fc980b868725387e984feaa150d93120cb8c0ce8e5ea75e085"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98077eb38900ef84dae1d7b0b63eb41c34", "guid": "bfdfe7dc352907fc980b868725387e98533ad5b8836caaa9120a678f4b597d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a292f65051f3f0fd9208478e1ca94e", "guid": "bfdfe7dc352907fc980b868725387e9857aa8881215af2ebecbd22ed043ff846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0d96bfe88c93464fdd892e71a9c4675", "guid": "bfdfe7dc352907fc980b868725387e98926e6596f0e853e3b2b670dc06154db5"}], "guid": "bfdfe7dc352907fc980b868725387e989f6d92543efa3ddebc174a99b1d5c975", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e982b00ecdf8ed7738e9bc9c63c7010825a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f3385c3fc924ea7214cb0fc98a1a262", "guid": "bfdfe7dc352907fc980b868725387e987b9d646fc467c3d583f3f0300e97001f"}], "guid": "bfdfe7dc352907fc980b868725387e982629a8c39f9ae4364600eb367bb614b9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982e5c366331c3a0eb8eec0f1d20c90060", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e98b623f22c0d3d037d0450c736133d3c3e", "name": "FirebaseDynamicLinks", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98834460d3052fa1f4117c6696cee22bc3", "name": "FirebaseDynamicLinks.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}