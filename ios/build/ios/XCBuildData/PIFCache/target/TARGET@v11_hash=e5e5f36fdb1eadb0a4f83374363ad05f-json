{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860a622f7807fce8f920ee972fd5c1beb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeUICore/StripeUICore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeUICore/StripeUICore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeUICore/StripeUICore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "StripeUICore", "PRODUCT_NAME": "StripeUICore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9826ec43c89da96eb1d4312d6d13aa718a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c540b820b3dfcb6763f693205528754", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeUICore/StripeUICore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeUICore/StripeUICore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeUICore/StripeUICore.modulemap", "PRODUCT_MODULE_NAME": "StripeUICore", "PRODUCT_NAME": "StripeUICore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f14eade5e32f59d85bf4db75c85978dc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c540b820b3dfcb6763f693205528754", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/StripeUICore/StripeUICore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/StripeUICore/StripeUICore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/StripeUICore/StripeUICore.modulemap", "PRODUCT_MODULE_NAME": "StripeUICore", "PRODUCT_NAME": "StripeUICore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837966cced162415d715788f861405383", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981b06db66b6acb7bb16962a41f8479827", "guid": "bfdfe7dc352907fc980b868725387e9853b3956ec9b6ba6152682f7416af13ed", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9800832b5563d491b6823f4ea8125075ee", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9882541311ff7495a29a27d8e1500076f3", "guid": "bfdfe7dc352907fc980b868725387e98f60876ccb09e6635789a10bf79e88361"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8dbbf55e4fb5e32bbf1a27cc5f3ad67", "guid": "bfdfe7dc352907fc980b868725387e9870c48374621995a8e9c00fc50803bef2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881263c72cebf19d408d695e859888896", "guid": "bfdfe7dc352907fc980b868725387e9854b1c0c33e8d07ddc25a2904f4a08385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c4f0b90455a62e65f839d68288e291", "guid": "bfdfe7dc352907fc980b868725387e985533677ebcb329b5d3d64cf85734a450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ef6a35fc09336783af36448f275381d", "guid": "bfdfe7dc352907fc980b868725387e98762106790e09d3fd660857565a67d88b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98297fa0a00d0c3f0dd6992f0b8c4bac9e", "guid": "bfdfe7dc352907fc980b868725387e98f34982136d527dcb6e3dd1ded87332e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d530ea09883336754cb48d263a80bcda", "guid": "bfdfe7dc352907fc980b868725387e9838c11ceab55c09871458a87141a47723"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98592c85704958120001a3f14db135d568", "guid": "bfdfe7dc352907fc980b868725387e986cd655d274189cf28009ef49b6b510da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f7490f30139ee6e0d50f57121923abd", "guid": "bfdfe7dc352907fc980b868725387e98414b6e0ecac64087dadda1bcfaf6eeed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459c829700fa9c4c6f997b2cebe5de86", "guid": "bfdfe7dc352907fc980b868725387e982de28f0de19f7b7bc39ea3c04b1b306b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bba1605f311182ed8dc09df2f6637e9b", "guid": "bfdfe7dc352907fc980b868725387e9883b8267acaa50de1f6efe0bc3c26e0d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987475ff34afa0bc7e409d37cf4ff03ef0", "guid": "bfdfe7dc352907fc980b868725387e984dd478c97885edf9edbad8bc125d387c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf4e9a42f14494b07c6d2e8551b926a9", "guid": "bfdfe7dc352907fc980b868725387e98db557b4b8dc3ad584e075c70b585b6d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee8bc0ed593ed17d94edac1ba72463aa", "guid": "bfdfe7dc352907fc980b868725387e982044b4fe300d38eebab694521fee4de2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0e1a7b681e4ea719283d656b6bf7425", "guid": "bfdfe7dc352907fc980b868725387e980ba8a2c34adc2e534c3cbc35d487ff91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e00b9d3b85ff998aca8ec7b46cf6b0da", "guid": "bfdfe7dc352907fc980b868725387e98d9c6d512b8f31d516d6daee423a5627d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5e9ecc9a7984d323060842e1705b38d", "guid": "bfdfe7dc352907fc980b868725387e981796f63ae90773b44dc394688c04b858"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878bc59cbb858202cc834cfe3f4bfc572", "guid": "bfdfe7dc352907fc980b868725387e98f4195357da55c776c0cabdb4f2016193"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c162698f84fcedbeab49e597dc83d9", "guid": "bfdfe7dc352907fc980b868725387e9812bbc8f1ea9bb816dc38b09621aa7c57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e32eac8bfd5e382bf9a9b1fa343a2c", "guid": "bfdfe7dc352907fc980b868725387e9817056cf068d23569745214818d861f1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f3b71174cbd2ee24738d3284d23e269", "guid": "bfdfe7dc352907fc980b868725387e98807e3a2601aca24657ce3c74069f55d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894e1611721375a47ac6970631c17af3c", "guid": "bfdfe7dc352907fc980b868725387e98bb1c6c552abf061c2f196817cf151ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc360c13656eca79aea03dc5faf9304e", "guid": "bfdfe7dc352907fc980b868725387e980b469602daed8fa451d50a0e80a15aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e19ce5e1c2e56211395479f03bf2eeb", "guid": "bfdfe7dc352907fc980b868725387e9858e8790f3914edf67f647024e2033d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d072b32d0c571e6e88514f3e970368", "guid": "bfdfe7dc352907fc980b868725387e98db380a129ff2db4f8fbe2c12c8be8977"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553994b81ab796c4e7271ea2ec683db1", "guid": "bfdfe7dc352907fc980b868725387e98b2d63c1c1e1ae34e68a6359e4add6902"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986378f48e6e10ef7d5a3d28771669f2bd", "guid": "bfdfe7dc352907fc980b868725387e988293c49b99e1af4834c395a938d840e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c7a0cd6e20c4f4e6b998ae3d554eac", "guid": "bfdfe7dc352907fc980b868725387e98e8b56ac618efb2c3b885e7a932e21c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981320818d54fa7fee8cfecc6cb6670b77", "guid": "bfdfe7dc352907fc980b868725387e989894fc2a8b00361a30fbd08ffa6adb32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd956226db2d69c3eb17331e66d62dd", "guid": "bfdfe7dc352907fc980b868725387e983bade5378dec4bd2820819a9f1f53dc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982056e479c3d6c4166cb1b7d3553efe7f", "guid": "bfdfe7dc352907fc980b868725387e98373cff8cb19a4dd3e4115eaede7ab2a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1fcff8e26f7f82f70da9c3da2ac98b", "guid": "bfdfe7dc352907fc980b868725387e98026e10959c07a660b65a70ebfafc11d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98febd469d5bf9cb0afa0b49bd4875e2a4", "guid": "bfdfe7dc352907fc980b868725387e9899b37a9ed7127d192e2fae65b3d12162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506f5eddc1438a5241ed594359b5d9ac", "guid": "bfdfe7dc352907fc980b868725387e983e0ffecd95eb2b5fceb2b73e45675e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bc1239efd908d0f0ec42dd9eca7948f", "guid": "bfdfe7dc352907fc980b868725387e9859f52728e6e6afa3bcbaf24f6db5669d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feaaf053e6f5c0c8ee3384664cb4e156", "guid": "bfdfe7dc352907fc980b868725387e98d85359816bc6c00ed3ee1e6370b4b4b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1dfb15c9321114d09b7fcf7dcc1ac03", "guid": "bfdfe7dc352907fc980b868725387e9897b499ba5af8d84114a294df001b1022"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f20f98e753efd3ff40494f5b030e6be6", "guid": "bfdfe7dc352907fc980b868725387e98deeb5187e32c814b118a2c1d94736345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b4f93b36ffdfe58cfcf5874741cc3ec", "guid": "bfdfe7dc352907fc980b868725387e982a72095e5c53032b62fde93dd319af08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdd2d0592763b9da5b4274dd03118b21", "guid": "bfdfe7dc352907fc980b868725387e98c6436ac4673a865eaa3e390ab1961642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8ec7109d582f831620075b9e4014727", "guid": "bfdfe7dc352907fc980b868725387e98cb1304801852b7d71a1a1774acc58bd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f81ed83b5779c1c120c2dbf018046de", "guid": "bfdfe7dc352907fc980b868725387e98222a13430b336ef914f9e364f29133e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb91ba4a106068d1ca8dfe9bac078f71", "guid": "bfdfe7dc352907fc980b868725387e989b7bbf2cb50f6c41d4bea44b10ba64a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdeec6ebbbacf7cf32b668db563bc926", "guid": "bfdfe7dc352907fc980b868725387e983d108929a19c85a7b9a371fc6face0fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2eb24e249f834acce0a591756b1f09a", "guid": "bfdfe7dc352907fc980b868725387e98db7cda5ca875385f41520c6760b7b6f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98131fbf5129ee749e9010616b7a066808", "guid": "bfdfe7dc352907fc980b868725387e9824d5cedd4e7816b9bd1ad8fd8faf10ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea5ffbc7f47c7e5c163b2b8aa234373", "guid": "bfdfe7dc352907fc980b868725387e98cbd8498da46bdc13e80e0daf5cfecdb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc8fcc6a17809267dc85c21915eaea08", "guid": "bfdfe7dc352907fc980b868725387e98cdba8ea2a404b2660b432e17fe54c246"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df0c060d72d546a615344256d00dda7b", "guid": "bfdfe7dc352907fc980b868725387e980839f8f4799b56c31e05841b56ac1d99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869f0e6e8ec22cac706371dd16b63fe66", "guid": "bfdfe7dc352907fc980b868725387e9808ffc2bff24818157e637462f10b29e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984451a161af8b3de1c1ba6e88e0c54ad3", "guid": "bfdfe7dc352907fc980b868725387e989fe1eee22023d48b17be68c9d0965d6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a5cb6524d0c2fee7bb329fff584363", "guid": "bfdfe7dc352907fc980b868725387e983ba622bacabf63ad6a51b3679de69be2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8e58efa8cd96270adc7b043609e037", "guid": "bfdfe7dc352907fc980b868725387e98ba1735050801e724bd85696107cc458c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820f2afd5639e04f8645387d1bf29465c", "guid": "bfdfe7dc352907fc980b868725387e98985b8d9f6d821f7d80ed4350a7599960"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c726ab4f792b095ebc821f967e5125", "guid": "bfdfe7dc352907fc980b868725387e988e3f66701ae794449ef6934ce928d6bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b03f67ab373389dd1b687c7a1a5150d9", "guid": "bfdfe7dc352907fc980b868725387e983c1e40b0de06e297c94dea5f12ccd3e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98528f42bedf00a6125b3bdc2e6e3d746e", "guid": "bfdfe7dc352907fc980b868725387e9872c2c2314574af9408c99dcb0bf5649e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168200c842bd7e0de845546c2b7ceb54", "guid": "bfdfe7dc352907fc980b868725387e98f3a5e6e3baeafde3b11203849c5343e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ce4a6abc7667512688b6b0dfade6f74", "guid": "bfdfe7dc352907fc980b868725387e9825d7b0855637e98d9250e10b73adedec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807cfdb746102b8095e30d9ee507c0580", "guid": "bfdfe7dc352907fc980b868725387e989fb547d17708043b2690f872d2d71b28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a64443fd5348ff8241de31ab1038b8", "guid": "bfdfe7dc352907fc980b868725387e98c15ac462b00da796ba0e3a9a98f81370"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb51c9259a60dbb92e44576600832a8d", "guid": "bfdfe7dc352907fc980b868725387e9849eedc389a8d1f9483cc5f77e32d4659"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4283e0211c75c17c5d496cb0d0d4ff", "guid": "bfdfe7dc352907fc980b868725387e98edc4cc7a24adffbea25f4c540ce60fd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883ea2be707bb94ed878dbd241b3006dd", "guid": "bfdfe7dc352907fc980b868725387e98c2883256a2c89ba50b0a10787f06d767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98180cd2a1d19de8f0f510a00742d60d7c", "guid": "bfdfe7dc352907fc980b868725387e98b8b26ba68abc3cbe9754a32380de881f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989445dfdae38b943c85dc6043907d7660", "guid": "bfdfe7dc352907fc980b868725387e988feec40b342b3f102a7fbde096239f43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4ee36a67835f1a257e1001aec052eb7", "guid": "bfdfe7dc352907fc980b868725387e98317d19ef0179683093f763306cb097c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c0e1fc8a43f2e01280f2042628632f5", "guid": "bfdfe7dc352907fc980b868725387e989d8eb944e039b9f7f8513b539205773b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccc4c0967f53f3b6d6cd5695e77120fb", "guid": "bfdfe7dc352907fc980b868725387e981aee0e495fa934753b80169a62a531fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804cff912cb675e1f766744cc5a70e8e2", "guid": "bfdfe7dc352907fc980b868725387e98573aa18a200a1aca9464aff76504c418"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897cd59b402e160c1938da261c1af2123", "guid": "bfdfe7dc352907fc980b868725387e98faa18ef440a127eaa884f7d50312868b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eefe775ee266cea0c6ce401baed4514", "guid": "bfdfe7dc352907fc980b868725387e98b689c7fb107a03252eef9d7e305b308c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfaba300bf477da3db4f7c8909ad9ed1", "guid": "bfdfe7dc352907fc980b868725387e98921e9a6005ceeaa283253363a49677cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98835c9382324da4801f70d8c320addb52", "guid": "bfdfe7dc352907fc980b868725387e982edac08427b6ecf30ec9311ea9b9d153"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8e94018d21bebdcd8d08bd7fcad0132", "guid": "bfdfe7dc352907fc980b868725387e98c239fb54b22f69742ce43d2cb10be2c6"}], "guid": "bfdfe7dc352907fc980b868725387e98250b8072bbeaf8c54f54f4bbb71cecb3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9851496b38d211b501a1f12d9e3a9d7394", "guid": "bfdfe7dc352907fc980b868725387e9883b8899261e814757c5285cd0852e768"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a61d761249950157b7def56ae6875b", "guid": "bfdfe7dc352907fc980b868725387e9844c4252c6de1d337696bbc71b02851d0"}], "guid": "bfdfe7dc352907fc980b868725387e98bf613b548af7c9008622e062517d2c01", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9865923ee9ba75852ec6d01a32cdc8a934", "targetReference": "bfdfe7dc352907fc980b868725387e98c2a1c2faedbc5c4bcc7c7e6fc87d379b"}], "guid": "bfdfe7dc352907fc980b868725387e98894398e4873eb8a2c9bbca7ad3e98ef4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e982d98be93881617cd378e87b1e9124bc7", "name": "StripeCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98c2a1c2faedbc5c4bcc7c7e6fc87d379b", "name": "StripeUICore-StripeUICore"}], "guid": "bfdfe7dc352907fc980b868725387e98f17b608a8faeb01ef6ec76d52489fd0b", "name": "StripeUICore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9806d56e69e7598ecab3f1c2f2b4c84cf3", "name": "StripeUICore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}