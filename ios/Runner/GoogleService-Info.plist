<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CLIENT_ID</key>
    <string>1071857916996-p320cnii8ave9k9t5udk5s85p1ut9tfc.apps.googleusercontent.com</string>
    <key>REVERSED_CLIENT_ID</key>
    <string>com.googleusercontent.apps.1071857916996-p320cnii8ave9k9t5udk5s85p1ut9tfc</string>
    <key>ANDROID_CLIENT_ID</key>
    <string>1071857916996-23nkc64kgp958bqvaofl08b2i6uf9e55.apps.googleusercontent.com</string>
    <key>API_KEY</key>
    <string>AIzaSyCxOAx2HedynHr9OC5H0rDFZekJ2bOoWY0</string>
    <key>GCM_SENDER_ID</key>
    <string>1071857916996</string>
    <key>PLIST_VERSION</key>
    <string>1</string>
    <key>BUNDLE_ID</key>
    <string>com.smallofficer.01</string>
    <key>PROJECT_ID</key>
    <string>small-officer</string>
    <key>STORAGE_BUCKET</key>
    <string>small-officer.appspot.com</string>
    <key>IS_ADS_ENABLED</key>
    <false></false>
    <key>IS_ANALYTICS_ENABLED</key>
    <false></false>
    <key>IS_APPINVITE_ENABLED</key>
    <true></true>
    <key>IS_GCM_ENABLED</key>
    <true></true>
    <key>IS_SIGNIN_ENABLED</key>
    <true></true>
    <key>GOOGLE_APP_ID</key>
    <string>1:1071857916996:ios:e6d6ff29a0e9663971f011</string>
    <key>DATABASE_URL</key>
    <string>https://small-officer-default-rtdb.firebaseio.com</string>
</dict>
</plist>
