<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="765" height="316" viewBox="0 0 765 316">
  <defs>
    <linearGradient id="linear-gradient" x2="0.982" y2="0.929" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#bf6bff"/>
      <stop offset="1" stop-color="#a22ffe"/>
    </linearGradient>
    <filter id="Rectangle_46" x="0" y="0" width="765" height="316" filterUnits="userSpaceOnUse">
      <feOffset dx="-13" dy="11" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="23" result="blur"/>
      <feFlood flood-color="#0c0c0c" flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <pattern id="pattern" preserveAspectRatio="none" width="100%" height="100%" viewBox="0 0 608 559">
      <image width="608" height="559" xlink:href="home_back-image.png"/>
    </pattern>
  </defs>
  <g id="Group_1463" data-name="Group 1463" transform="translate(26 -408)">
    <g transform="matrix(1, 0, 0, 1, -26, 408)" filter="url(#Rectangle_46)">
      <rect id="Rectangle_46-2" data-name="Rectangle 46" width="627" height="178" rx="20" transform="translate(82 58)" fill="url(#linear-gradient)"/>
    </g>
    <rect id="Image_22" data-name="Image 22" width="162" height="149" rx="5" transform="translate(56 481)" fill="url(#pattern)"/>
    <text id="Hey_here_s_the_announcement_for_the_new_space_" data-name="Hey, here&apos;s the announcement
for the new space!" transform="translate(234 547)" fill="#fff" font-size="28" font-family="Helvetica" letter-spacing="0.02em"><tspan x="0" y="0">Hey, here&apos;s the announcement</tspan><tspan x="0" y="40">for the new space!</tspan></text>
  </g>
</svg>
