import 'package:connectivity/connectivity.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Response;
import 'package:get_storage/get_storage.dart';
import '../../main.dart';
import '../app/modules/no_internet/views/no_internet_view.dart';
import '../app/routes/app_pages.dart';
import '../constants/color_constant.dart';
import '../constants/constant.dart';
import '../constants/size_constant.dart';
import '../utilities/customeDialogs.dart';

class MethodType {
  static const String post = "POST";
  static const String get = "GET";
  static const String put = "PUT";
  static const String delete = "DELETE";
  static const String patch = "PETCH";
}

class NetworkClient {
  static NetworkClient? _shared;

  NetworkClient._();

  static NetworkClient get getInstance => _shared = _shared ?? NetworkClient._();

  final dio = Dio();

  Map<String, dynamic> getAuthHeaders({String? tokenRegister}) {
    Map<String, dynamic> authHeaders = <String, dynamic>{};
    GetStorage box = GetStorage();
    String token = "";
    if (box.read(Constant.tokenKey) != null) {
      token = box.read(Constant.tokenKey);
      print(token);
      // token =
      //     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicm9sZSI6IkFkbWluIiwiaWF0IjoxNjUxNTAzNDExLCJleHAiOjE2NTE1ODk4MTF9.DkJhSf78gpX86gzruLiTs_PKSXl4Slj-XzELfztLa6k";
    }
    if (tokenRegister != null) {
      dio.options.headers["Authorization"] = "Bearer $tokenRegister";
    } else {
      if (!isNullEmptyOrFalse(token)) {
        dio.options.headers["Authorization"] = "Bearer $token";
      } else {
        authHeaders["Content-Type"] = "application/json";
        dio.options.headers["Authorization"] = "Bearer "
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicm9sZSI6IkFkbWluIiwiaWF0IjoxNjUxNTAzMTE1LCJleHAiOjE2NTE1ODk1MTV9.m3pw_ojLzMUWSGQ4Ebm3H7iq17f5ySJObIGTw5ZzpfE";
      }
    }

    return authHeaders;
  }

  Map<String, dynamic> getAuthHeadersForVerify(String token) {
    Map<String, dynamic> authHeaders = <String, dynamic>{};

    // } else {
    dio.options.headers["Authorization"] = "Bearer $token";
    // } else {
    authHeaders["Content-Type"] = "application/json";
    // }

    return authHeaders;
  }

  Future callApi(
    BuildContext context,
    String baseUrl,
    String command,
    String method, {
    var params,
    Map<String, dynamic>? headers,
    Function(dynamic response, String message)? successCallback,
    Function(dynamic message, String statusCode)? failureCallback,
  }) async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      Get.toNamed(Routes.NO_INTERNET)?.then((value) => isNetworkPageOpen.value = false);
      // failureCallback!("", "No Internet Connection");
      // getDialog(title: "Error", desc: "No Internet Connection.");
    }

    dio.options.validateStatus = (status) {
      return status! <= 505;
    };
    dio.options.connectTimeout = Duration(hours: 5) /*50000*/; //5s
    dio.options.receiveTimeout = Duration(hours: 5) /*50000*/;

    if (headers != null) {
      for (var key in headers.keys) {
        dio.options.headers[key] = headers[key];
      }
    }

    switch (method) {
      case MethodType.post:
        Response response = await dio.post(baseUrl + command, data: params);
        parseResponse(context, response, successCallback: successCallback!, failureCallback: failureCallback!);
        break;
      case MethodType.patch:
        Response response = await dio.patch(baseUrl + command, data: params);
        parseResponse(context, response, successCallback: successCallback!, failureCallback: failureCallback!);
        break;

      case MethodType.get:
        Response response = await dio.get(baseUrl + command, queryParameters: params);
        parseResponse(context, response, successCallback: successCallback!, failureCallback: failureCallback!);
        break;

      case MethodType.put:
        Response response = await dio.put(baseUrl + command, data: params);
        parseResponse(context, response, successCallback: successCallback!, failureCallback: failureCallback!);
        break;

      case MethodType.delete:
        Response response = await dio.delete(baseUrl + command, data: params);
        parseResponse(context, response, successCallback: successCallback!, failureCallback: failureCallback!);
        break;

      default:
    }
  }

  parseResponse(BuildContext context, Response response,
      {Function(dynamic response, String message)? successCallback,
      Function(dynamic statusCode, String message)? failureCallback}) {
    // app.resolve<CustomDialogs>().showCircularDialog(context);
    String message = "response.data['message']";
    if (response.statusCode == 401) {
      box.remove(Constant.tokenKey);
      Get.offAllNamed(Routes.loginScreen);
    } else if (response.statusCode == 200 || response.statusCode == 201 || response.statusCode == 203) {
      // hideDialog(true, context);
      if (isNullEmptyOrFalse(response.data)) {
        successCallback!(response.statusCode, message);
        return;
      }
      if (response.data is Map<String, dynamic> || response.data is List<dynamic>) {
        successCallback!(response.data, message);
        return;
      } else if (response.data is List<Map<String, dynamic>>) {
        successCallback!(response.data, response.statusMessage.toString());
        return;
      } else {
        failureCallback!(response.data, response.statusMessage.toString());
        return;
      }
    } else {
      // hideDialog(true, context);

      failureCallback!(response.data, response.statusMessage.toString());
      return;
    }
  }

  void hideDialog(bool isProgress, BuildContext context) {
    if (isProgress) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
    }
  }

  getDialog({String title = "Error", String desc = "Some Thing went wrong...."}) {
    return Get.defaultDialog(
        barrierDismissible: false,
        title: title,
        content: Text(desc),
        buttonColor: appTheme.primaryTheme,
        textConfirm: "Ok",
        confirmTextColor: Colors.white,
        onConfirm: () {
          Get.back();
        });
  }
}
