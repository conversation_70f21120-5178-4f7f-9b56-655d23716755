import 'package:connectivity/connectivity.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/color_constant.dart';
import '../main.dart';
import '../utilities/customeDialogs.dart';
import 'network_call_back.dart';

class NetworkCall {
  Future makeCall(ApiCall apiCall, BuildContext context,
      {bool isProgress = true}) async {
    var connectivity = await Connectivity().checkConnectivity();
    print(connectivity);
    if (connectivity == ConnectivityResult.mobile ||
        connectivity == ConnectivityResult.wifi) {
      if (isProgress) {
        app.resolve<CustomDialogs>().showCircularDialog(context);
      }
      try {
        var resp = await apiCall();
        print(resp);
        if (resp != null) {
          hideDialog(isProgress, context);

          if (resp.status == "success") {
            return resp;
          } else if (resp.status == "fail") {
            getDialog(title: "Error", desc: resp.message.toString());
            return resp;
          } else {
            return Future.error(resp);
          }
        } else {
          getDialog(title: "Error", desc: resp.message.toString());

          hideDialog(isProgress, context);
          return Future.error(resp);
        }
      } catch (e) {
        hideDialog(isProgress, context);
        return Future.error("Something went wrong.........");
      }
    } else {
      return Future.error("No Internet Connection");
      // return Future.error(ErrorResp(
      //     message2: "No Internet Connection",
      //     network2: false,
      //     status2: "Internet unavailable."));
    }
  }

  void hideDialog(bool isProgress, BuildContext context) {
    if (isProgress) {
      app.resolve<CustomDialogs>().hideCircularDialog(context);
    }
  }

  getDialog(
      {String title = "Error", String desc = "Some Thing went wrong...."}) {
    return Get.defaultDialog(
        barrierDismissible: false,
        title: title,
        content: Text(desc),
        buttonColor: appTheme.primaryTheme,
        textConfirm: "Ok",
        confirmTextColor: Colors.white,
        onConfirm: () {
          Get.back();
        });
  }
}
