// import 'package:dio/dio.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:my_school/app/data/Network.dart';
//
// class ServiceModule {
//   NetworkService networkService({bool isUserLogin = true}) {
//     GetStorage box = GetStorage();
//     String token = "";
//     if (box.read("token") != null) {
//       token = box.read("token");
//     }
//
//     Dio dio = Dio();
//     dio.options.connectTimeout = 50000; //5s
//     dio.options.receiveTimeout = 50000;
//     dio.options.headers["Content-Type"] = "application/json";
//     if (isUserLogin && !isNullEmptyOrFalse(token)) {
//       dio.options.headers["Authorization"] = "Bearer " + token;
//     }
//     return NetworkService(dio);
//   }
// }
