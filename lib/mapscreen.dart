import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
//
// import 'page.dart';

const CameraPosition _kInitialPosition =
    CameraPosition(target: LatLng(-33.852, 151.211), zoom: 11.0);

// class SnapshotPage extends GoogleMapExampleAppPage {
//   const SnapshotPage({Key? key})
//       : super(const Icon(Icons.camera_alt), 'Take a snapshot of the map',
//             key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return _SnapshotBody();
//   }
// }

class SnapshotBody extends StatefulWidget {
  @override
  SnapshotBodyState createState() => SnapshotBodyState();
}

class SnapshotBodyState extends State<SnapshotBody> {
  GoogleMapController? _mapController;
  Uint8List? _imageBytes;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            SizedBox(
              height: 180,
              child: GoogleMap(
                onMapCreated: onMapCreated,
                initialCameraPosition: _kInitialPosition,
              ),
            ),
            TextButton(
              child: const Text('Take a snapshot'),
              onPressed: () async {
                final Uint8List? imageBytes =
                    await _mapController?.takeSnapshot();
                setState(() {
                  _imageBytes = imageBytes;
                });
              },
            ),
            Container(
              decoration: BoxDecoration(color: Colors.blueGrey[50]),
              height: 180,
              child: _imageBytes != null ? Image.memory(_imageBytes!) : null,
            ),
          ],
        ),
      ),
    );
  }

  // ignore: use_setters_to_change_properties
  void onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }
}
