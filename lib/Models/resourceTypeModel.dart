import 'package:small_officer/Models/resourceDataModel.dart';

class ResourceTypeModel {
  int? status;
  DataResource? data;

  ResourceTypeModel({this.status, this.data});

  ResourceTypeModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data =
        json['data'] != null ? DataResource.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class DataResource {
  int? count;
  List<Rows>? rows;

  DataResource({this.count, this.rows});

  DataResource.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    if (json['rows'] != null) {
      rows = <Rows>[];
      json['rows'].forEach((v) {
        rows!.add(Rows.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    if (rows != null) {
      data['rows'] = rows!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Rows {
  int? id;
  String? name;
  String? createdAt;
  String? updatedAt;
  List<Resources>? resources;

  Rows({this.id, this.name, this.createdAt, this.updatedAt, this.resources});

  Rows.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    if (json['Resources'] != null) {
      resources = <Resources>[];
      json['Resources'].forEach((v) {
        resources!.add(Resources.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    if (resources != null) {
      data['Resources'] = resources!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Resources {
  int? id;
  String? name;
  String? description;
  int? personCapacity;
  num? ratePerHour;
  String? openAt;
  String? closeAt;
  String? address;
  String? cancellationPolicy;
  String? imageUrl;
  String? createdAt;
  String? updatedAt;
  num? locationId;
  int? resourceTypeId;
  ResourceType? resourceType;
  LocationType? location;
  Resources(
      {this.id,
      this.name,
      this.description,
      this.personCapacity,
      this.ratePerHour,
      this.openAt,
      this.closeAt,
      this.address,
      this.cancellationPolicy,
      this.imageUrl,
      this.resourceType,
      this.location,
      this.createdAt,
      this.updatedAt,
      this.locationId,
      this.resourceTypeId});

  Resources.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    personCapacity = json['personCapacity'];
    ratePerHour = json['ratePerHour'];
    openAt = json['openAt'];
    closeAt = json['closeAt'];
    address = json['address'];
    resourceType = json['ResourceType'] != null
        ? ResourceType.fromJson(json['ResourceType'])
        : null;
    location = json['Location'] != null
        ? LocationType.fromJson(json['Location'])
        : null;
    cancellationPolicy = json['cancellationPolicy'];
    imageUrl = json['imageUrl'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    locationId = json['LocationId'];
    resourceTypeId = json['ResourceTypeId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['personCapacity'] = personCapacity;
    data['ratePerHour'] = ratePerHour;
    data['openAt'] = openAt;
    data['closeAt'] = closeAt;
    data['address'] = address;
    data['cancellationPolicy'] = cancellationPolicy;
    data['imageUrl'] = imageUrl;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['LocationId'] = locationId;
    data['ResourceTypeId'] = resourceTypeId;
    return data;
  }
}
