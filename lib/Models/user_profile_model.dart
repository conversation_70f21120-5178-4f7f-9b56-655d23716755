class UserProfileModel {
  int? status;
  ProfileData? data;

  UserProfileModel({this.status, this.data});

  UserProfileModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data = json['data'] != null ? ProfileData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class ProfileData {
  int? id;
  String? uid;
  String? firstName;
  String? lastName;
  String? about;
  String? email;
  int? mobile;
  // num? totalBookingHours;
  num? totalBookingH;
  String? imageUrl;
  String? createdAt;
  String? updatedAt;

  ProfileData(
      {this.id,
      this.uid,
      this.firstName,
      this.lastName,
      this.totalBookingH,
      this.about,
      this.email,
      this.mobile,
      this.imageUrl,
      this.createdAt,
      this.updatedAt});

  ProfileData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uid = json['uid'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    about = json['about'];
    email = json['email'];
    mobile = json['mobile'];
    imageUrl = json['imageUrl'];
    totalBookingH = json['totalBookingHours'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['uid'] = uid;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['about'] = about;
    data['email'] = email;
    data['mobile'] = mobile;
    data['imageUrl'] = imageUrl;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}
