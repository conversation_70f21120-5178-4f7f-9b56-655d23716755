class BannerDataModel {
  int? status;
  List<Bannerdata>? data;

  BannerDataModel({this.status, this.data});

  BannerDataModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <Bannerdata>[];
      json['data'].forEach((v) {
        data!.add(Bannerdata.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Bannerdata {
  int? id;
  String? title;
  String? imageUrl;
  bool? isActive;
  String? createdAt;
  String? updatedAt;
  int? locationId;

  Bannerdata(
      {this.id,
      this.title,
      this.locationId,
      this.imageUrl,
      this.isActive,
      this.createdAt,
      this.updatedAt});

  Bannerdata.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    imageUrl = json['imageUrl'];
    isActive = json['isActive'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    locationId = json['LocationId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['imageUrl'] = imageUrl;
    data['LocationId'] = locationId;
    data['isActive'] = isActive;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}
