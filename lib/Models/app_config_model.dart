// To parse this JSON data, do
//
//     final appConfigModel = appConfigModelFromJson(jsonString);

import 'dart:convert';

AppConfigModel appConfigModelFromJson(String str) => AppConfigModel.fromJson(json.decode(str));

String appConfigModelToJson(AppConfigModel data) => json.encode(data.toJson());

class AppConfigModel {
  int? status;
  AppConfigData? data;

  AppConfigModel({
    this.status,
    this.data,
  });

  factory AppConfigModel.fromJson(Map<String, dynamic> json) => AppConfigModel(
        status: json["status"],
        data: json["data"] == null ? null : AppConfigData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "data": data?.toJson(),
      };
}

class AppConfigData {
  int? id;
  bool? appInMaintenance;
  String? androidVersionCode;
  String? iosVersionCode;
  bool? forceUpdate;
  bool? softUpdate;
  DateTime? createdAt;
  DateTime? updatedAt;

  AppConfigData({
    this.id,
    this.appInMaintenance,
    this.androidVersionCode,
    this.iosVersionCode,
    this.forceUpdate,
    this.softUpdate,
    this.createdAt,
    this.updatedAt,
  });

  factory AppConfigData.fromJson(Map<String, dynamic> json) => AppConfigData(
        id: json["id"],
        appInMaintenance: json["appInMaintenance"],
        androidVersionCode: json["androidVersionCode"],
        iosVersionCode: json["iosVersionCode"],
        forceUpdate: json["forceUpdate"],
        softUpdate: json["softUpdate"],
        createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "appInMaintenance": appInMaintenance,
        "androidVersionCode": androidVersionCode,
        "iosVersionCode": iosVersionCode,
        "forceUpdate": forceUpdate,
        "softUpdate": softUpdate,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };
}
