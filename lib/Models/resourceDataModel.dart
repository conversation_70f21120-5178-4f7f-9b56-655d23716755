import 'package:small_officer/Models/booking_data_model.dart';

class ResourceDataModel {
  int? status;
  ResourceData? data;

  ResourceDataModel({this.status, this.data});

  ResourceDataModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    data =
        json['data'] != null ? ResourceData.fromJson(json['data']) : null;
  }
}

class ResourceData {
  int? count;
  List<RowsData>? rows;

  ResourceData({this.count, this.rows});

  ResourceData.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    if (json['rows'] != null) {
      rows = <RowsData>[];
      json['rows'].forEach((v) {
        rows!.add(RowsData.fromJson(v));
      });
    }
  }
}

class RowsData {
  int? id;
  String? name;
  String? description;
  int? personCapacity;
  num? ratePerHour;
  String? openAt;
  String? closeAt;
  String? latitude;
  String? longitude;
  String? wifiName;
  String? wifiPassword;

  bool? ac;
  bool? printer;
  bool? coffeeTea;
  bool? kitchen;
  bool? monitor;
  bool? parking;
  bool? wheelchairAccessible;
  bool? filteredWater;
  bool? mailServices;
  bool? staffed;
  bool? wifi;

  String? address;
  List<String>? otherImageUrls;
  String? cancellationPolicy;
  String? imageUrl;
  String? createdAt;
  String? updatedAt;
  String? lockId;
  String? lockId2;
  String? lockId3;
  int? locationId;
  int? resourceTypeId;
  ResourceType? resourceType;
  LocationType? location;
  List<Plans>? plans;
  List<BookingData>? bookingList;
  List<WeekDayTime>? weekDayTimes;

  List<Map> get getIcons {
    List<Map> resultIcons = [];
    Map<String, dynamic> iconsMap = {
      "ac": {
        "icon": "assets/service_icons/AC.png",
        "name": "AC",
        "value": ac,
      },
      "printer": {
        "icon": "assets/service_icons/Printer.png",
        "name": "Printer",
        "value": printer,
      },
      "coffeeTea": {
        "icon": "assets/service_icons/Coffe.png",
        "name": "Coffe",
        "value": coffeeTea,
      },
      "kitchen": {
        "icon": "assets/service_icons/Kitchen.png",
        "name": "Kitchen",
        "value": kitchen,
      },
      "monitor": {
        "icon": "assets/service_icons/Monitor.png",
        "name": "Monitor",
        "value": monitor,
      },
      "parking": {
        "icon": "assets/service_icons/Parking.png",
        "name": "Parking",
        "value": parking,
      },
      "wheelchairAccessible": {
        "icon": "assets/service_icons/Accessibility.png",
        "name": "Accessibility",
        "value": wheelchairAccessible,
      },
      "filteredWater": {
        "icon": "assets/service_icons/Filtered_water.png",
        "name": "Water",
        "value": filteredWater,
      },
      "mailServices": {
        "icon": "assets/service_icons/Mail_service.png",
        "name": "Mail",
        "value": mailServices,
      },
      "staffed": {
        "icon": "assets/service_icons/Staffed.png",
        "name": "Staffed",
        "value": staffed,
      },
      "wifi": {
        "icon": "assets/service_icons/Wi-Fi.png",
        "name": "WiFi",
        "value": wifi,
      },
    };

    iconsMap.forEach((key, value) {
      if (value["value"] == true) {
        resultIcons.add(value);
      }
    });

    return resultIcons;
  }

  RowsData({
    this.id,
    this.name,
    this.latitude,
    this.longitude,
    this.wifiName,
    this.lockId,
    this.lockId2,
    this.lockId3,
    this.wifiPassword,
    this.ac,
    this.printer,
    this.coffeeTea,
    this.kitchen,
    this.monitor,
    this.parking,
    this.wheelchairAccessible,
    this.filteredWater,
    this.mailServices,
    this.staffed,
    this.wifi,
    this.description,
    this.personCapacity,
    this.ratePerHour,
    this.otherImageUrls,
    this.openAt,
    this.closeAt,
    this.address,
    this.cancellationPolicy,
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
    this.locationId,
    this.resourceTypeId,
    this.resourceType,
    this.weekDayTimes,
  });

  RowsData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    personCapacity = json['personCapacity'];
    ratePerHour = json['ratePerHour'];
    openAt = json['openAt'];
    closeAt = json['closeAt'];
    address = json['address'];
    cancellationPolicy = json['cancellationPolicy'];
    imageUrl = json['imageUrl'];
    createdAt = json['createdAt'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    ac = json["ac"] ?? false;
    printer = json["printer"] ?? false;
    coffeeTea = json["coffeeTea"] ?? false;
    kitchen = json["kitchen"] ?? false;
    monitor = json["monitor"] ?? false;
    parking = json["parking"] ?? false;
    wheelchairAccessible = json["wheelchairAccessible"] ?? false;
    filteredWater = json["filteredWater"] ?? false;
    mailServices = json["mailServices"] ?? false;
    staffed = json["staffed"] ?? false;
    wifi = json["wifi"] ?? false;
    lockId = json['lockId'].toString();
    lockId2 = json['lockId2'].toString() ?? "";
    lockId3 = json['lockId3'].toString() ?? "";
    wifiName = json['wifiName'];
    wifiPassword = json['wifiPassword'];
    updatedAt = json['updatedAt'];
    locationId = json['LocationId'];
    resourceTypeId = json['ResourceTypeId'];
    if (json['otherImageUrls'] != null) {
      otherImageUrls = json['otherImageUrls'].cast<String>();
    }
    if (json['Bookings'] != null) {
      bookingList = <BookingData>[];
      json['Bookings'].forEach((v) {
        bookingList!.add(BookingData.fromJson(v));
      });
    }
    resourceType = json['ResourceType'] != null
        ? ResourceType.fromJson(json['ResourceType'])
        : null;
    location = json['Location'] != null
        ? LocationType.fromJson(json['Location'])
        : null;
    if (json['Plans'] != null) {
      plans = <Plans>[];
      json['Plans'].forEach((v) {
        plans!.add(Plans.fromJson(v));
      });
    }
    weekDayTimes = json["WeekDayTimes"] != null
        ? List<WeekDayTime>.from(
            json["WeekDayTimes"].map((x) => WeekDayTime.fromJson(x)))
        : [];
  }
}

class Plans {
  int? id;
  String? name;
  String? description;
  int? price;
  int? duartion;
  bool? isActive;
  String? createdAt;
  String? updatedAt;
  int? resourceId;

  Plans(
      {this.id,
      this.name,
      this.description,
      this.price,
      this.duartion,
      this.isActive,
      this.createdAt,
      this.updatedAt,
      this.resourceId});

  Plans.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    price = json['price'];
    duartion = json['duartion'];
    isActive = json['isActive'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    resourceId = json['ResourceId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['price'] = price;
    data['duartion'] = duartion;
    data['isActive'] = isActive;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['ResourceId'] = resourceId;
    return data;
  }
}

class ResourceType {
  int? id;
  String? name;
  String? createdAt;
  String? updatedAt;

  ResourceType({this.id, this.name, this.createdAt, this.updatedAt});

  ResourceType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class LocationType {
  int? id;
  String? name;
  String? createdAt;
  String? updatedAt;
  int? lockId;
  String? latitude;
  String? longitude;

  LocationType(
      {this.id,
      this.name,
      this.createdAt,
      this.updatedAt,
      this.latitude,
      this.longitude});

  LocationType.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    lockId = json['lockId'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['lockId'] = lockId;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class WeekDayTime {
  WeekDayTime({
    this.id,
    this.weekDay,
    this.isOpen,
    this.openAt,
    this.closeAt,
    this.createdAt,
    this.updatedAt,
    this.resourceId,
  });

  int? id;
  String? weekDay;
  bool? isOpen;
  String? openAt;
  String? closeAt;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? resourceId;

  factory WeekDayTime.fromJson(Map<String, dynamic> json) => WeekDayTime(
        id: json["id"],
        weekDay: json["weekDay"],
        isOpen: json["isOpen"],
        openAt: json["openAt"],
        closeAt: json["closeAt"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        resourceId: json["ResourceId"],
      );
}
