import 'package:intl/intl.dart';
import 'package:small_officer/Models/resourceDataModel.dart';

class BookingDataModel {
  int? status;
  List<BookingData>? data;

  BookingDataModel({this.status, this.data});

  BookingDataModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    if (json['data'] != null) {
      data = <BookingData>[];
      json['data'].forEach((v) {
        data!.add(BookingData.fromJson(v));
      });
    }
  }
}

class BookingData {
  int? id;
  String? startAt;
  String? endAt;
  String? createdAt;
  String? updatedAt;
  int? resourceId;
  int? userId;
  RowsData? resource;
  User? user;
  bool? plan = false;

  BookingData(
      {this.id,
      this.startAt,
      this.endAt,
      this.createdAt,
      this.plan,
      this.updatedAt,
      this.resourceId,
      this.userId,
      this.resource,
      this.user});

  String get formatedStartDate {
    return DateFormat("MM.dd.yyyy").format(DateTime.parse(startAt!).toLocal());
  }

  String get formatedStartTime {
    return DateFormat("hh:mm a").format(DateTime.parse(startAt!).toLocal());
  }

  BookingData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startAt = json['startAt'];
    endAt = json['endAt'];
    plan = json['plan'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    resourceId = json['ResourceId'];
    userId = json['UserId'];
    resource = json['Resource'] != null
        ? RowsData.fromJson(json['Resource'])
        : null;
    user = json['User'] != null ? User.fromJson(json['User']) : null;
  }
}

class Location {
  int? id;
  String? name;
  String? city;
  String? state;
  String? country;
  Null? imageUrl;
  String? createdAt;
  String? updatedAt;

  Location(
      {this.id,
      this.name,
      this.city,
      this.state,
      this.country,
      this.imageUrl,
      this.createdAt,
      this.updatedAt});

  Location.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    city = json['city'];
    state = json['state'];
    country = json['country'];
    imageUrl = json['imageUrl'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['city'] = city;
    data['state'] = state;
    data['country'] = country;
    data['imageUrl'] = imageUrl;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class User {
  int? id;
  String? uid;
  String? firstName;
  String? lastName;
  String? about;
  String? email;
  int? mobile;
  String? imageUrl;
  String? createdAt;
  String? updatedAt;

  User(
      {this.id,
      this.uid,
      this.firstName,
      this.lastName,
      this.about,
      this.email,
      this.mobile,
      this.imageUrl,
      this.createdAt,
      this.updatedAt});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uid = json['uid'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    about = json['about'];
    email = json['email'];
    mobile = json['mobile'];
    imageUrl = json['imageUrl'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['uid'] = uid;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['about'] = about;
    data['email'] = email;
    data['mobile'] = mobile;
    data['imageUrl'] = imageUrl;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}
