class SignUpModel {
  int? status;
  String? message;
  SignUpData? data;
  String? token;

  SignUpModel({this.status, this.message, this.data, this.token});

  SignUpModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? SignUpData.fromJson(json['data']) : null;
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['token'] = token;
    return data;
  }
}

class SignUpData {
  UserData? user;

  SignUpData({this.user});

  SignUpData.fromJson(Map<String, dynamic> json) {
    user = json['user'] != null ? UserData.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (user != null) {
      data['user'] = user!.toJson();
    }
    return data;
  }
}

class UserData {
  int? id;
  String? uid;
  String? imageUrl;
  String? firstName;
  String? lastName;
  String? about;
  String? email;
  String? mobile;
  String? updatedAt;
  String? createdAt;

  UserData(
      {this.id,
      this.uid,
      this.imageUrl,
      this.firstName,
      this.lastName,
      this.about,
      this.email,
      this.mobile,
      this.updatedAt,
      this.createdAt});

  UserData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uid = json['uid'];
    imageUrl = json['imageUrl'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    about = json['about'];
    email = json['email'];
    mobile = json['mobile'];
    updatedAt = json['updatedAt'];
    createdAt = json['createdAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['uid'] = uid;
    data['imageUrl'] = imageUrl;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['about'] = about;
    data['email'] = email;
    data['mobile'] = mobile;
    data['updatedAt'] = updatedAt;
    data['createdAt'] = createdAt;
    return data;
  }
}
