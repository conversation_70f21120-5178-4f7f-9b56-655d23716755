import 'package:small_officer/Models/resourceTypeModel.dart';

class InvoiceListModel {
  int? status;
  int? dataLength;
  List<Invoice>? data;

  InvoiceListModel({this.status, this.dataLength, this.data});

  InvoiceListModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    dataLength = json['dataLength'];
    if (json['data'] != null) {
      data = <Invoice>[];
      json['data'].forEach((v) {
        data!.add(Invoice.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['dataLength'] = dataLength;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Invoice {
  String? dueDate;
  int? id;
  double? totalAmount;
  bool? isPaid;
  String? createdAt;
  String? updatedAt;
  int? bookingId;
  Booking? booking;

  Invoice(
      {this.dueDate,
      this.id,
      this.totalAmount,
      this.isPaid,
      this.createdAt,
      this.updatedAt,
      this.bookingId,
      this.booking});

  Invoice.fromJson(Map<String, dynamic> json) {
    dueDate = json['dueDate'];
    id = json['id'];
    totalAmount = json['totalAmount'] != null
        ? (json['totalAmount'] is int ? (json['totalAmount'] as int).toDouble() : json['totalAmount'])
        : null;
    isPaid = json['isPaid'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    bookingId = json['BookingId'];
    booking = json['Booking'] != null ? Booking.fromJson(json['Booking']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['dueDate'] = dueDate;
    data['id'] = id;
    data['totalAmount'] = totalAmount;
    data['isPaid'] = isPaid;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['BookingId'] = bookingId;
    if (booking != null) {
      data['Booking'] = booking!.toJson();
    }
    return data;
  }
}

class Booking {
  int? id;
  String? startAt;
  String? endAt;
  String? createdAt;
  String? updatedAt;
  int? resourceId;
  int? userId;
  Resources? resource;
  User? user;

  Booking(
      {this.id,
      this.startAt,
      this.endAt,
      this.createdAt,
      this.updatedAt,
      this.resourceId,
      this.userId,
      this.resource,
      this.user});

  Booking.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startAt = json['startAt'];
    endAt = json['endAt'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    resourceId = json['ResourceId'];
    userId = json['UserId'];
    if (json['Resource'] != null) {
      resource = json['Resource'] != null ? Resources.fromJson(json['Resource']) : null;
    }

    user = json['User'] != null ? User.fromJson(json['User']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['startAt'] = startAt;
    data['endAt'] = endAt;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['ResourceId'] = resourceId;
    data['UserId'] = userId;

    if (user != null) {
      data['User'] = user!.toJson();
    }
    if (resource != null) {
      data['Resource'] = resource!.toJson();
    }
    return data;
  }
}

class User {
  int? id;
  String? uid;
  String? firstName;
  String? lastName;
  String? about;
  String? email;
  int? mobile;
  String? imageUrl;
  String? createdAt;
  String? updatedAt;

  User(
      {this.id,
      this.uid,
      this.firstName,
      this.lastName,
      this.about,
      this.email,
      this.mobile,
      this.imageUrl,
      this.createdAt,
      this.updatedAt});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uid = json['uid'];
    firstName = json['firstName'];
    lastName = json['lastName'];
    about = json['about'];
    email = json['email'];
    mobile = json['mobile'];
    imageUrl = json['imageUrl'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['uid'] = uid;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['about'] = about;
    data['email'] = email;
    data['mobile'] = mobile;
    data['imageUrl'] = imageUrl;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}
