import 'package:small_officer/Models/resourceTypeModel.dart';

import 'banner_data_model.dart';

class LocationDataModel {
  int? status;
  int? dataLength;
  List<LocationData>? data;

  LocationDataModel({this.status, this.dataLength, this.data});

  LocationDataModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    dataLength = json['dataLength'];
    if (json['data'] != null) {
      data = <LocationData>[];
      json['data'].forEach((v) {
        data!.add(LocationData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['dataLength'] = dataLength;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LocationData {
  int? id;
  String? name;
  String? city;
  String? state;
  String? country;
  String? imageUrl;
  String? createdAt;
  String? updatedAt;
  List<Resources>? resources;
  List<Bannerdata>? announcements = [];
  List<Infos>? infos;

  LocationData(
      {this.id,
      this.name,
      this.city,
      this.state,
      this.infos,
      this.country,
      this.announcements,
      this.imageUrl,
      this.createdAt,
      this.updatedAt,
      this.resources});

  LocationData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    city = json['city'];
    state = json['state'];
    country = json['country'];
    imageUrl = json['imageUrl'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    if (json['Resources'] != null) {
      resources = <Resources>[];
      json['Resources'].forEach((v) {
        resources!.add(Resources.fromJson(v));
      });
    }
    if (json['Infos'] != null) {
      infos = <Infos>[];
      json['Infos'].forEach((v) {
        infos!.add(Infos.fromJson(v));
      });
    }
    if (json['Announcements'] != null) {
      announcements = <Bannerdata>[];
      json['Announcements'].forEach((v) {
        announcements!.add(Bannerdata.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (infos != null) {
      data['Infos'] = infos!.map((v) => v.toJson()).toList();
    }
    data['city'] = city;
    data['state'] = state;
    data['country'] = country;
    data['imageUrl'] = imageUrl;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    if (resources != null) {
      data['Resources'] = resources!.map((v) => v.toJson()).toList();
    }
    if (announcements != null) {
      data['Announcements'] =
          announcements!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Infos {
  int? id;
  String? imageUrl;
  String? title;
  String? description;
  String? createdAt;
  String? updatedAt;
  int? locationId;

  Infos(
      {this.id,
      this.imageUrl,
      this.title,
      this.description,
      this.createdAt,
      this.updatedAt,
      this.locationId});

  Infos.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    imageUrl = json['imageUrl'];
    title = json['title'];
    description = json['description'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    locationId = json['LocationId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['imageUrl'] = imageUrl;
    data['title'] = title;
    data['description'] = description;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['LocationId'] = locationId;
    return data;
  }
}

// class Resources {
//   int? id;
//   String? name;
//   String? description;
//   int? personCapacity;
//   int? ratePerHour;
//   String? openAt;
//   String? closeAt;
//   String? address;
//   String? cancellationPolicy;
//   String? imageUrl;
//   String? createdAt;
//   String? updatedAt;
//   int? locationId;
//   int? resourceTypeId;
//
//   Resources(
//       {this.id,
//         this.name,
//         this.description,
//         this.personCapacity,
//         this.ratePerHour,
//         this.openAt,
//         this.closeAt,
//         this.address,
//         this.cancellationPolicy,
//         this.imageUrl,
//         this.createdAt,
//         this.updatedAt,
//         this.locationId,
//         this.resourceTypeId});
//
//   Resources.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     description = json['description'];
//     personCapacity = json['personCapacity'];
//     ratePerHour = json['ratePerHour'];
//     openAt = json['openAt'];
//     closeAt = json['closeAt'];
//     address = json['address'];
//     cancellationPolicy = json['cancellationPolicy'];
//     imageUrl = json['imageUrl'];
//     createdAt = json['createdAt'];
//     updatedAt = json['updatedAt'];
//     locationId = json['LocationId'];
//     resourceTypeId = json['ResourceTypeId'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['name'] = this.name;
//     data['description'] = this.description;
//     data['personCapacity'] = this.personCapacity;
//     data['ratePerHour'] = this.ratePerHour;
//     data['openAt'] = this.openAt;
//     data['closeAt'] = this.closeAt;
//     data['address'] = this.address;
//     data['cancellationPolicy'] = this.cancellationPolicy;
//     data['imageUrl'] = this.imageUrl;
//     data['createdAt'] = this.createdAt;
//     data['updatedAt'] = this.updatedAt;
//     data['LocationId'] = this.locationId;
//     data['ResourceTypeId'] = this.resourceTypeId;
//     return data;
//   }
// }
