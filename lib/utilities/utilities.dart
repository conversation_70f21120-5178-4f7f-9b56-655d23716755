import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:url_launcher/url_launcher.dart';

Text getLabelText(
    {required String text,
    double size = 17,
    Color? color,
    FontWeight weight = FontWeight.bold}) {
  return Text(
    text,
    style: TextStyle(
        color: (isNullEmptyOrFalse(color)) ? appTheme.secondaryColor : color,
        fontWeight: weight,
        fontSize: size),
  );
}

getShimerForHome() {
  return Center(
    child: Shimmer.fromColors(
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade50,
      enabled: true,
      child: Container(
        // color: Colors.red,
        margin: EdgeInsets.only(
          bottom: MySize.size14!,
        ),
        padding: EdgeInsets.all(
          MySize.size10!,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            MySize.size20!,
          ),
          // border: Border.all(
          //   color: appTheme.gray2Color,
          //   width: 1.5,
          // ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GridView.count(
                crossAxisCount: 2,
                physics: NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                scrollDirection: Axis.vertical,
                crossAxisSpacing: MySize.getScaledSizeWidth(15),
                childAspectRatio: 0.80,
                mainAxisSpacing: MySize.getScaledSizeHeight(20),
                children: List.generate(
                  12,
                  (ind) => Container(
                    height: MySize.size90,
                    width: 100,
                    decoration: BoxDecoration(
                        // color: Colors.white,
                        borderRadius: BorderRadius.circular(MySize.size8!),
                        border: Border.all(
                          color: appTheme.borderColor,
                        )),
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeWidth(7),
                        vertical: MySize.getScaledSizeHeight(7)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: MySize.size120,
                          child: Stack(
                            children: [
                              ClipRRect(
                                child: Container(
                                  height: MySize.size120,
                                  color: Colors.white,
                                ),
                                borderRadius:
                                    BorderRadius.circular(MySize.size4!),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: MySize.size8!,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              height: MySize.size15,
                              width: MySize.size50,
                              color: Colors.white,
                            ),
                            Row(
                              children: [
                                Container(
                                  height: MySize.size15,
                                  width: MySize.size40,
                                  color: Colors.white,
                                ),
                              ],
                            )
                          ],
                        ),
                        SizedBox(
                          height: MySize.size15!,
                        ),
                        Row(
                          children: [
                            Container(
                              height: MySize.size15,
                              width: MySize.size80,
                              color: Colors.white,
                            ),
                            // SizedBox(
                            //   width: MySize.getScaledSizeWidth(5),
                            // ),
                            // Container(
                            //   height: MySize.size20,
                            //   width: MySize.size50,
                            //   color: Colors.white,
                            // ),
                          ],
                        ),
                        SizedBox(
                          height: MySize.size15!,
                        ),
                        Container(
                          height: MySize.size15,
                          // width: MySize.size80,
                          color: Colors.white,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

getShimerForMoreProfiile() {
  return Center(
    child: Shimmer.fromColors(
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade50,
      enabled: true,
      child: Container(
        // color: Colors.red,
        margin: EdgeInsets.only(
          bottom: MySize.size14!,
        ),
        padding: EdgeInsets.all(
          MySize.size16!,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            MySize.size20!,
          ),
          // border: Border.all(
          //   color: appTheme.gray2Color,
          //   width: 1.5,
          // ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: MySize.size20,
                width: MySize.size100,
                color: Colors.white,
              ),
              SizedBox(
                height: 30,
              ),
              Row(
                children: [
                  // Stack(
                  //   children: [
                  Stack(children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(MySize.size100!),
                      // child: Image(
                      //   image: AssetImage(
                      //       "assets/deo.png"),
                      //   height: MySize.size80,
                      //   width: MySize.size80,
                      // ),
                      child: CircleAvatar(
                        radius: MySize.size50!,
                        backgroundColor: Colors.white,
                      ),
                    ),
                  ]),

                  Space.width(20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: MySize.size10,
                          width: MySize.size100,
                          color: Colors.white,
                        ),
                        Space.height(10),
                        Container(
                          height: MySize.size10,
                          width: MySize.size50,
                          color: Colors.white,
                        ),
                        Space.height(10),
                        Container(
                          height: MySize.size10,
                          //width: MySize.size50,
                          color: Colors.white,
                        ),
                        // Space.height(10),
                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        //   children: [
                        //     Container(
                        //       height: MySize.size10,
                        //       width: MySize.size100,
                        //       color: Colors.white,
                        //     ),
                        //     Container(
                        //       height: MySize.size10,
                        //       width: MySize.size50,
                        //       color: Colors.white,
                        //     ),
                        //   ],
                        // ),
                      ],
                    ),
                  )
                ],
              ),
              Space.height(30),
              // Container(
              //   height: MySize.size100!,
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(
              //       MySize.size20!,
              //     ),
              //     color: Colors.white,
              //     // border: Border.all(
              //     //   color: appTheme.gray2Color,
              //     //   width: 1.5,
              //     // ),
              //   ),
              // ),
              // Space.height(30),
              Container(
                height: MySize.size60!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              ),
              Space.height(10),
              Container(
                height: MySize.size60!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              ),
              Space.height(10),
              Container(
                height: MySize.size60!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              ),
              Space.height(10),
              Container(
                height: MySize.size60!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              ),
              Space.height(10),
              Container(
                height: MySize.size60!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              ),
              Space.height(10),
              Container(
                height: MySize.size60!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              ),
              Space.height(10),
              Container(
                height: MySize.size60!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

getShimerForNotification() {
  return Center(
    child: Shimmer.fromColors(
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade50,
      enabled: true,
      child: Container(
        // color: Colors.red,
        margin: EdgeInsets.only(
          bottom: MySize.size14!,
        ),
        padding: EdgeInsets.all(
          MySize.size5!,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            MySize.size20!,
          ),
          // border: Border.all(
          //   color: appTheme.gray2Color,
          //   width: 1.5,
          // ),
        ),
        child: ListView.separated(
            itemBuilder: (context, i) {
              return Container(
                height: MySize.size80!,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                  color: Colors.white,
                  // border: Border.all(
                  //   color: appTheme.gray2Color,
                  //   width: 1.5,
                  // ),
                ),
              );
            },
            separatorBuilder: (context, i) {
              return SizedBox(
                height: 15,
              );
            },
            itemCount: 10),
      ),
    ),
  );
}

getShimerForInvoice() {
  return Center(
    child: Shimmer.fromColors(
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade50,
      enabled: true,
      child: Container(
        // color: Colors.red,
        margin: EdgeInsets.only(
          bottom: MySize.size14!,
        ),
        padding: EdgeInsets.all(
          MySize.size5!,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            MySize.size20!,
          ),
          // border: Border.all(
          //   color: appTheme.gray2Color,
          //   width: 1.5,
          // ),
        ),
        child: ListView.separated(
            itemBuilder: (context, i) {
              return Container(
                width: double.infinity,
                height: MySize.size100,
                margin: EdgeInsets.only(bottom: MySize.size10!),
                decoration: BoxDecoration(
                    // color: appTheme.fillColor,
                    borderRadius: BorderRadius.circular(MySize.size4!),
                    border: Border.all(
                      color: appTheme.borderColor,
                    )),
                // padding: EdgeInsets.symmetric(
                //   horizontal: MySize.getScaledSizeWidth(10),
                //   vertical: MySize.size10!,
                // ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        ClipRRect(
                          child: Container(
                            height: MySize.getScaledSizeHeight(100),
                            width: MySize.getScaledSizeHeight(100),
                            color: Colors.white,
                          ),
                          borderRadius: BorderRadius.circular(MySize.size4!),
                        ),
                        SizedBox(
                          width: MySize.getScaledSizeWidth(20),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  height: MySize.size15,
                                  width: MySize.size15,
                                  color: Colors.white,
                                ),
                                SizedBox(
                                  height: MySize.size10,
                                ),
                                Container(
                                  height: MySize.size15,
                                  width: MySize.size80,
                                  color: Colors.white,
                                ),
                              ],
                            ),
                            SizedBox(
                              height: MySize.size15,
                            ),
                            Container(
                              height: MySize.size15,
                              width: MySize.size80,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ],
                    ),
                    Container(
                      width: MySize.getScaledSizeWidth(100),
                      height: MySize.size100,
                      decoration: BoxDecoration(
                        border: Border(
                          left: BorderSide(color: appTheme.borderColor),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            height: MySize.size15,
                            width: MySize.size50,
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
            separatorBuilder: (context, i) {
              return SizedBox(
                height: 15,
              );
            },
            itemCount: 10),
      ),
    ),
  );
}

getShimerForBookingList() {
  return Center(
    child: Shimmer.fromColors(
      baseColor: Colors.grey.shade200,
      highlightColor: Colors.grey.shade50,
      enabled: true,
      child: Container(
        // color: Colors.red,

        margin: EdgeInsets.only(
          bottom: MySize.size14!,
        ),
        padding: EdgeInsets.all(
          MySize.size5!,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            MySize.size20!,
          ),
          // border: Border.all(
          //   color: appTheme.gray2Color,
          //   width: 1.5,
          // ),
        ),
        child: ListView.separated(
            itemBuilder: (context, i) {
              return Container(
                decoration: BoxDecoration(
                    border: Border.all(color: appTheme.borderColor),
                    borderRadius: BorderRadius.circular(MySize.size10!)),
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.getScaledSizeWidth(10),
                  vertical: MySize.size10!,
                ),
                child: Row(
                  children: [
                    ClipRRect(
                      // child: Image(
                      //   image: AssetImage(
                      //       "assets/table.png"),
                      //   fit: BoxFit.cover,
                      //   height: MySize.size140,
                      //   width: MySize.size140,
                      // ),
                      child: Container(
                        height: MySize.size140!,
                        width: MySize.size140!,
                        color: Colors.white,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    SizedBox(
                      width: MySize.getScaledSizeWidth(7),
                    ),
                    Expanded(
                      child: Container(
                        //   height: MySize.size140,
                        //width: MySize.size140,
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  height: MySize.size15,
                                  width: MySize.size50,
                                  color: Colors.white,
                                ),
                                Container(
                                  height: MySize.size15,
                                  width: MySize.size50,
                                  color: Colors.white,
                                ),
                              ],
                            ),
                            Container(
                              height: MySize.size15,
                              width: MySize.size50,
                              color: Colors.white,
                            ),
                            SizedBox(
                              height: MySize.size10,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: MySize.size15,
                                  width: MySize.size50,
                                  color: Colors.white,
                                ),
                                Expanded(
                                  child: Container(
                                    height: MySize.size15,
                                    width: MySize.size50,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                              crossAxisAlignment: CrossAxisAlignment.start,
                            ),
                            SizedBox(
                              height: MySize.size8,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Container(
                                      height: MySize.size15,
                                      width: MySize.size50,
                                      color: Colors.white,
                                    ),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Container(
                                      height: MySize.size15,
                                      width: MySize.size50,
                                      color: Colors.white,
                                    ),
                                    Container(
                                      height: MySize.size15,
                                      width: MySize.size50,
                                      color: Colors.white,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(
                              height: MySize.size8,
                            ),
                            Center(
                              child: Row(
                                children: [
                                  Container(
                                    height: MySize.size30,
                                    width: MySize.size100,
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(5)),
                                  ),
                                  Container(
                                    height: MySize.size30,
                                    width: MySize.size100,
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(5)),
                                  ),
                                ],
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                              ),
                            )
                          ],
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                      ),
                    )
                  ],
                ),
                width: double.infinity,
              );
            },
            separatorBuilder: (context, i) {
              return SizedBox(
                height: 15,
              );
            },
            itemCount: 10),
      ),
    ),
  );
}

DateTime getDateFromString(String dateString, {String? formatter}) {
  const String kMainSourceFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
  if (formatter == null) {
    formatter = kMainSourceFormat;
  }
  try {
    return DateFormat(formatter).parse(dateString).toLocal();
  } catch (e) {
    // Handle the parsing error here, e.g., log the error, return a default value, etc.
    print("Error parsing date: $dateString");
    return DateTime.now(); // Return the current date/time as a fallback
  }
}

getDateFromStringWithHourAndMinute(int seou) {
  int totalSeconds = seou;
  int hours = (totalSeconds / 3600).round(); // 1 hour = 3600 seconds
  int minutes = ((totalSeconds % 3600) / 60).round();

  print("${seou}");
  print("${hours}  $minutes");
  return "${hours.abs()}H:${minutes.abs()}M";
}

DateTime parseDatetimeFromUtc({required String isoFormattedString}) {
  var dateTime = DateTime.parse(isoFormattedString);
  return dateTime.toLocal();
}

DateTime getDateFromStringFromUtc(String dateString, {String? formatter}) {
  const String kMainSourceFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
  if (formatter == null) {
    formatter = kMainSourceFormat;
  }
  return DateFormat(formatter).parse(dateString, true);
}

class CommonNetworkImageView extends StatelessWidget {
  ///[url] is required parameter for fetching network image
  final String url;
  final double height;
  final double width;
  final BoxFit fit;
  final String placeHolder;

  CommonNetworkImageView({
    required this.url,
    this.height = 200,
    this.width = 200,
    this.fit = BoxFit.contain,
    this.placeHolder = 'assets/table.png',
  });

  @override
  Widget build(BuildContext context) {
    return url.isEmpty
        ? Image.asset(
            placeHolder,
            height: height,
            width: width,
            fit: fit,
          )
        : CachedNetworkImage(
            height: height,
            width: width,
            fit: fit,
            imageUrl: url,
            placeholder: (context, url) => Container(
              height: 30,
              width: 30,
              child: LinearProgressIndicator(
                color: Colors.grey.shade200,
                backgroundColor: Colors.grey.shade100,
              ),
            ),
            errorWidget: (context, url, error) => Image.asset(
              placeHolder,
              height: height,
              width: width,
              fit: fit,
            ),
          );
  }
}

class MapUtils {
  MapUtils._();

  static Future<void> openMap(double latitude, double longitude) async {
    String googleUrl =
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
    //  if (await canLaunch(googleUrl)) {
    await launch(googleUrl);
    // } else {
    // throw 'Could not open the map.';
    // }
  }
}
