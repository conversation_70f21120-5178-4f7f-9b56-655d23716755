import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';

class DynamicLink {
  static Future<String> generateLink({
    String? id,
  }) async {
    final DynamicLinkParameters parameters = DynamicLinkParameters(
      uriPrefix: 'https://smallofficer.page.link',
      link: Uri.parse(
        'https://smallofficer.page.link/source?id=$id',
      ),
      androidParameters: AndroidParameters(
        packageName: 'com.small_officer.small_officer',
        // minimumVersion: 30,
      ),
      iosParameters: IOSParameters(
        bundleId: "com.user.smallOfficer",
        appStoreId: "1635176146",
        // minimumVersion: "1.0.1",
      ),
    );
    final Uri? dynamicUrl = await parameters.longDynamicLink;
    return "${dynamicUrl!.origin}${dynamicUrl.path}";
  }
}
