import 'dart:io';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/app/modules/my_profile/controllers/my_profile_controller.dart';
import 'package:small_officer/utilities/utilities.dart';
import '../Models/invoice_list_model.dart';
import 'file_handle_api.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class PdfInvoiceApi {
  static Future<File> generate(Invoice invoice) async {
    MyProfileController myProfileController = Get.put(MyProfileController());
    final pdf = pw.Document();

    final iconImage = (await rootBundle.load('assets/icon.png')).buffer.asUint8List();

    final tableHeaders = [
      // 'Name',
      // 'Resource Type',
      'Location',
      'Start At',
      'End At',
      'Duration',
      'Total',
    ];

    final tableData = [
      [
        // invoice.booking!.resource!.name.toString(),
        // invoice.booking!.resource!.resourceType!.name.toString(),
        invoice.booking!.resource!.location!.name.toString(),
        DateFormat("MM.dd.yyyy hh:mm a").format(getDateFromStringFromUtc(
          invoice.booking!.startAt.toString(),
        ).toLocal()),
        DateFormat("MM.dd.yyyy hh:mm a").format(getDateFromStringFromUtc(
          invoice.booking!.endAt.toString(),
        ).toLocal()),
        // '\$ 5',
        "${getDateFromStringFromUtc(
          invoice.booking!.endAt.toString(),
        ).toLocal().difference(getDateFromStringFromUtc(
              invoice.booking!.startAt.toString(),
            ).toLocal()).inHours} H",
        '\$ ${invoice.totalAmount.toString()}',
      ],
    ];

    pdf.addPage(
      pw.MultiPage(
        // header: (context) {
        //   return pw.Text(
        //     'Flutter Approach',
        //     style: pw.TextStyle(
        //       fontWeight: pw.FontWeight.bold,
        //       fontSize: 15.0,
        //     ),
        //   );
        // },
        build: (context) {
          return [
            pw.Row(
              children: [
                pw.Image(
                  pw.MemoryImage(iconImage),
                  height: 72,
                  width: 72,
                ),
                pw.SizedBox(width: 1 * PdfPageFormat.mm),
                pw.Column(
                  mainAxisSize: pw.MainAxisSize.min,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'INVOICE',
                      style: pw.TextStyle(
                        fontSize: 17.0,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      'Small Officer',
                      style: const pw.TextStyle(
                        fontSize: 15.0,
                        color: PdfColors.grey700,
                      ),
                    ),
                  ],
                ),
                pw.Spacer(),
                pw.Column(
                  mainAxisSize: pw.MainAxisSize.min,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      myProfileController.userProfileModel!.value.data!.firstName.toString() +
                          myProfileController.userProfileModel!.value.data!.lastName.toString(),
                      style: pw.TextStyle(
                        fontSize: 15.5,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      myProfileController.userProfileModel!.value.data!.email.toString(),
                    ),
                    pw.Text(
                      DateTime.now().toString(),
                    ),
                  ],
                ),
              ],
            ),
            pw.SizedBox(height: 1 * PdfPageFormat.mm),
            pw.Divider(),
            pw.SizedBox(height: 1 * PdfPageFormat.mm),
            // pw.Text(
            //   'Dear John,\nLorem ipsum dolor sit amet consectetur adipisicing elit. Maxime mollitia, molestiae quas vel sint commodi repudiandae consequuntur voluptatum laborum numquam blanditiis harum quisquam eius sed odit fugiat iusto fuga praesentium optio, eaque rerum! Provident similique accusantium nemo autem. Veritatis obcaecati tenetur iure eius earum ut molestias architecto voluptate aliquam nihil, eveniet aliquid culpa officia aut! Impedit sit sunt quaerat, odit, tenetur error',
            //   textAlign: pw.TextAlign.justify,
            // ),
            pw.SizedBox(height: 5 * PdfPageFormat.mm),

            ///
            /// PDF Table Create
            ///
            ///
            ///
            pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.start,
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Text(
                    'Resource Name : ',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(width: 10),
                  pw.Text(
                    '${invoice.booking!.resource!.name}',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.normal,
                    ),
                  ),
                ]),
            pw.SizedBox(height: 5 * PdfPageFormat.mm),
            pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.start,
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Text(
                    'Resource Type : ',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(width: 10),
                  pw.Text(
                    '${invoice.booking!.resource!.resourceType!.name}',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.normal,
                    ),
                  ),
                ]),
            pw.SizedBox(height: 5 * PdfPageFormat.mm),
            pw.Table.fromTextArray(
              headers: tableHeaders,
              data: tableData,
              border: null,
              cellAlignment: pw.Alignment.center,
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
              cellHeight: 30.0,
              cellAlignments: {
                0: pw.Alignment.center,
                1: pw.Alignment.center,
                2: pw.Alignment.center,
                3: pw.Alignment.center,
                4: pw.Alignment.center,
              },
            ),
            pw.Divider(),
            pw.Container(
              alignment: pw.Alignment.centerRight,
              child: pw.Row(
                children: [
                  pw.Spacer(flex: 6),
                  // pw.Expanded(
                  //   flex: 4,
                  //   child: pw.Column(
                  //     crossAxisAlignment: pw.CrossAxisAlignment.start,
                  //     children: [
                  //       pw.Row(
                  //         children: [
                  //           pw.Expanded(
                  //             child: pw.Text(
                  //               'Net total',
                  //               style: pw.TextStyle(
                  //                 fontWeight: pw.FontWeight.bold,
                  //               ),
                  //             ),
                  //           ),
                  //           pw.Text(
                  //             '\$ 464',
                  //             style: pw.TextStyle(
                  //               fontWeight: pw.FontWeight.bold,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       pw.Row(
                  //         children: [
                  //           pw.Expanded(
                  //             child: pw.Text(
                  //               'Vat 19.5 %',
                  //               style: pw.TextStyle(
                  //                 fontWeight: pw.FontWeight.bold,
                  //               ),
                  //             ),
                  //           ),
                  //           pw.Text(
                  //             '\$ 90.48',
                  //             style: pw.TextStyle(
                  //               fontWeight: pw.FontWeight.bold,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       pw.Divider(),
                  //       pw.Row(
                  //         children: [
                  //           pw.Expanded(
                  //             child: pw.Text(
                  //               'Total amount due',
                  //               style: pw.TextStyle(
                  //                 fontSize: 14.0,
                  //                 fontWeight: pw.FontWeight.bold,
                  //               ),
                  //             ),
                  //           ),
                  //           pw.Text(
                  //             '\$ 554.48',
                  //             style: pw.TextStyle(
                  //               fontWeight: pw.FontWeight.bold,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       pw.SizedBox(height: 2 * PdfPageFormat.mm),
                  //       pw.Container(height: 1, color: PdfColors.grey400),
                  //       pw.SizedBox(height: 0.5 * PdfPageFormat.mm),
                  //       pw.Container(height: 1, color: PdfColors.grey400),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
            ),
          ];
        },
        footer: (context) {
          return pw.Column(
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Divider(),
              pw.SizedBox(height: 2 * PdfPageFormat.mm),
              pw.Text(
                'Small Officer',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              ),
              // pw.SizedBox(height: 1 * PdfPageFormat.mm),
              // pw.Row(
              //   mainAxisAlignment: pw.MainAxisAlignment.center,
              //   children: [
              //     pw.Text(
              //       'Address: ',
              //       style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              //     ),
              //     pw.Text(
              //       'Merul Badda, Anandanagor, Dhaka 1212',
              //     ),
              //   ],
              // ),
              // pw.SizedBox(height: 1 * PdfPageFormat.mm),
              // pw.Row(
              //   mainAxisAlignment: pw.MainAxisAlignment.center,
              //   children: [
              //     pw.Text(
              //       'Email: ',
              //       style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              //     ),
              //     pw.Text(
              //       '<EMAIL>',
              //     ),
              //   ],
              // ),
            ],
          );
        },
      ),
    );

    return FileHandleApi.saveDocument(name: 'my_invoice_${invoice.booking!.resource!.name}${invoice.id}.pdf', pdf: pdf);
  }
}
