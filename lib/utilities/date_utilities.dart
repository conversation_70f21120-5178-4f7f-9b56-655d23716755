import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

final firstDate = DateTime(1990);
final lastDate = DateTime(DateTime.now().toUtc().year, 12, 31);

extension DateTimeExt on DateTime {
  double get daysSinceEpoch => millisecondsSinceEpoch / 86400000;
  DateTime get dateStart => DateTime.utc(year, month, day);
  Map<String, dynamic> get asQueryParam =>
      <String, dynamic>{'day': formatDate()};

  String formatTime() => DateFormat('HH:mm').format(this);
  String format12Time() => DateFormat('hh:mm a').format(this);
  String formatDate() => DateFormat('yyyy-MM-dd').format(this);
  String formatDateShort() => DateFormat('MMM-dd').format(this);
  String formatTimeElapsedFrom() {
    final difference = DateTime.now().toUtc().difference(toUtc());
    return difference.formatDurationSingle();
  }

  bool isSameTime(DateTime other) =>
      other.minute == minute && other.hour == hour;

  bool isSameDate(DateTime other) =>
      other.year == year && other.month == month && other.day == day;

  double dayTimeToDouble() {
    final timeOfDay = TimeOfDay.fromDateTime(this);
    final hours = timeOfDay.hour;
    final minutesDecimal = (timeOfDay.minute % 60) / 60;
    return hours + minutesDecimal;
  }

  static DateTime nowUtc() {
    final now = DateTime.now();
    return DateTime.utc(now.year, now.month, now.day, now.hour, now.minute,
        now.second, now.millisecond, now.microsecond);
  }

  static DateTime get todayStart {
    final now = DateTime.now().toUtc();
    return DateTime(now.year, now.month, now.day);
  }

  static DateTime dayStart(DateTime other) {
    return DateTime(other.year, other.month, other.day);
  }

  static DateTime dayEnd(DateTime other) {
    return DateTime(other.year, other.month, other.day + 1);
  }

  static DateTime get todayEnd {
    final now = DateTime.now().toUtc();
    return DateTime(now.year, now.month, now.day + 1);
  }

  static DateTime get weekStart {
    final now = DateTime.now().toUtc();
    return DateTime(now.year, now.month, now.day - (now.weekday - 1));
  }

  static DateTime get monthStart {
    final now = DateTime.now().toUtc();
    return DateTime(now.year, now.month, 0);
  }

  static DateTime get yearStart {
    final now = DateTime.now().toUtc();
    return DateTime(now.year);
  }

  static DateTime get yearEnd {
    final now = DateTime.now().toUtc();
    return DateTime(now.year, 12, 31);
  }

  DateTime copyWith({
    int? year,
    int? month,
    int? day,
    int? hour,
    int? minute,
    int? second,
    int? millisecond,
    int? microsecond,
  }) {
    if (isUtc) {
      return DateTime.utc(
        year ?? this.year,
        month ?? this.month,
        day ?? this.day,
        hour ?? this.hour,
        minute ?? this.minute,
        second ?? this.second,
        millisecond ?? this.millisecond,
        microsecond ?? this.microsecond,
      );
    }
    return DateTime(
      year ?? this.year,
      month ?? this.month,
      day ?? this.day,
      hour ?? this.hour,
      minute ?? this.minute,
      second ?? this.second,
      millisecond ?? this.millisecond,
      microsecond ?? this.microsecond,
    );
  }
}

extension CopyWith on DateTimeRange {
  DateTimeRange copyWith({DateTime? start, DateTime? end}) =>
      DateTimeRange(start: start ?? this.start, end: end ?? this.end);
  double toDouble() => duration.inHours + (duration.inMinutes % 60) / 60;
}

extension TimeFormatExt on int {
  String formatElapsedTime() {
    final seconds = this / 1000;
    final secondsLimit = (seconds % 60).toInt();
    final minutes = ((seconds / 60) % 60).toInt();
    final hours = ((seconds / 3600) % 60).toInt();
    final secondsString = secondsLimit < 10 ? '0$secondsLimit' : secondsLimit;
    final minutesString = minutes < 10 ? '0$minutes' : minutes;
    final hoursString = hours.toStringAsFixed(0);
    return '$hoursString:$minutesString:$secondsString';
  }

  String formatElapsedTimeWithoutSeconds() {
    final seconds = this / 1000;
    final minutes = ((seconds / 60) % 60).toInt();
    final hours = ((seconds / 3600) % 60).toInt();
    final minutesString = minutes < 10 ? '0$minutes' : minutes;
    final hoursString = hours.toStringAsFixed(0);
    return '$hoursString:$minutesString';
  }
}

extension DateTimeRangeExt on DateTimeRange {
  String formatRange() {
    final start = this.start.formatDate();
    final end = this.end.formatDate();
    return '$start - $end';
  }

  String intToTimeLeft(int value) {
    int h, m, s;

    h = value ~/ 3600;

    m = ((value - h * 3600)) ~/ 60;

    s = value - (h * 3600) - (m * 60);

    String hourLeft =
        h.toString().length < 2 ? "0$h" : h.toString();

    String minuteLeft =
        m.toString().length < 2 ? "0$m" : m.toString();

    String secondsLeft =
        s.toString().length < 2 ? "0$s" : s.toString();

    String result = "$hourLeft:$minuteLeft:$secondsLeft";

    return result;
  }

  String formatTimeRange() {
    final start = this.start.formatTime();
    final end = this.end.formatTime();

    return '$start - $end';
  }

  String formatTimeRangeFormat() {
    final diff = end.difference(start);
    return intToTimeLeft(diff.inSeconds);
  }

  static DateTimeRange get today {
    final todayStart = DateTimeExt.todayStart;
    return DateTimeRange(
      start: todayStart,
      end: todayStart.add(
        const Duration(days: 1),
      ),
    );
  }

  static DateTimeRange get yesterday {
    final todayStart = DateTimeExt.todayStart;
    return DateTimeRange(
      start: todayStart.subtract(const Duration(days: 1)),
      end: todayStart,
    );
  }

  static DateTimeRange get week {
    final todayEnd = DateTimeExt.todayEnd;
    final weekStart = DateTimeExt.weekStart;
    return DateTimeRange(start: weekStart, end: todayEnd);
  }

  static DateTimeRange get month {
    final todayEnd = DateTimeExt.todayEnd;
    final monthStart = DateTimeExt.monthStart;
    return DateTimeRange(start: monthStart, end: todayEnd);
  }

  static DateTimeRange get year {
    final yearStart = DateTimeExt.yearStart;
    final todayEnd = DateTimeExt.todayEnd;
    return DateTimeRange(start: yearStart, end: todayEnd);
  }
}

extension DurationExt on Duration {
  String formatDuration() {
    var hours = inHours.toString();
    var minutes = (inMinutes % 60).toString();
    var seconds = (inSeconds % 60).toString();
    if (hours.length == 1) {
      hours = '0$hours';
    }
    if (minutes.length == 1) {
      minutes = '0$minutes';
    }
    if (seconds.length == 1) {
      seconds = '0$seconds';
    }
    return '$hours:$minutes:$seconds';
  }

  String formatDurationWithoutSec() {
    var hours = inHours.toString();
    var minutes = (inMinutes % 60).toString();
    var seconds = (inSeconds % 60).toString();
    if (hours.length == 1) {
      hours = '0$hours';
    }
    if (minutes.length == 1) {
      minutes = '0$minutes';
    }
    if (seconds.length == 1) {
      seconds = '0$seconds';
    }
    return '$hours:$minutes';
  }

  String formatDurationSingle() {
    if (this < const Duration(minutes: 1)) {
      return '${inSeconds}s';
    } else if (this < const Duration(hours: 1)) {
      return '${inMinutes}m';
    } else if (this < const Duration(days: 1)) {
      return '${inHours}h';
    } else {
      return '${inDays}d';
    }
  }
}
