import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:small_officer/constants/color_constant.dart';

class CustomDialogs {
  void showCircularDialog(BuildContext context, {bool isFromPrivacy = true}) {
    CircularDialog.showLoadingDialog(context, isFromPrivacy: isFromPrivacy);
  }

  void hideCircularDialog(BuildContext context) {
    Get.back();
  }

  getDialog({
    String title = "Error",
    String desc = "Some Thing went wrong....",
  }) {
    return Get.defaultDialog(
        barrierDismissible: false,
        title: title,
        content: Text(desc),
        buttonColor: appTheme.secondaryColor,
        textConfirm: "Ok",
        confirmTextColor: Colors.white,
        onConfirm: () {
          Get.back();
        });
  }
}

class CircularDialog {
  static Future<void> showLoadingDialog(BuildContext context,
      {bool isFromPrivacy = true}) {
    return showDialog(
      // barrierColor: (isFromPrivacy) ? Colors.transparent : Colors.black54,
      context: context,
      builder: (BuildContext context) {
        return WillPopScope(
            child: Center(
                child: CircularProgressIndicator(color: appTheme.primaryTheme)),
            onWillPop: () async {
              return false;
            });
      },
      barrierDismissible: false,
    );
  }
}
