import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

class FCMService extends GetxService {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  bool _initialized = false;

  Future<void> init() async {
    initFCM();
    fcmSubscribe();
    var initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');

    final DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: true,
      defaultPresentAlert: true,
      requestAlertPermission: true,
    );

    final InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid, iOS: initializationSettingsIOS);

    flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: selectNotification
    );
    findToken();

    //FirebaseMessaging.onBackgroundMessage(myBackgroundHandler);
  }

  Future<void> selectNotification(NotificationResponse? notificationResponse) async {
    if (notificationResponse?.payload != null) {
      Future.delayed(const Duration(seconds: 1), () {
        // CommonFunction.getNotificationOnTap(jsonDecode(notificationResponse?.payload ?? "{}"))();
      });
      print("Notification clicked with payload: ${notificationResponse?.payload}");
      // Implement navigation or other actions based on the payload
    }
  }


  // Future selectNotification(String? payload) async {
  //   await flutterLocalNotificationsPlugin.cancelAll();
  // }

  Future<void> myBackgroundHandler(RemoteMessage message) async {
    await Firebase.initializeApp();
    return _showNotification(message);
  }

  Future _showNotification(RemoteMessage message) async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // title
      description: 'This channel is used for important notifications.', // description
      importance: Importance.max,
    );
    final DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings(
      requestSoundPermission: true,
      requestBadgePermission: true,
      defaultPresentAlert: true,
      requestAlertPermission: true,
    );
    if (Platform.isAndroid) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }

    if (Platform.isIOS) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.initialize(initializationSettingsIOS);
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
    }
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    print(message.data);
    Map<String, dynamic> data = message.data;
    AndroidNotification? android = message.notification!.android;
    flutterLocalNotificationsPlugin.show(
      0,
      data['title'],
      data['body'],
      NotificationDetails(
        android: AndroidNotificationDetails(
          channel.id,
          channel.name,
          channelDescription: channel.description,
          icon: android?.smallIcon,
          // other properties...
        ),
        iOS: DarwinNotificationDetails(presentAlert: true, presentSound: true),
      ),
      payload: 'Default_Sound',
    );
  }

  void setToken(String? token) async {
    print('FCM Token: $token');
    if (token != null) {}
  }

  Future<void> saveToken() async {}

  /// Define a top-level named handler which background/terminated messages will
  /// call.
  ///
  /// To verify things are working, check out the native platform logs.
  Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
    // If you're going to use other Firebase services in the background, such as Firestore,
    // make sure you call `initializeApp` before using other Firebase services.
    await Firebase.initializeApp();
    print('Handling a background message ${message.messageId}');
  }

  void initFCM() async {
    if (!_initialized) {
      // For iOS request permission first.
      if (Platform.isIOS) {
        FirebaseMessaging.instance.requestPermission();
      }

      /// Update the iOS foreground notification presentation options to allow
      /// heads up notifications.
      await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
        if (message != null) {
          showNotification(message);
        }
      });

      // Set the background messaging handler early on, as a named top-level function
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        //debugprint('onMessage: ${message.data}');

        ('onMessage');
        print(message);
        print(message.messageId);

        showNotification(message);
      });

      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('onMessageOpenedApp');
        print(message.notification);
        print(message.data);
        showNotification(message);
      });

      //find token
      //FirebaseMessaging.instance.getToken().then(setToken);
      Stream<String> tokenStream = FirebaseMessaging.instance.onTokenRefresh;
      tokenStream.listen(setToken);

      _initialized = true;
    }

    /*String testMessage =
        '{"notification": {"title": "vishwajit chauhan", "body": "Liked your moment","image": "https://bounsh.s3.amazonaws.com/profile/cbf0a9fb-0cc4-4d0f-9f3d-4391fc43e911.jpg"}, "data": {"profile": "https://bounsh.s3.amazonaws.com/profile/cbf0a9fb-0cc4-4d0f-9f3d-4391fc43e911.jpg", "uid": "vishwajit76", "name": "vishwajit chauhan", "image":"" , "stuff_type": "4", "action_type": "1", "stuff_id": "108"}}';
    Map<String, dynamic> data =
        new Map<String, dynamic>.from(json.decode(testMessage));
    showNotification(data);*/
  }

  void showNotification(RemoteMessage message) async {
    print("flutter fcm message title - ${message.notification!.title}");
    print("flutter fcm message body - ${message.notification!.body}");
    print("flutter fcm message data - ${json.encode(message.data)}");

    String? image;
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;
    AppleNotification? apple = message.notification?.apple;

    if (notification != null) {
      /*Map<String, dynamic> notification =
          new Map<String, dynamic>.from(message['notification']);*/
      //Utils.printMap(notification);

      if (Platform.isAndroid) {
        if (android!.imageUrl != null) {
          image = android.imageUrl;
        }
      } else if (Platform.isIOS) {
        if (apple!.imageUrl != null) {
          image = apple.imageUrl;
        }
      }
    } else {}

    _showNotification(message);
  }

  void fcmSubscribe() {
    // _firebaseMessaging.subscribeToTopic('pvc2print');
    _firebaseMessaging.subscribeToTopic('TempAllUsers');
  }

  void fcmUnSubscribe() {
    // _firebaseMessaging.unsubscribeFromTopic('pvc2print');
    _firebaseMessaging.unsubscribeFromTopic('TempAllUsers');
  }

  void findToken() async {
    String? fcmToken;

    if (Platform.isAndroid) {
      fcmToken = (await FirebaseMessaging.instance.getToken())!;
    } else if (Platform.isIOS) {
      fcmToken = (await FirebaseMessaging.instance.getToken())!;
    }

    if (fcmToken != null) {
      print("fcm token ---> $fcmToken");
    } else {
      print("fcm token is null");
    }
  }
}
