// import 'package:flutter/services.dart';
// import 'package:pdf/pdf.dart';
// import 'dart:io';
// import 'package:pdf/widgets.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:flutter/material.dart' as material;

// import '../app/modules/invoice_detail/views/invoice_detail_view.dart';

// reportView(context) async {
//   final Document pdf = Document();
//   final font = await rootBundle.load("Fonts/Helvetica.ttf");
//   final ttf = Font.ttf(font);

//   pdf.addPage(Page(
//     pageFormat: PdfPageFormat.a4,

//     build: (Context context) {
//       return Column(children: [
//         Text("sds", style: TextStyle(font: ttf)),
//       ]);
//     },
//     // crossAxisAlignment: CrossAxisAlignment.start,
//     // header: (Context context) {
//     //   // if (context.pageNumber == 1) {
//     //   //   return null;
//     //   // }
//     //   return Container(
//     //       alignment: Alignment.centerRight,
//     //       margin: const EdgeInsets.only(bottom: 3.0 * PdfPageFormat.mm),
//     //       padding: const EdgeInsets.only(bottom: 3.0 * PdfPageFormat.mm),
//     //       // decoration: const BoxDecoration(
//     //       //     border:
//     //       //         BoxBorder(bottom: true, width: 0.5, color: PdfColors.grey)),
//     //       child: Text('Report',
//     //           style: Theme.of(context)
//     //               .defaultTextStyle
//     //               .copyWith(color: PdfColors.grey)));
//     // },
//     // footer: (Context context) {
//     //   return Container(
//     //       alignment: Alignment.centerRight,
//     //       margin: const EdgeInsets.only(top: 1.0 * PdfPageFormat.cm),
//     //       child: Text('Page ${context.pageNumber} of ${context.pagesCount}',
//     //           style: Theme.of(context)
//     //               .defaultTextStyle
//     //               .copyWith(color: PdfColors.grey)));
//     // },
//   ));
//   final String dir = (await getApplicationDocumentsDirectory()).path;
//   final String path = '$dir/report.pdf';
//   final File file = File(path);
//   await file.writeAsBytes(await pdf.save());
//   material.Navigator.of(context).push(
//     material.MaterialPageRoute(
//       builder: (_) => PdfViewerPage(path: file),
//     ),
//   );
//   //save PDF
// }
