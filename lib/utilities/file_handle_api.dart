import 'dart:io';
import 'package:open_file_safe/open_file_safe.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';

class FileHandleApi {
  // save pdf file function
  static Future<File> saveDocument({
    required String name,
    required pw.Document pdf,
  }) async {
    final bytes = await pdf.save();

    final dir = await getDownloadDirectory();
    final file = (Platform.isAndroid)
        ? File('/sdcard/download/$name')
        : File('${dir.path}/$name');
    await file.writeAsBytes(bytes);
    return file;
  }

  // open pdf file function
  static Future<void> openFile(File file) async {
    final url = file.path;
    await OpenFile.open(url);
  }

  static Future<Directory> getDownloadDirectory() async {
    if (Platform.isAndroid) {
      return await getDownloadDirectory();
    }

    // in this example we are using only Android and iOS so I can assume
    // that you are not trying it for other platforms and the if statement
    // for iOS is unnecessary

    // iOS directory visible to user

    return await getApplicationDocumentsDirectory();

    // return await getExternalStorageDirectory();
  }
}
