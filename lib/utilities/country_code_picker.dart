import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';

Widget countryCodePicker(
    {Function(CountryCode? v)? onChange, Function(CountryCode? v)? onInit}) {
  return Container(
    width: 70,
    //color: Colors.red,
    child: CountryCodePicker(
      padding: EdgeInsets.all(0),
      // textStyle:
      // GoogleFonts.sourceSansPro(color: AppColors.kOrange, fontSize: 16),
      initialSelection: 'US',
      enabled: false,
      onChanged: onChange,

      showFlag: true,
      alignLeft: false,
      showFlagDialog: true,
      showFlagMain: false,
      onInit: onInit,
    ),
  );
}
