import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:kiwi/kiwi.dart';
import 'package:small_officer/constants/app_module.dart';
import 'package:small_officer/utilities/fcm_service.dart';
import 'app/routes/app_pages.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;

import 'constants/api_constant.dart';
import 'constants/config.dart';

late KiwiContainer app;
IO.Socket? socket;
GetStorage box = GetStorage();
void main() async {
  CONFIG().config(Environment.production);
  baseURL = CONFIG().baseURL;
  authUrl = CONFIG().authUrl;
  if (defaultTargetPlatform == TargetPlatform.android) {
    AndroidGoogleMapsFlutter.useAndroidViewSurface = true;
  }
  WidgetsFlutterBinding.ensureInitialized();

  Stripe.publishableKey = CONFIG().publishableKey; //Staging
  Stripe.merchantIdentifier = 'merchant.com.smallofficer.01';

  await Stripe.instance.applySettings();

  // socket = IO.io('http://************:8080', <String, dynamic>{
  //   'transports': ['websocket'],
  //   'autoConnect': true,
  // }).connect();
  // print(socket!.connected);

  // Set the status bar color to transparent
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ),
  );

  await Firebase.initializeApp();

  await GetStorage.init();
  app = KiwiContainer();
  setup();
  getFcmToken();
  await SystemChrome.setPreferredOrientations(
      <DeviceOrientation>[DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
  runApp(
    GetMaterialApp(
      theme: ThemeData(
        canvasColor: Colors.white,
      ),
      debugShowCheckedModeBanner: false,
      title: "Application",
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routes,
    ),
  );
}

getFcmToken() async {
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  await messaging.requestPermission().then((value) async {
    FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
    try {
      String? fcmToken = await firebaseMessaging.getToken().catchError(
        (e) {
          debugPrint("error FCM TOKEN :: $e");
          return "";
        },
      );

      if (fcmToken != null) {
        debugPrint('This is Token IOS: ' '$fcmToken');

        Get.put<FCMService>(FCMService()..init());
      }
    } catch (e) {
      debugPrint("FCM TOKEN EXCEPTION :: $e");
    }
  });
}
