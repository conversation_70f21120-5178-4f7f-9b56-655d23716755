//Staging base URLs:
String baseURL = "https://stag-backend.admin-smallofficer.com/api/v1/";
String authUrl = "https://stag-backend.admin-smallofficer.com/auth/";
const String playStoreLink = "https://play.google.com/store/apps/details?id=com.small_officer.small_officer",
    appStoreLink = "https://apps.apple.com/app/smallOfficer/6446423886";

//Production base URLs: // #TODO
// const String baseURL = "https://backend.admin-smallofficer.com/api/v1/";
// const String authUrl = "https://backend.admin-smallofficer.com/auth/";

class ApiConstant {
  static const signUp = "users/signup";
  static const loginApi = "users/login";
  static const userProfile = "user/profile";
  static const deleteMe = "user/delete";
  static const appreport = "appreport";
  static const fcmToken = "user/updateFcmToken";

  static const locationApi = "users/login";
  static const verifyUser = "user/verify";
  static const userSignUp = "user/signup";
  static const sourceTypeApi = "resourcetype";
  static const info = "info";
  static const invoice = "invoice";
  static const bookingList = "booking";
  static const uploadfile = "uploadfile";
  static const chat = "chat";
  static const announcement = "announcement";
  static const location = "location";
  static const sourceAllDataApi = "resource";
  static const notification = "notification";
  static const notificationDelete = "notification/all";
  static const appConfig = "appConfig";
}
