import 'package:flutter/material.dart';
import 'package:small_officer/constants/color_constant.dart';

Widget appButton(
    {required String btnText,
    Widget? child,
    Function? onTap,
    double? width,
    double? height,
    String? fontFamily,
    double? borderRadius,
    Gradient? btnGradient,
    TextStyle? textStyle}) {
  return GestureDetector(
      onTap: () {
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
        decoration: BoxDecoration(
            gradient: btnGradient ??
                LinearGradient(
                  colors: [BaseTheme().newPrimaryColor, BaseTheme().newPrimaryColor],
                ),
            borderRadius: BorderRadius.circular(borderRadius ?? 15)),
        height: height ?? 55,
        width: width ?? double.infinity,
        child: child ??
            Center(
                child: FittedBox(
              child: Text(
                btnText,
                style: textStyle ??
                    TextStyle(
                        fontSize: 16,
                        color: BaseTheme().GrayColor,
                        fontWeight: FontWeight.w600,
                        fontStyle: FontStyle.normal),
              ),
            )),
      ));
}
