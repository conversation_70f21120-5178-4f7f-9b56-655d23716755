enum Environment { development, staging, production }

class CONFIG {
  static final CONFIG _instance = CONFIG._internal();
  factory CONFIG() {
    return _instance;
  }

  CONFIG._internal();
  String baseURL = "https://stag-backend.admin-smallofficer.com/api/v1/";
  String authUrl = "https://stag-backend.admin-smallofficer.com/auth/";
  String publishableKey =
      "pk_test_51InqqBJp7VibIijpH0MjwYdAMxcknH6Whdp82NXe3iOvZGYT6ocW1Afuy0WuZWT4lxL0Cxe5J1NsfQLLXkSD9nqO00AL9ojcVe";
  String secreteKey =
      "sk_test_51InqqBJp7VibIijpZoKwPFcgwr43RL1jVPnpwBCiQLrFCggACM3rilmu83MlG8CBaAlP4LzBEQc8cWYiMjOOiwkV00ufLFLTqL";
  String env = "prod";

  config(Environment environment) {
    switch (environment) {
      case Environment.development:
        {
          baseURL = "https://stag-backend.admin-smallofficer.com/api/v1/";
          authUrl = "https://stag-backend.admin-smallofficer.com/auth/";
          env = "dev_";
          publishableKey =
              "pk_test_51InqqBJp7VibIijpH0MjwYdAMxcknH6Whdp82NXe3iOvZGYT6ocW1Afuy0WuZWT4lxL0Cxe5J1NsfQLLXkSD9nqO00AL9ojcVe";
          secreteKey =
              "sk_test_51InqqBJp7VibIijpZoKwPFcgwr43RL1jVPnpwBCiQLrFCggACM3rilmu83MlG8CBaAlP4LzBEQc8cWYiMjOOiwkV00ufLFLTqL";
        }
        break;
      case Environment.staging:
        {
          baseURL = "https://stag-backend.admin-smallofficer.com/api/v1/";
          authUrl = "https://stag-backend.admin-smallofficer.com/auth/";
          env = "stag_";
          publishableKey =
              "pk_test_51InqqBJp7VibIijpH0MjwYdAMxcknH6Whdp82NXe3iOvZGYT6ocW1Afuy0WuZWT4lxL0Cxe5J1NsfQLLXkSD9nqO00AL9ojcVe";
          secreteKey =
              "sk_test_51InqqBJp7VibIijpZoKwPFcgwr43RL1jVPnpwBCiQLrFCggACM3rilmu83MlG8CBaAlP4LzBEQc8cWYiMjOOiwkV00ufLFLTqL";
        }
        break;
      case Environment.production:
        {
          baseURL = "https://backend.admin-smallofficer.com/api/v1/";
          authUrl = "https://backend.admin-smallofficer.com/auth/";
          env = "prod_";
          publishableKey =
              "pk_live_51InqqBJp7VibIijpWiUtROhnFg9ra76Z3AZeQWuhcGfQNi9WqHhmvs4SMiXier6DH50Sq3HTdOIgto1ZHpusmi3m00aYcIYGaY";
          secreteKey =
              "***********************************************************************************************************";
        }
        break;
    }
  }
}
