import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:small_officer/utilities/fcm_service.dart';

import '../../../../utilities/utilities.dart';
import '../controllers/my_profile_controller.dart';

class MyProfileView extends GetWidget<MyProfileController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyProfileController>(
      init: MyProfileController(),
      builder: (controller) {
        return Obx(() => Scaffold(
              body: Padding(
                padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(15)),
                  child: (controller.hasData.value)
                      ? SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: MySize.size10,
                              ),
                              Text(
                                "Profile",
                                style: TextStyle(
                                  fontSize: MySize.size20,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              SizedBox(
                                height: MySize.size20,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // ClipRRect(
                                  //   child: Image(
                                  //     image: AssetImage("assets/man_logo1.jpg"),
                                  //     height: MySize.size100,
                                  //   ),
                                  // ),
                                  isNullEmptyOrFalse(controller.userProfileModel!.value.data!.imageUrl)
                                      ? CircleAvatar(
                                          radius: MySize.size52,
                                          backgroundColor: appTheme.primaryTheme,
                                          child: CircleAvatar(
                                            backgroundColor: Colors.white,
                                            radius: MySize.size50,
                                            child: Center(
                                              child: Image(
                                                image: AssetImage("assets/user.png"),
                                                height: MySize.size40,
                                              ),
                                            ),
                                          ),
                                        )
                                      : ClipRRect(
                                          borderRadius: BorderRadius.circular(200),
                                          child: CircleAvatar(
                                            backgroundColor: Colors.white,
                                            radius: MySize.size50,
                                            child: Center(
                                              child: CommonNetworkImageView(
                                                url: controller.userProfileModel!.value.data!.imageUrl.toString(),
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                        ),
                                  SizedBox(
                                    width: MySize.getScaledSizeWidth(15),
                                  ),
                                  Expanded(
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                isNullEmptyOrFalse(controller.userProfileModel!.value.data!.firstName)
                                                    ? "N/A"
                                                    : controller.userProfileModel!.value.data!.firstName.toString(),
                                                style: TextStyle(fontWeight: FontWeight.w500, fontSize: MySize.size18),
                                              ),
                                              SizedBox(
                                                height: MySize.size8,
                                              ),
                                              // Text(
                                              //   isNullEmptyOrFalse(controller
                                              //           .userProfileModel!
                                              //           .value
                                              //           .data!
                                              //           .about)
                                              //       ? "N/A"
                                              //       : controller
                                              //           .userProfileModel!
                                              //           .value
                                              //           .data!
                                              //           .about
                                              //           .toString(),
                                              //   style: TextStyle(
                                              //       fontSize: MySize.size14),
                                              // ),
                                              // SizedBox(
                                              //   height: MySize.size3,
                                              // ),
                                              Text(
                                                isNullEmptyOrFalse(controller.userProfileModel!.value.data!.mobile)
                                                    ? "N/A"
                                                    : controller.userProfileModel!.value.data!.mobile.toString(),
                                                style: TextStyle(fontSize: MySize.size14),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    width: MySize.getScaledSizeWidth(5),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      Get.toNamed(Routes.editProfile, arguments: {
                                        "userData": controller.userProfileModel!.value.data,
                                      });
                                    },
                                    child: SvgPicture.asset(
                                      "assets/edit.svg",
                                      height: MySize.size18,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: MySize.size20,
                              ),
                              Container(
                                height: MySize.size1,
                                color: appTheme.textGrayColor.withOpacity(0.5),
                              ),
                              SizedBox(
                                height: MySize.size30,
                              ),
                              InkWell(
                                onTap: () {
                                  Get.toNamed(Routes.paymentMethod);
                                },
                                child: getListRow(img: "assets/payment.svg", name: "Payment History"),
                              ),
                              SizedBox(
                                height: MySize.size30,
                              ),
                              InkWell(
                                onTap: () {
                                  Get.toNamed(Routes.invoiceList);
                                },
                                child: getListRow(img: "assets/invoice.svg", name: "Invoice"),
                              ),
                              SizedBox(
                                height: MySize.size30,
                              ),
                              InkWell(
                                onTap: () {
                                  Get.toNamed(Routes.termsCondition);
                                },
                                child: getListRow(img: "assets/terms.svg", name: "Terms & Condition"),
                              ),
                              SizedBox(
                                height: MySize.size30,
                              ),
                              InkWell(
                                  onTap: () {
                                    Get.toNamed(Routes.reportIssue);
                                  },
                                  child: getListRow(img: "assets/report_new.svg", name: "Report & Issues")),
                              SizedBox(
                                height: MySize.size30,
                              ),
                              InkWell(
                                  onTap: () async {
                                    GetStorage box = GetStorage();
                                    // box.write(Constant.tokenKey, null);
                                    print(box.read(Constant.loginMethod));
                                    if (box.read(Constant.loginMethod) == "google") {
                                      final GoogleSignIn googleSignIn = GoogleSignIn();

                                      final GoogleSignInAccount? googleSignInAccount = await googleSignIn.signOut();

                                      await FirebaseAuth.instance.signOut().then((value) {});
                                    }
                                    box.write(Constant.tokenKey, null);
                                    box.write(Constant.loginMethod, null);
                                    box.remove(Constant.tokenKey);
                                    FCMService().fcmUnSubscribe();
                                    // await FirebaseAuth.instance.signOut();
                                    Get.offAllNamed(Routes.loginScreen);
                                  },
                                  child: getListRow(
                                    img: "assets/log-out.svg",
                                    name: "Log Out",
                                  )),
                              SizedBox(
                                height: MySize.size30,
                              ),
                            ],
                          ),
                        )
                      : Center(
                          child: getShimerForMoreProfiile(),
                        ),
                ),
              ),
            ));
      },
    );
  }

  Widget getListRow({String? name, String? img}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SizedBox(
              width: MySize.getScaledSizeWidth(20),
            ),
            Container(
              width: MySize.getScaledSizeWidth(30),
              child: SvgPicture.asset(
                img.toString(),
                height: MySize.size22,
              ),
            ),
            SizedBox(
              width: MySize.getScaledSizeWidth(20),
            ),
            Text(
              name!,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: MySize.size18,
              ),
            ),
          ],
        ),
        Icon(
          Icons.arrow_forward_ios,
          size: MySize.size18,
        ),
      ],
    );
  }
}
