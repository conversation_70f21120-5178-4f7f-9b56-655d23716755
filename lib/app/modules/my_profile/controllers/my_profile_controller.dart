import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:small_officer/Models/user_profile_model.dart';
import 'package:small_officer/data/network_client.dart';
import '../../../../constants/api_constant.dart';

class MyProfileController extends GetxController {
  //TODO: Implement MyProfileController

  final count = 0.obs;
  RxBool hasData = false.obs;
  Rx<UserProfileModel>? userProfileModel;
  @override
  void onInit() {
    super.onInit();

    callApiForGetSourceTypeList(context: Get.context!);
  }

  callApiForGetSourceTypeList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    Map<String, dynamic> dict = {};

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.userProfile,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        userProfileModel = UserProfileModel.fromJson(response).obs;
        print(userProfileModel!.value.data?.firstName);
      },
      failureCallback: (status, message) {
        hasData.value = true;

        //app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
