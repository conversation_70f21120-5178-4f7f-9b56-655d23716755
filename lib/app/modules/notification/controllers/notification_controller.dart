import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../Models/notification_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../../home_screen/controllers/home_screen_controller.dart';

class NotificationController extends GetxController {
  //TODO: Implement NotificationController

  final count = 0.obs;
  RxBool hasData = false.obs;
  NotificationModel? notificationModel;
  HomeScreenController homeScreenController = Get.put(HomeScreenController());

  SharedPreferences? prefs;
  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      callApiForGetNotificationData(context: Get.context!);
    });
  }

 /* @override
  void onReady() {
    super.onReady();
  }*/

  callApiForGetNotificationData(
      {required BuildContext context, int? LocationId}) {
    hasData.value = false;
    // FocusScope.of(context).unfocus();
    // app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    // GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.notification,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        // app.resolve<CustomDialogs>().hideCircularDialog(context);
        notificationModel = NotificationModel.fromJson(response);
        // int n = i - prefs!.getInt(Constant.notiCount)!.toInt();
        //box.write("notiCount", i.toInt());

        prefs!.setInt(Constant.notiCountForAlert,
            notificationModel!.data!.length.toInt());
        print(prefs!.getInt(Constant.notiCountForAlert)!.toInt().toString());
        homeScreenController.countNewNotiAlert.value = 0.toInt();
      },
      failureCallback: (status, message) {
        hasData.value = true;

        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForDeleteNotificationData(
      {required BuildContext context, int? notificationId}) {
    // FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    // GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.notification}/$notificationId",
      MethodType.delete,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        // hasData.value = true;
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        //notificationModel = NotificationModel.fromJson(response);
      },
      failureCallback: (status, message) {
        // hasData.value = true;

        app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForDeleteAllNotificationData(
      {required BuildContext context, int? notificationId}) {
    // FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.notificationDelete,
      MethodType.delete,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        // hasData.value = true;
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        callApiForGetNotificationData(context: context);
        //notificationModel = NotificationModel.fromJson(response);
      },
      failureCallback: (status, message) {
        // hasData.value = true;

        app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
