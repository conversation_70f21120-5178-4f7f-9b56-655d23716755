import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/Models/notification_model.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/utilities/utilities.dart';

import '../../../../constants/size_constant.dart';
import '../controllers/notification_controller.dart';

class NotificationView extends GetWidget<NotificationController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  top: MySize.size17!,
                  right: MySize.size13!,
                  bottom: MySize.size17!),
              child: SvgPicture.asset(
                "assets/arrow_back.svg",
                width: MySize.getScaledSizeWidth(40),
              ),
            ),
          ),
          title: Container(
            // color: Colors.red,
            width: MySize.getScaledSizeWidth(192),
            height: MySize.getScaledSizeHeight(55),
            child: Image(
              image: AssetImage("assets/logo1.jpg"),
              fit: BoxFit.fill,
            ),
          ),
          centerTitle: true,
          elevation: 0,
        ),
        body: Container(
          padding: EdgeInsets.symmetric(
            horizontal: MySize.getScaledSizeWidth(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Notifications",
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: MySize.size22,
                    ),
                  ),
                  if ((controller.notificationModel != null &&
                      controller.notificationModel!.data != null &&
                      controller.notificationModel!.data!.isNotEmpty))
                    InkWell(
                      onTap: () {
                        _asyncConfirmDialog(context, controller);
                      },
                      child: Text(
                        "Clear All",
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: MySize.size18,
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(
                height: MySize.size10,
              ),
              Container(
                height: MySize.size1,
                width: double.infinity,
                color: appTheme.textGrayColor,
              ),
              SizedBox(
                height: MySize.size10,
              ),
              Expanded(
                  child: (controller.hasData.value)
                      ? ((controller.notificationModel != null &&
                              controller.notificationModel!.data != null &&
                              controller.notificationModel!.data!.isNotEmpty)
                          ? ListView.separated(
                              itemBuilder: (context, i) {
                                NotificationD n =
                                    controller.notificationModel!.data![i];
                                return Slidable(
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: MySize.getScaledSizeWidth(15),
                                      vertical: MySize.size10!,
                                    ),
                                    decoration: BoxDecoration(
                                        color: Color(0xffF8F8FA),
                                        borderRadius: BorderRadius.circular(
                                            MySize.size5!),
                                        border: Border.all(
                                            color: appTheme.borderColor)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          n.title.toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontSize: MySize.size16,
                                          ),
                                        ),
                                        SizedBox(
                                          height: MySize.size4,
                                        ),
                                        Text(
                                          n.body.toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.normal,
                                            fontSize: MySize.size14,
                                          ),
                                          maxLines: 3,
                                        ),
                                        SizedBox(
                                          height: MySize.size4,
                                        ),
                                        Row(
                                          children: [
                                            Expanded(child: Container()),
                                            Text(
                                              // "19:20   01-01-2022",
                                              DateFormat('hh:mm a  dd-MM-yyyy')
                                                  .format(getDateFromString(
                                                          n.createdAt!)
                                                      .toLocal())
                                                  .toString(),
                                              style: TextStyle(
                                                  fontWeight: FontWeight.normal,
                                                  fontSize: MySize.size14,
                                                  color:
                                                      appTheme.textGrayColor),
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                  startActionPane: ActionPane(
                                    motion: ScrollMotion(),
                                    children: [
                                      // SlidableAction(
                                      //   // An action can be bigger than the others.
                                      //   flex: 2,
                                      //   onPressed: doNothing,
                                      //   backgroundColor: Color(0xFF7BC043),
                                      //   foregroundColor: Colors.white,
                                      //   icon: Icons.archive,
                                      //   label: 'Archive',
                                      // ),
                                      SlidableAction(
                                        onPressed: (val) async {
                                          await controller
                                              .callApiForDeleteNotificationData(
                                                  context: context,
                                                  notificationId: n.id);
                                          controller
                                              .callApiForGetNotificationData(
                                                  context: context);
                                          controller.notificationModel!.data!
                                              .removeAt(i);
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  content: Text(
                                                      'Notification Deleted')));
                                        },
                                        backgroundColor: Colors.red,
                                        foregroundColor: Colors.white,
                                        icon: Icons.delete,
                                        // flex: 1,
                                        label: 'Delete',
                                      ),
                                    ],
                                  ),
                                  endActionPane: ActionPane(
                                    motion: ScrollMotion(),
                                    children: [
                                      // SlidableAction(
                                      //   // An action can be bigger than the others.
                                      //   flex: 2,
                                      //   onPressed: doNothing,
                                      //   backgroundColor: Color(0xFF7BC043),
                                      //   foregroundColor: Colors.white,
                                      //   icon: Icons.archive,
                                      //   label: 'Archive',
                                      // ),
                                      SlidableAction(
                                        onPressed: (val) async {
                                          await controller
                                              .callApiForDeleteNotificationData(
                                                  context: context,
                                                  notificationId: n.id);
                                          controller
                                              .callApiForGetNotificationData(
                                                  context: context);
                                          controller.notificationModel!.data!
                                              .removeAt(i);
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(SnackBar(
                                                  content: Text(
                                                      'Notification Deleted')));
                                        },
                                        backgroundColor: Colors.red,
                                        foregroundColor: Colors.white,
                                        icon: Icons.delete,
                                        // flex: 1,
                                        label: 'Delete',
                                      ),
                                    ],
                                  ),
                                );
                                // return Dismissible(
                                //   key: UniqueKey(),
                                //   onDismissed: (direction) async {
                                //     await controller
                                //         .callApiForDeleteNotificationData(
                                //             context: context,
                                //             notificationId: n.id);
                                //     controller.callApiForGetNotificationData(
                                //         context: context);
                                //     controller.notificationModel!.data!
                                //         .removeAt(i);
                                //     ScaffoldMessenger.of(context).showSnackBar(
                                //         SnackBar(
                                //             content:
                                //                 Text('Notification Deleted')));
                                //   },
                                //   child: Container(
                                //     padding: EdgeInsets.symmetric(
                                //       horizontal: MySize.getScaledSizeWidth(15),
                                //       vertical: MySize.size10!,
                                //     ),
                                //     decoration: BoxDecoration(
                                //         color: Color(0xffF8F8FA),
                                //         borderRadius: BorderRadius.circular(
                                //             MySize.size5!),
                                //         border: Border.all(
                                //             color: appTheme.borderColor)),
                                //     child: Column(
                                //       crossAxisAlignment:
                                //           CrossAxisAlignment.start,
                                //       children: [
                                //         Text(
                                //           n.title.toString(),
                                //           style: TextStyle(
                                //             fontWeight: FontWeight.w500,
                                //             fontSize: MySize.size16,
                                //           ),
                                //         ),
                                //         SizedBox(
                                //           height: MySize.size4,
                                //         ),
                                //         Text(
                                //           n.body.toString(),
                                //           style: TextStyle(
                                //             fontWeight: FontWeight.normal,
                                //             fontSize: MySize.size14,
                                //           ),
                                //           maxLines: 3,
                                //         ),
                                //         SizedBox(
                                //           height: MySize.size4,
                                //         ),
                                //         Row(
                                //           children: [
                                //             Expanded(child: Container()),
                                //             Text(
                                //               // "19:20   01-01-2022",
                                //               DateFormat('hh:mm a  dd-MM-yyyy')
                                //                   .format(getDateFromString(
                                //                       n.createdAt.toString())),
                                //               style: TextStyle(
                                //                   fontWeight: FontWeight.normal,
                                //                   fontSize: MySize.size14,
                                //                   color:
                                //                       appTheme.textGrayColor),
                                //             ),
                                //           ],
                                //         )
                                //       ],
                                //     ),
                                //   ),
                                // );
                              },
                              separatorBuilder: (context, i) {
                                return SizedBox(
                                  height: MySize.size15,
                                );
                              },
                              itemCount:
                                  controller.notificationModel!.data!.length,
                            )
                          : Center(
                              child: Text("No notification found"),
                            ))
                      : Center(
                          child: getShimerForNotification(),
                        ))
            ],
          ),
        ),
      );
    });
  }

  _asyncConfirmDialog(
      BuildContext context, NotificationController controller) async {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button for close dialog!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Delete Notifications'),
          content: const Text('Are you sure delete all notifications?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: const Text(
                'Delete',
                style: TextStyle(
                  color: Colors.white,
                ),
              ),
              style:
                  TextButton.styleFrom(backgroundColor: appTheme.primaryTheme),

              //  color: appTheme.primaryTheme,
              onPressed: () {
                Navigator.of(context).pop();
                controller.callApiForDeleteAllNotificationData(
                  context: context,
                );
              },
            )
          ],
        );
      },
    );
  }
}
