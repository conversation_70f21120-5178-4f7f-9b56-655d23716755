import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:marquee/marquee.dart';
import 'package:small_officer/constants/color_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../constants/size_constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../../../../utilities/submit_button.dart';
import '../../../../utilities/utilities.dart';
import '../../../routes/app_pages.dart';
import '../controllers/lock_screen_controller.dart';

class LockScreenView extends GetWidget<LockScreenController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  right: MySize.size13!,
                  top: MySize.size17!,
                  bottom: MySize.size17!),
              child: SvgPicture.asset(
                "assets/arrow_back.svg",
              ),
            ),
          ),
          title: Container(
            // color: Colors.red,
            width: MySize.getScaledSizeWidth(192),
            height: MySize.getScaledSizeHeight(55),
            child: Image(
              image: AssetImage("assets/logo1.jpg"),
              fit: BoxFit.fill,
            ),
          ),
          actions: [
            InkWell(
              onTap: () {
                Get.toNamed(Routes.notification);
              },
              child: Padding(
                padding: EdgeInsets.only(
                    left: MySize.getScaledSizeWidth(15),
                    right: MySize.getScaledSizeWidth(15),
                    top: MySize.size17!,
                    bottom: MySize.size17!),
                child: Image(
                  image: AssetImage("assets/notification.png"),
                ),
              ),
            ),
          ],
          centerTitle: true,
          elevation: 0,
        ),
        body: Container(
          width: MySize.screenWidth,
          height: MySize.screenHeight,
          padding: EdgeInsets.symmetric(
              horizontal: MySize.getScaledSizeWidth(15),
              vertical: MySize.size10!),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 20,
                child: Marquee(
                  text:
                      'This screen will show your reservations and allow you to unlock doors 15 minutes prior. For last minute bookings, please message us and we will open the door manually.',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.red,
                  ),
                  scrollAxis: Axis.horizontal,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  blankSpace: 20.0,
                  velocity: 50.0,
                  pauseAfterRound: Duration(seconds: 1),
                  startPadding: 10.0,
                  accelerationDuration: Duration(seconds: 1),
                  accelerationCurve: Curves.linear,
                  decelerationDuration: Duration(milliseconds: 500),
                  decelerationCurve: Curves.easeOut,
                ),
              ),
              SizedBox(
                height: MySize.size10,
              ),
              Text(
                "All Booking",
                style: TextStyle(
                  fontSize: MySize.size20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Expanded(
                  child: (!controller.hasData.value)
                      ? Center(
                          child: getShimerForBookingList(),
                        )
                      : ((controller.bookingDataModel != null &&
                              controller.bookingDataModel!.data != null &&
                              controller.bookingDataModel!.data!.isNotEmpty)
                          ? Container(
                              padding: EdgeInsets.only(top: MySize.size15!),
                              child: ListView.separated(
                                itemBuilder: (context, i) {
                                  return InkWell(
                                    onTap: () {
                                      Get.toNamed(Routes.productDetail,
                                          arguments: {
                                            "isBooked": true,
                                            StringConstant.productData:
                                                controller.bookingDataModel!
                                                    .data![i].resource,
                                            StringConstant.bookingModel:
                                                controller
                                                    .bookingDataModel!.data![i],
                                          });
                                    },
                                    child: Column(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  color:
                                                      BaseTheme().borderColor),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      MySize.size10!)),
                                          padding: EdgeInsets.symmetric(
                                            horizontal:
                                                MySize.getScaledSizeWidth(10),
                                            vertical: MySize.size10!,
                                          ),
                                          child: Row(
                                            children: [
                                              ClipRRect(
                                                // child: Image(
                                                //   image: AssetImage(
                                                //       "assets/table.png"),
                                                //   fit: BoxFit.cover,
                                                //   height: MySize.size140,
                                                //   width: MySize.size140,
                                                // ),
                                                child: CommonNetworkImageView(
                                                  url: (controller
                                                              .bookingDataModel!
                                                              .data![i]
                                                              .resource!
                                                              .imageUrl !=
                                                          null)
                                                      ? controller
                                                          .bookingDataModel!
                                                          .data![i]
                                                          .resource!
                                                          .imageUrl
                                                          .toString()
                                                      : "",
                                                  height: MySize.size140!,
                                                  width: MySize.size140!,
                                                  fit: BoxFit.fill,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              SizedBox(
                                                width:
                                                    MySize.getScaledSizeWidth(
                                                        7),
                                              ),
                                              Expanded(
                                                child: Container(
                                                  // height: MySize.size140,
                                                  width: MySize.size140,
                                                  child: Column(
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text(
                                                            "#${controller
                                                                    .bookingDataModel!
                                                                    .data![i]
                                                                    .id}",
                                                            style: TextStyle(
                                                              color: BaseTheme()
                                                                  .textGrayColor,
                                                              fontSize:
                                                                  MySize.size16,
                                                            ),
                                                          ),
                                                          Text(
                                                            "\$${controller.bookingDataModel!.data![i].resource!.ratePerHour} /Hour",
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize:
                                                                  MySize.size16,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            controller
                                                                .bookingDataModel!
                                                                .data![i]
                                                                .resource!
                                                                .name
                                                                .toString(),
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize:
                                                                  MySize.size16,
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            height:
                                                                MySize.size10,
                                                          ),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .start,
                                                            children: [
                                                              SvgPicture.asset(
                                                                "assets/location.svg",
                                                                height: MySize
                                                                    .size14,
                                                                color: BaseTheme()
                                                                    .textGrayColor,
                                                              ),
                                                              Expanded(
                                                                child: Text(
                                                                  " ${controller.bookingDataModel!.data![i].resource!.address.toString()}",
                                                                  style: TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .normal,
                                                                      fontSize:
                                                                          MySize
                                                                              .size16,
                                                                      color: BaseTheme()
                                                                          .textGrayColor),
                                                                  maxLines: 4,
                                                                ),
                                                              ),
                                                            ],
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                          ),
                                                          SizedBox(
                                                            height:
                                                                MySize.size8,
                                                          ),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  SvgPicture
                                                                      .asset(
                                                                    "assets/date.svg",
                                                                    height: MySize
                                                                        .size14,
                                                                  ),
                                                                  (controller
                                                                              .bookingDataModel!
                                                                              .data![i]
                                                                              .startAt !=
                                                                          null)
                                                                      ? Text(
                                                                          " ${DateFormat("MM.dd.yyyy").format(getDateFromStringFromUtc(
                                                                                controller.bookingDataModel!.data![i].startAt.toString(),
                                                                              ).toLocal())} | ${DateFormat("hh:mm a").format(getDateFromStringFromUtc(
                                                                                controller.bookingDataModel!.data![i].startAt.toString(),
                                                                              ).toLocal())}",
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                MySize.size16,
                                                                          ),
                                                                        )
                                                                      : SizedBox(),
                                                                ],
                                                              ),
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  SvgPicture
                                                                      .asset(
                                                                    "assets/time.svg",
                                                                    height: MySize
                                                                        .size14,
                                                                  ),
                                                                  Text(
                                                                    " ${getDateFromString(
                                                                          controller
                                                                              .bookingDataModel!
                                                                              .data![i]
                                                                              .endAt
                                                                              .toString(),
                                                                        )
                                                                            .difference(getDateFromString(
                                                                              controller.bookingDataModel!.data![i].startAt.toString(),
                                                                            ))
                                                                            .inHours} H",
                                                                    style: TextStyle(
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .normal,
                                                                        fontSize:
                                                                            MySize
                                                                                .size16,
                                                                        color: BaseTheme()
                                                                            .textGrayColor),
                                                                  ),
                                                                ],
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(
                                                            height:
                                                                MySize.size8,
                                                          ),
                                                          Row(
                                                            children: [
                                                              if (getButtonVisibleOrNot(
                                                                  c: controller,
                                                                  i: i))
                                                                SizedBox(
                                                                  width: 10,
                                                                ),
                                                              InkWell(
                                                                onTap: () {
                                                                  Get.toNamed(
                                                                      Routes
                                                                          .productDetail,
                                                                      arguments: {
                                                                        "isBooked":
                                                                            true,
                                                                        "isBooked":
                                                                            true,
                                                                        StringConstant.productData: controller
                                                                            .bookingDataModel!
                                                                            .data![i]
                                                                            .resource,
                                                                        StringConstant.bookingModel: controller
                                                                            .bookingDataModel!
                                                                            .data![i],
                                                                      });
                                                                },
                                                                child: button(
                                                                    title:
                                                                        "View",
                                                                    width: 90,
                                                                    height: 35),
                                                              ),
                                                              Space.width(12),
                                                              InkWell(
                                                                child: button(
                                                                    title:
                                                                        "Unlock Door",
                                                                    backgroundColor:
                                                                        BaseTheme()
                                                                            .newPrimaryColor,
                                                                    textColor:
                                                                        Colors
                                                                            .white,
                                                                    borderColor:
                                                                        BaseTheme()
                                                                            .newPrimaryColor,
                                                                    width: 120,
                                                                    height: 35),
                                                                onTap: () {
                                                                  Map<String,
                                                                          dynamic>
                                                                      dict = {
                                                                    "context": {
                                                                      "location":
                                                                          {
                                                                        "longitude":
                                                                            0,
                                                                        "latitude":
                                                                            0
                                                                      }
                                                                    },
                                                                    "lock": {
                                                                      "proximity_proof":
                                                                          "string"
                                                                    }
                                                                  };
                                                                  // GetStorage box = GetStorage();
                                                                  app
                                                                      .resolve<
                                                                          CustomDialogs>()
                                                                      .showCircularDialog(
                                                                          context);

                                                                  NetworkClient
                                                                      .getInstance
                                                                      .callApi(
                                                                    context,
                                                                    "https://api.kisi.io/locks/",
                                                                    '${controller.bookingDataModel!.data![i].resource!.lockId!}/unlock',
                                                                    MethodType
                                                                        .post,
                                                                    headers: {
                                                                      'Authorization':
                                                                          'KISI-LOGIN 1a91144f7a387c1009de22561372b8d6',
                                                                      'Content-Type':
                                                                          'application/json',
                                                                      "accept":
                                                                          "application/json",
                                                                    },
                                                                    params:
                                                                        jsonEncode(
                                                                            dict),
                                                                    successCallback:
                                                                        (response,
                                                                            message) {
                                                                      print(
                                                                          response);

                                                                      app
                                                                          .resolve<
                                                                              CustomDialogs>()
                                                                          .hideCircularDialog(
                                                                              context);
                                                                      Map<String,
                                                                              dynamic>
                                                                          dict =
                                                                          {
                                                                        "context":
                                                                            {
                                                                          "location":
                                                                              {
                                                                            "longitude":
                                                                                0,
                                                                            "latitude":
                                                                                0
                                                                          }
                                                                        },
                                                                        "lock":
                                                                            {
                                                                          "proximity_proof":
                                                                              "string"
                                                                        }
                                                                      };

                                                                      app
                                                                          .resolve<
                                                                              CustomDialogs>()
                                                                          .showCircularDialog(
                                                                              context);

                                                                      return NetworkClient
                                                                          .getInstance
                                                                          .callApi(
                                                                        context,
                                                                        "https://api.kisi.io/locks/",
                                                                        '${controller.bookingDataModel!.data![i].resource!.location!.lockId!}/unlock',
                                                                        MethodType
                                                                            .post,
                                                                        headers: {
                                                                          'Authorization':
                                                                              'KISI-LOGIN 1a91144f7a387c1009de22561372b8d6',
                                                                          'Content-Type':
                                                                              'application/json',
                                                                          "accept":
                                                                              "application/json",
                                                                        },
                                                                        params:
                                                                            jsonEncode(dict),
                                                                        successCallback:
                                                                            (response,
                                                                                message) {
                                                                          print(
                                                                              response);

                                                                          app.resolve<CustomDialogs>().hideCircularDialog(
                                                                              context);
                                                                          app.resolve<CustomDialogs>().getDialog(
                                                                              desc: "Please open door.",
                                                                              title: "Success");
                                                                        },
                                                                        failureCallback:
                                                                            (status,
                                                                                message) {
                                                                          app.resolve<CustomDialogs>().hideCircularDialog(
                                                                              context);
                                                                          app.resolve<CustomDialogs>().getDialog(
                                                                              desc: "Please message us if the door is not opening.",
                                                                              title: "Error");

                                                                          print(
                                                                              " error");
                                                                        },
                                                                      );
                                                                    },
                                                                    failureCallback:
                                                                        (status,
                                                                            message) {
                                                                      app
                                                                          .resolve<
                                                                              CustomDialogs>()
                                                                          .hideCircularDialog(
                                                                              context);
                                                                      app.resolve<CustomDialogs>().getDialog(
                                                                          desc:
                                                                              "Please message us if the door is not opening.",
                                                                          title:
                                                                              "Error");

                                                                      print(
                                                                          " error");
                                                                    },
                                                                  );
                                                                },
                                                              ),
                                                            ],
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                          )
                                                        ],
                                                      )
                                                    ],
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                                separatorBuilder: (context, i) {
                                  return SizedBox(
                                    height: MySize.size30,
                                  );
                                },
                                itemCount:
                                    controller.bookingDataModel!.data!.length,
                              ),
                            )
                          : Center(
                              child: Text(
                                "No booking data found",
                                style: TextStyle(fontSize: MySize.size14!),
                              ),
                            ))),
            ],
          ),
        ),
      );
    });
  }

  getButtonVisibleOrNot({LockScreenController? c, int i = 0}) {
    if (c!.bookingDataModel!.data![i].plan!) {
      return false;
    } else {
      if (getDateFromString(c.bookingDataModel!.data![i].createdAt!)
          .add(Duration(days: 1))
          .isAfter(DateTime.now())) {
        return true;
      } else {
        return false;
      }
    }
  }
}
