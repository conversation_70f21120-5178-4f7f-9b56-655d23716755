import 'dart:convert';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/utilities/text_field.dart';

import '../../../../constants/color_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../constants/size_constant.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/chat_detail_controller.dart';
import 'package:path/path.dart' as p;

class ChatDetailView extends GetWidget<ChatDetailController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ChatDetailController>(
        init: ChatDetailController(),
        builder: (controller) {
          return Scaffold(
            appBar: AppBar(
              leading: InkWell(
                onTap: () {
                  Get.back();
                },
                child: Padding(
                  padding: EdgeInsets.only(
                      left: MySize.getScaledSizeWidth(15),
                      top: MySize.size17!,
                      right: MySize.size13!,
                      bottom: MySize.size17!),
                  child: SvgPicture.asset(
                    "assets/arrow_back.svg",
                  ),
                ),
              ),
              title: Container(
                // color: Colors.red,
                width: MySize.getScaledSizeWidth(192),
                height: MySize.getScaledSizeHeight(55),
                child: Image(
                  image: AssetImage("assets/logo1.jpg"),
                  fit: BoxFit.fill,
                ),
              ),
              centerTitle: true,
              backgroundColor: Colors.white,
              elevation: 0,
              automaticallyImplyLeading: true,
            ),
            body: Container(
              height: MySize.screenHeight,
              width: MySize.screenWidth,
              padding: EdgeInsets.symmetric(
                horizontal: MySize.getScaledSizeWidth(15),
                vertical: MySize.size15!,
              ),
              child: Column(
                children: [
                  Expanded(
                    child: StreamBuilder(
                      stream: controller.databaseRef
                          .child("chats")
                          .child(
                              controller.box.read(Constant.userId).toString())
                          .orderByValue()
                          .onValue,
                      builder: (context, AsyncSnapshot snapShot) {
                        if (snapShot.connectionState ==
                            ConnectionState.waiting) {
                          return Center(
                            child: CircularProgressIndicator(
                                color: appTheme.primaryTheme),
                          );
                        }
                        if (snapShot.hasData) {
                          DataSnapshot dataValues = snapShot.data!.snapshot;
                          if (dataValues.value != null) {
                            Map<dynamic, dynamic> values =
                                dataValues.value as Map<dynamic, dynamic>;

                            values.forEach((key, value) {
                              print(
                                  "$value------>>>>><<<<-------");
                            });
                            controller.messageList.clear();
                            values.forEach((key, value) {
                              controller.messageList.insert(
                                  0, messageItemFromJson(jsonEncode(value)));
                            });
                            controller.messageList.sort((a, b) {
                              return a.createdAt.compareTo(b.createdAt);
                            });
                            controller.messageList.value =
                                controller.messageList.reversed.toList();
                            return (controller.messageList.isEmpty)
                                ? SizedBox()
                                : Padding(
                                    padding:
                                        EdgeInsets.only(bottom: MySize.size70!),
                                    child: ListView.separated(
                                        reverse: true,
                                        itemBuilder: (context, index) {
                                          return (controller.messageList[index]
                                                      .sender !=
                                                  "Admin")
                                              ? yourMessage(
                                                  model: controller
                                                      .messageList[index],
                                                )
                                              : opponentMessage(
                                                  model: controller
                                                      .messageList[index],
                                                );
                                        },
                                        separatorBuilder: (context, index) {
                                          return SizedBox(
                                            height: 15,
                                          );
                                        },
                                        itemCount:
                                            controller.messageList.length),
                                  );
                          }

                          return Text("");
                        } else if (snapShot.hasError) {
                          print(snapShot.error.toString());
                          return Text(snapShot.error.toString());
                        }
                        return Text("");
                      },
                    ),
                  ),
                ],
              ),
            ),
            bottomSheet: SafeArea(
              child: Padding(
                padding: EdgeInsets.only(
                    left: MySize.getScaledSizeWidth(10),
                    right: MySize.getScaledSizeWidth(10),
                    bottom: MySize.getScaledSizeHeight(30)),
                child: getTextFormField(
                  isFillColor: false,
                  hintText: "Need help? Let us know!",
                  textEditingController: controller.msgController.value,
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(MySize.size20!),
                    child: Obx(() {
                      return InkWell(
                        onTap: () async {
                          final ImagePicker _picker = ImagePicker();
                          final XFile? image = await _picker.pickImage(
                              source: ImageSource.gallery);
                          if (image != null) {
                            File imagepath = File(image.path);
                            controller.imgFileName =
                                p.basenameWithoutExtension(imagepath.path);
                            controller.selectedImg = File(image.path).obs;
                            controller.hasImg.value = true;
                          }
                        },
                        child: Stack(
                          children: [
                            SvgPicture.asset(
                              "assets/file.svg",
                              height: MySize.size20,
                            ),
                            if (controller.hasImg.value)
                              Positioned(
                                top: 0,
                                right: 0,
                                child: CircleAvatar(
                                  radius: MySize.size5,
                                  backgroundColor: Colors.red,
                                ),
                              )
                          ],
                        ),
                      );
                    }),
                  ),
                  suffixIcon: InkWell(
                    onTap: () {
                      if (!isNullEmptyOrFalse(
                              controller.msgController.value.text) ||
                          controller.hasImg.value) {
                        if (controller.hasImg.value) {
                          controller.callApiForUploadImage(context: context);
                        } else {
                          controller.callApiFoSendMsg(context: context);
                        }
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.all(MySize.size20!),
                      child: SvgPicture.asset(
                        "assets/Send.svg",
                        height: MySize.size20,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        });
  }

  Column opponentMessage({MessageModel? model}) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(
            int.parse(model!.createdAt!.toString()))
        .toLocal();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // SizedBox(
            //   width: MySize.getScaledSizeWidth(10),
            // ),
            Column(
              children: [
                if (!isNullEmptyOrFalse((model.attachmentUrl)))
                  Container(
                    height: MySize.size250,
                    // width: MySize.size250,
                    child: CommonNetworkImageView(
                      url: model.attachmentUrl.toString(),
                      fit: BoxFit.cover,
                    ),
                  ),
                if (!isNullEmptyOrFalse((model.attachmentUrl)))
                  SizedBox(
                    height: MySize.getScaledSizeWidth(10),
                  ),
                if (!isNullEmptyOrFalse((model.message)))
                  Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.size20!, vertical: MySize.size10!),
                    width: MySize.getScaledSizeWidth(260),
                    decoration: BoxDecoration(
                      color: Color(0xffEEEEEE),
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(MySize.size15!),
                        topLeft: Radius.circular(MySize.size15!),
                        bottomRight: Radius.circular(MySize.size15!),
                      ),
                      border: Border.all(
                        color: Color(0xffD9D9D9),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          model.senderName.toString(),
                          style: TextStyle(
                            color: appTheme.primaryTheme,
                            fontWeight: FontWeight.w800,
                            fontSize: MySize.size16,
                          ),
                        ),
                        Space.height(10),
                        Text(model.message.toString()),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            DateFormat('hh:mm a dd-MM-yyyy').format(date),
                            style: TextStyle(
                                color: appTheme.primaryTheme,
                                fontSize: MySize.size14!),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            )
          ],
        ),
        // SizedBox(
        //   height: MySize.size5,
        // ),
        // Padding(
        //   padding: EdgeInsets.only(
        //     left: MySize.getScaledSizeWidth(50),
        //   ),
        //   child: Text(
        //     DateFormat('hh:mm a dd-MM-yyyy').format(date),
        //     style: TextStyle(
        //         color: appTheme.textGrayColor, fontSize: MySize.size14!),
        //   ),
        // )
      ],
    );
  }

  Column yourMessage({MessageModel? model}) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(
            int.parse(model!.createdAt.toString()))
        .toLocal();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: Container(),
            ),
            SizedBox(
              width: MySize.getScaledSizeWidth(10),
            ),
            Column(
              children: [
                if (!isNullEmptyOrFalse((model.attachmentUrl)))
                  Container(
                    height: MySize.size250,
                    // width: MySize.size250,
                    child: CachedNetworkImage(
                      // height: height,
                      // width: width,
                      // fit: fit,
                      imageUrl: model.attachmentUrl.toString(),
                      placeholder: (context, url) => Container(
                        height: 30,
                        width: 30,
                        child: LinearProgressIndicator(
                          color: Colors.grey.shade200,
                          backgroundColor: Colors.grey.shade100,
                        ),
                      ),
                      errorWidget: (context, url, error) => Image.asset(
                        'assets/table.png',
                      ),
                    ),
                  ),
                if (!isNullEmptyOrFalse((model.attachmentUrl)))
                  SizedBox(
                    height: MySize.getScaledSizeWidth(10),
                  ),
                if (!isNullEmptyOrFalse((model.message)))
                  Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.size20!, vertical: MySize.size10!),
                    width: MySize.getScaledSizeWidth(260),
                    decoration: BoxDecoration(
                      color: Color(0xffEEEEEE),
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(MySize.size15!),
                        topLeft: Radius.circular(MySize.size15!),
                        bottomRight: Radius.circular(MySize.size15!),
                      ),
                      border: Border.all(
                        color: Color(0xffD9D9D9),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Me",
                          style: TextStyle(
                            color: appTheme.primaryTheme,
                            fontWeight: FontWeight.w800,
                            fontSize: MySize.size16,
                          ),
                        ),
                        Space.height(10),
                        Text(model.message.toString()),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            DateFormat('hh:mm a dd-MM-yyyy').format(date),
                            style: TextStyle(
                                color: appTheme.primaryTheme,
                                fontSize: MySize.size14!),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            )
          ],
        ),
        // SizedBox(
        //   height: MySize.size5,
        // ),
        // Padding(
        //   padding: EdgeInsets.only(
        //     left: MySize.getScaledSizeWidth(50),
        //   ),
        //   child: Text(
        //     DateFormat('hh:mm a dd-MM-yyyy').format(date),
        //     style: TextStyle(
        //         color: appTheme.textGrayColor, fontSize: MySize.size14!),
        //   ),
        // )
      ],
    );
  }
}

MessageModel messageItemFromJson(String str) =>
    MessageModel.fromJson(json.decode(str));

class MessageModel {
  int? createdAt;
  String? message;
  int? senderId;
  String? senderName;
  String? sender;
  String? attachmentUrl;
  String? attachementType;

  MessageModel(
      {required this.createdAt,
      required this.message,
      required this.senderId,
      required this.sender,
      required this.attachmentUrl,
      required this.attachementType,
      required this.senderName});

  factory MessageModel.fromJson(Map<String, dynamic> json) => MessageModel(
        createdAt:
            (isNullEmptyOrFalse(json["createdAt"])) ? null : json["createdAt"],
        message: (isNullEmptyOrFalse(json["message"])) ? null : json["message"],
        senderId: (isNullEmptyOrFalse(json["senderId"]))
            ? null
            : (json["senderId"] is String)
                ? int.tryParse(json["senderId"])
                : json["senderId"],
        senderName: (isNullEmptyOrFalse(json["senderName"]))
            ? null
            : (json["senderName"] is String)
                ? json["senderName"]
                : json["senderName"].toString(),
        sender: (isNullEmptyOrFalse(json["sender"]))
            ? null
            : (json["sender"] is String)
                ? json["sender"]
                : json["sender"].toString(),
        attachmentUrl: (isNullEmptyOrFalse(json["attachmentUrl"]))
            ? null
            : (json["attachmentUrl"] is String)
                ? json["attachmentUrl"]
                : json["attachmentUrl"].toString(),
        attachementType: (isNullEmptyOrFalse(json["attachementType"]))
            ? null
            : (json["attachementType"] is String)
                ? json["attachementType"]
                : json["attachementType"].toString(),
      );

  Map<String, dynamic> toJson() => {
        "createdAt": (isNullEmptyOrFalse(createdAt)) ? null : createdAt,
        "message": (isNullEmptyOrFalse(message)) ? null : message,
        "senderId": (isNullEmptyOrFalse(senderId)) ? null : senderId,
      };
}
