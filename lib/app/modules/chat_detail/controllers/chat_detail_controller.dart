import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:firebase_database/firebase_database.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:small_officer/app/modules/home_screen/controllers/home_screen_controller.dart';

import 'package:socket_io_client/socket_io_client.dart' as IO;

import '../../../../constants/api_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../data/network_client.dart';

class ChatDetailController extends GetxController {
  late Rx<File> selectedImg;
  RxBool hasImg = false.obs;
  String? imgFileName;
  final count = 0.obs;
  IO.Socket? socket;
  Rx<TextEditingController> msgController = TextEditingController().obs;
  HomeScreenController homeScreenController = Get.put(HomeScreenController());
  SharedPreferences? prefs;

  DatabaseReference databaseRef = FirebaseDatabase(
          databaseURL: "https://small-officer-default-rtdb.firebaseio.com/")
      .reference();
  String? fileUrl;
  String? fileType;
  GetStorage box = GetStorage();
  RxList messageList = RxList([]);
  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      //  connectToServer();
      databaseRef
          .child("chats")
          .child(box.read(Constant.userId).toString())
          .once()
          .then((onValue) {
        DataSnapshot dataValues = onValue.snapshot;
        if (dataValues.value != null) {
          Map<dynamic, dynamic> values =
              dataValues.value as Map<dynamic, dynamic>;
          int i = 0;
          values.forEach((key, value) {
            i++;
          });

          //box.write("notiCount", i.toInt());

          prefs!.setInt(Constant.notiCount, i.toInt());
          print(prefs!.getInt(Constant.notiCount)!.toInt().toString());
          homeScreenController.countNewNoti.value = 0.toInt();
        }
      });
    });
  }

  callApiForUploadImage({
    required BuildContext context,
  }) async {
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    dict["file"] = hasImg.value
        ? await MultipartFile.fromFile(selectedImg.value.path,
            filename: imgFileName)
        : null;
    FormData formData = FormData.fromMap(dict);

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.uploadfile,
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: formData,
      successCallback: (response, message) {
        //  app.resolve<CustomDialogs>().hideCircularDialog(context);
        fileUrl = response["fileUrl"];
        fileType = response["fileType"];
        callApiFoSendMsg(context: context);
      },
      failureCallback: (status, message) {
        //  app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiFoSendMsg({
    required BuildContext context,
  }) async {
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();
    dict["message"] = msgController.value.text;
    dict["attachmentUrl"] = fileUrl;
    dict["attachementType"] = fileType;
    // FormData formData = new FormData.fromMap(dict);

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.chat}/${box.read(Constant.userId)}",
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        // app.resolve<CustomDialogs>().hideCircularDialog(context);
        msgController.value.clear();
        hasImg.value = false;
        fileUrl = null;
        fileType = null;
      },
      failureCallback: (status, message) {
        //app.resolve<CustomDialogs>().hideCircularDialog(context);
        // app
        //     .resolve<CustomDialogs>()
        //     .getDialog(title: "Success", desc: "Profile Update Sucessfully.");
        print(" error");
      },
    );
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  void connectToServer() {
    try {
      //IO.Socket socket = IO.io('http://localhost:3000');
      // socket.onConnect((_) {
      //   print('connect');
      //   socket.emit('msg', 'test');
      // });
      // Configure socket transports must be sepecified
      // socket =
      //     IO.io('https://backend.admin-smallofficer.com', <String, dynamic>{
      //   'transports': ['websocket'],
      //   'autoConnect': true,
      // }).connect();
      socket =
          IO.io('https://backend.admin-smallofficer.com', <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': true,
      }).connect();
      // socket = IO.io(
      //   'https://backend.admin-smallofficer.com',
      //   IO.OptionBuilder()
      //       .setTransports(['websocket'])
      //       .disableAutoConnect()
      //       .build(),
      // );

      // Connect to websocket
      socket!.connect();
      print(socket!.connected);
      socket!.onConnect((_) {
        print('connect');
        socket!.emit("join_room", {"UserId": 21});
      });
      socket!.onConnectError((_) {
        print(_);
      });

      // Handle socket events
      socket!.on('recieveFromAdmin', (data) {
        print(data);
      });
      //
      socket!.on('disconnect', (_) => print('disconnect'));
      // socket!.on('fromServer', (_) => print(_));
    } catch (e) {
      print(e.toString());
    }
  }

  // Send Location to Server
  sendLocation(Map<String, dynamic> data) {
    socket!.emit("location", data);
  }

  // Listen to Location updates of connected usersfrom server
  handleLocationListen(Map<String, dynamic> data) async {
    print(data);
  }

  // Send update of user's typing status
  sendTyping(bool typing) {
    socket!.emit("typing", {
      "id": socket!.id,
      "typing": typing,
    });
  }

  // Listen to update of typing status from connected users
  void handleTyping(Map<String, dynamic> data) {
    print(data);
  }

  // Send a Message to the server
  sendMessage(String message) {
    socket!.emit("sendToAdmin", {
      "UserId": 21,
      "msg": "Hello",
      "attachmentUrl": null,
      "attachementType": null
    });
  }

  // Listen to all message events from connected users
  void handleMessage(Map<String, dynamic> data) {
    print(data);
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
