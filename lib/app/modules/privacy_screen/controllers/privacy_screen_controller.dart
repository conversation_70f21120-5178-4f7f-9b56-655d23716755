import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../../constants/color_constant.dart';

class PrivacyScreenController extends GetxController {
  //TODO: Implement PrivacyScreenController

  final count = 0.obs;
  RxBool hasLoaded = false.obs;
  final Completer<WebViewController> controller =
      Completer<WebViewController>();
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // app
      //     .resolve<CustomDialogs>()
      //     .showCircularDialog(Get.context!, isFromPrivacy: true);
      showDialog(
        context: Get.context!,
        builder: (BuildContext context) {
          return WillPopScope(
              child: Center(
                  child:
                      CircularProgressIndicator(color: appTheme.primaryTheme)),
              onWillPop: () async {
                return false;
              });
        },
        barrierDismissible: false,
      );
    });
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}
  void increment() => count.value++;
}
