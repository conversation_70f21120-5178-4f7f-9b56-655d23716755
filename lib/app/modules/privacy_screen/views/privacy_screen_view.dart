import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../constants/size_constant.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../../../../utilities/submit_button.dart';
import '../controllers/privacy_screen_controller.dart';

class PrivacyScreenView extends GetWidget<PrivacyScreenController> {
  @override
  Widget build(BuildContext context) {
    // app.resolve<CustomDialogs>().showCircularDialog(
    //       context,
    //     );
    return Scaffold(
      body: Container(
        height: MySize.screenHeight,
        padding: EdgeInsets.only(
            left: MySize.getScaledSizeWidth(20),
            right: MySize.getScaledSizeWidth(20),
            top: MediaQuery.of(context).padding.top),
        child: Column(
          children: [
            Expanded(
              child: WebView(
                initialUrl: 'https://www.smallofficer.com/privacy',
                gestureNavigationEnabled: false,
                backgroundColor: Colors.white,
                onProgress: (val) {},
                //javascriptMode: JavascriptMode.unrestricted,
                onWebViewCreated: (WebViewController webViewController) {
                  controller.controller.complete(webViewController);
                },
                onPageStarted: (String url) {
                  // app.resolve<CustomDialogs>().showCircularDialog(
                  //       context,
                  //     );
                },
                onPageFinished: (String url) {
                  controller.hasLoaded.value = true;
                  // print('Page finished loading: $url');
                  app.resolve<CustomDialogs>().hideCircularDialog(context);
                },
              ),
            ),
            Column(
              children: [
                // SizedBox(height: MySize.getScaledSizeHeight(30)),
                InkWell(
                    onTap: () {
                      // Get.toNamed(Routes.PRIVACY_SCREEN);
                    },
                    child: InkWell(
                      onTap: () {
                        Get.offAllNamed(Routes.homeScreen);
                      },
                      child: button(
                          title: "Accept",
                          backgroundColor: BaseTheme().newPrimaryColor,
                          width: MySize.getScaledSizeWidth(480)),
                    )),
                SizedBox(height: MySize.getScaledSizeHeight(20)),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
