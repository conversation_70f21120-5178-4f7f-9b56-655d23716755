import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:small_officer/constants/size_constant.dart';

import '../controllers/splash_screen_controller.dart';

class SplashScreenView extends GetWidget<SplashScreenController> {
  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: MySize.screenHeight,
              width: MySize.screenWidth,
              child: Image.asset(
                "assets/App1.jpg",
                fit: BoxFit.cover,
              ),
            ),
            // SizedBox(height: MySize.getScaledSizeHeight(15)),
            // Text(
            //   "Work From Here",
            //   style: TextStyle(fontSize: MySize.size20),
            // )
          ],
        ),
      ),
    );
  }
}
