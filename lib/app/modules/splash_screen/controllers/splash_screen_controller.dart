import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:connectivity/connectivity.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:small_officer/Models/app_config_model.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/api_constant.dart';
import 'package:small_officer/constants/constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:small_officer/data/network_client.dart';

import '../../../../main.dart';
import '../../no_internet/views/no_internet_view.dart';

class SplashScreenController extends GetxController {
  @override
  void onInit() async {
    GetStorage box = GetStorage();
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      log("result ==> $result");
      if (result == ConnectivityResult.none) {
        Get.toNamed(Routes.NO_INTERNET)?.then((value) => isNetworkPageOpen.value = false);
      } else {
        // if (isDashBoardOpen.value) {
        //   if (isNetworkPageOpen.value) {
        //     Get.back();
        //   }
        // } else {
        //   // here you must not navigate this page because you have to check first appconfig and user login or not
        //   // Get.offAllNamed(Routes.DASH_BOARD);
        // }
      }
    });

    Timer(Duration(seconds: 3), () => callApiForAppConfig(context: Get.context!)
        // Get.offAllNamed((!isNullEmptyOrFalse(box.read(Constant.tokenKey))) ? Routes.homeScreen : Routes.loginScreen),
        );
    super.onInit();
  }

  Rx<AppConfigData> appConfigData = AppConfigData().obs;

  RxString appVersion = ''.obs;
  initPackageInfo() async {
    if (!kIsWeb) {
      final PackageInfo info = await PackageInfo.fromPlatform();
      appVersion.value = info.version;
    }
  }

  callApiForAppConfig({required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    await initPackageInfo();
    log("APP VERSION :: ${appVersion.value}");
    return NetworkClient.getInstance.callApi(context, baseURL, ApiConstant.appConfig, MethodType.get,
        headers: NetworkClient.getInstance.getAuthHeaders(), successCallback: (response, message) async {
      AppConfigModel config = AppConfigModel.fromJson(response);
      appConfigData.value = config.data!;
      if (appConfigData.value.appInMaintenance ?? true) {
        Get.offAllNamed(Routes.APP_UNDER_MAINTENANCE);
        // Get.offAllNamed(Routes.APP_UPDATE_AVAILABLE, arguments: {"softUpdate": true});
        return;
      } else if ((appConfigData.value.forceUpdate ?? true) &&
          (Platform.isAndroid
              ? appVersion.value != appConfigData.value.androidVersionCode
              : appVersion.value != appConfigData.value.iosVersionCode)) {
        Get.offAllNamed(Routes.APP_UPDATE_AVAILABLE);
        return;
      } else if ((appConfigData.value.softUpdate ?? true) &&
          (Platform.isAndroid
              ? appVersion.value != appConfigData.value.androidVersionCode
              : appVersion.value != appConfigData.value.iosVersionCode)) {
        Get.offAllNamed(Routes.APP_UPDATE_AVAILABLE, arguments: {"softUpdate": true});
        return;
      } else {
        Get.offAllNamed((!isNullEmptyOrFalse(box.read(Constant.tokenKey))) ? Routes.homeScreen : Routes.loginScreen);
      }

      log("::::::::::::::::::::APP CONFIG $response");
    }, failureCallback: (status, message) {
      // hasData.value = true;
    });
  }

  @override
  void onClose() {}
}
