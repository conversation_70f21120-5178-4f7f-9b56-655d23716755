import 'package:connectivity/connectivity.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/app_button.dart';
import 'package:small_officer/constants/app_images.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/constant.dart';

import '../../../../constants/size_constant.dart';
import '../../../../main.dart';
import '../controllers/no_internet_controller.dart';

RxBool isNetworkPageOpen = false.obs;
RxBool isDashBoardOpen = false.obs;

class NoInternetView extends GetView<NoInternetController> {
  const NoInternetView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    isNetworkPageOpen.value = true;
    // ignore: deprecated_member_use
    return WillPopScope(
        child: Scaffold(
            body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                AppImages.appLogo,
                height: 94,
                // width: 89,
              ),
              Space.height(50),
              Image.asset(
                AppImages.noInternet,
                height: 220,
              ),
              Space.height(30),
              Text(
                "No Internet Connection",
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
              ),
              Space.height(10),
              Text(
                "Please check your internet connection and reopen the app.",
                style: TextStyle(fontSize: 16, color: BaseTheme().secondaryColor, fontWeight: FontWeight.w400),
                textAlign: TextAlign.center,
              ),
              Space.height(30),
              appButton(
                  btnText: "Try Again",
                  width: 124,
                  onTap: () {
                    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
                      print("RESULT :: $result");
                      if (result != ConnectivityResult.none) {
                        if (isNetworkPageOpen.value) {
                          if (isDashBoardOpen.value) {
                            Get.back();
                          } else {
                            Get.offAllNamed((!isNullEmptyOrFalse(box.read(Constant.tokenKey)))
                                ? Routes.homeScreen
                                : Routes.loginScreen);
                          }
                        }
                      }
                    });
                  })
            ],
          ),
        )),
        onWillPop: () {
          return Future.value(false);
        });
  }
}
