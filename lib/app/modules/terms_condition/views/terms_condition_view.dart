import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:small_officer/utilities/submit_button.dart';
import '../../../../constants/color_constant.dart';
import '../../../../constants/size_constant.dart';
import '../controllers/terms_condition_controller.dart';

class TermsConditionView extends GetView<TermsConditionController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Padding(
            padding: EdgeInsets.only(
                left: MySize.getScaledSizeWidth(15),
                top: MySize.size17!,
                bottom: MySize.size17!),
            child: Icon(
              Icons.arrow_back_ios,
              color: appTheme.primaryTheme,
            ),
          ),
        ),
        title: Container(
          // color: Colors.red,
          width: MySize.getScaledSizeWidth(192),
          height: MySize.getScaledSizeHeight(55),
          child: Image(
            image: AssetImage("assets/logo1.jpg"),
            fit: BoxFit.fill,
          ),
        ),
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        padding: EdgeInsets.symmetric(
          horizontal: MySize.getScaledSizeWidth(20),
        ),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Terms & Condition",
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: MySize.size22,
                      ),
                    ),
                    SizedBox(
                      height: MySize.size5,
                    ),
                    Container(
                      height: MySize.size1,
                      width: double.infinity,
                      color: appTheme.textGrayColor,
                    ),
                    SizedBox(
                      height: MySize.size10,
                    ),
                    Space.height(MySize.size12!),
                    titleText(title: "TERMS OF USE"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "1. For the avoidance of doubt, Licensor and GUESTS hereby acknowledge and agree that this License Agreement does not create any tenancy or sub-tenancy interest, leasehold estate, or other real property interest in GUESTS’ favor with respect to the Workspace, the Premises or the Building. Licensor hereby grants to GUESTS and GUESTS hereby accepts from the Licensor, a license to use the SMALL OFFICER flexible workspaces at ALL SMALL OFFICER LOCATIONS."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "2. GUESTS shall use the Workspaces solely for office purposes and for no other use whatsoever. Office use of a “retail,” or “medical” nature or business involving frequent visits by members of the public, is not permitted. Any other uses are prohibited without the Licensor’s prior written consent."),
                    Space.height(MySize.size12!),
                    bodyText(title: "3. Services."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "a. The WORKSPACE - is an enclosed professional office with a door and cloud based access system. Shared/common areas are for all GUESTS’ use, all other areas are considered private."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "b. Private Office - Desk, chair in an enclosed private office (with a door and cloud based access system). ONE UNASSIGNED PARKING SPACE PER OFFICE (WHERE AVAILABLE, FIRST COME FIRST SERVE)."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "c. Workstation (desk) - Desk and chair, located within the common area, reserved for personal use."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "d. Conference Room hours (where available): Table and chairs in an enclosed private space (with a door and cloud based access system.)"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "4. Term. Occupancy is on a rental basis. The term of occupancy is only for reserved hours and minutes, commences at start and ends at the completion of the reservation. Space is only available to guests and business activity related visitors."),
                    Space.height(MySize.size12!),
                    bodyText(title: "5. Fees."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "a. Fee Per hour (“term”) is payable in advance and on the day of each new term. Payment for any partial hour of the Term prorated based on the number of minutes in that calendar day."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "b. Security Deposit or terms that are worked out with a Guest that are exclusive to a space for a term that extends past a predetermined amount of days. Security deposits will be payable up front and returnable, after successful performance of this Agreement, from Licensor to Guest within seven (7) days."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "6. Cancellation Policy. Hourly reservations require a 24 hour notice for a credit refund. Exclusive space terms will require a termination notice that both the Workspace and Guest agree to upon Contract."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "7. Access. GUESTS shall have access to the Workspace during reserved hours. Additional access will be granted at Licensor’s discretion. GUESTS are responsible for maintaining the security of their own access and if GUESTS’ access are compromised or lost in any way, GUEST must notify Licensor and the Licensor, at their discretion, may charge a fee to reestablish GUESTS’ access."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "8. Rules and Regulations. GUESTS agrees to comply with the Rules and Regulations attached hereto and are part of this License Agreement, which may be amended from time to time by Licensor in its sole and absolute discretion."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "9. Liability. We will not be responsible for any loss or damage to your property within our space or within the building, or land where our space is located. This includes your property of any kind such as, by way of example only, computers, books, papers, wallets, handbags, and any other item you own or you bring onto the land (including our space) where our space is located."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "10. Notices. All notices under this License Agreement must be in writing. Any notice under this License Agreement may be given by personal delivery, electronic mail, U.S. mail (postage prepaid), or commercial delivery service (e.g. Federal Express or UPS), and will be deemed duly given when received by the party charged with such notice and addressed as follows:"),
                    Space.height(MySize.size12!),
                    bodyText(title: "As to Licensor:"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "SMALL OFFICER, LLC 197 Route 18 South, Suite 101N"),
                    Space.height(MySize.size12!),
                    bodyText(title: "East Brunswick, NJ 08816"),
                    Space.height(MySize.size12!),
                    bodyText(title: "Attn: Denise Reynier"),
                    Space.height(MySize.size12!),
                    bodyText(title: "Email: <EMAIL>"),
                    Space.height(MySize.size12!),
                    bodyText(title: "As to Guest: Company Name"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "11. Severability. If a court of competent jurisdiction shall declare any part of the License Agreement invalid or unenforceable, it shall not affect the validity of the balance of this Agreement."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "12. No Waiver. The failure by Licensor to exercise rights granted to Guest herein shall not constitute a waiver of any term, covenant or condition of this License Agreement, unless such waiver be in writing by Licensor."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "13. Entire Agreement. This License Agreement constitutes the entire agreement between the parties hereto with respect to the subject matter hereof and cancels and supersedes any prior understandings and agreements between the parties hereto with respect thereto. There are no representations, warranties, terms, conditions, undertakings or collateral agreements, express, implied or statutory, between the parties other than as expressly set forth in this License Agreement."),
                    Space.height(MySize.size24!),
                    titleText(title: "RULES AND REGULATIONS"),
                    Space.height(MySize.size12!),
                    bodyText(
                      title:
                          "By entering into our License Agreement, you (“GUEST”) acknowledge that Small Officer is a shared and open office workspace community (“Workspace”) for the benefit of all of its GUESTS. As such, we as the Licensor reserve the right at any time to remedy, to the extent of removal from and denial of access to the premises, for such violations related to but not all inclusive of:",
                    ),
                    Space.height(MySize.size12!),
                    bodyText(title: "1. Any illegal activity;"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "2. Offensive or disruptive behavior, noise, advertising, soliciting;"),
                    Space.height(MySize.size12!),
                    bodyText(title: "3. Theft of property, goods, services;"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "4. Delivery, possession and/or storage of excessive, voluminous, heavy, odorous, flammable, explosive materials of any kind;"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "5. Damage to, excessive waste of or misuse of the property, materials, supplies of the Workspace, its Guests or its guests."),
                    Space.height(MySize.size20!),
                    bodyText(
                        title:
                            "As the Licensor, it is our mission and responsibility to ensure that you as well as all of our Guests have the most productive, enjoyable and rewarding experience possible. We will do our best at all times to deliver this but as the Licensor we will not be liable for a Guest’s:"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "1. Loss of or damage to personal property, profits, wages, business;"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "2. Personal injury or harm caused by their actions or the actions of another;"),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "3. Internet security and any information the Guest or another may place on it."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "You agree to defend, indemnify, and hold harmless Small Officer, LLC, its’ guests, officers, employees, and agents from and against any and all losses, damages, claims, expenses, and liabilities of any kind (including for attorney’s fees and other legal costs), where caused by your breach of this Agreement or by or arising from your use of the Workspace or the building, parking lot or land where the Workspace is located."),
                    Space.height(MySize.size12!),
                    bodyText(
                        title:
                            "By accepting you agree to this License Agreement."),
                    Space.height(MySize.size12!),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: MySize.size18!),
              child: InkWell(
                onTap: () {
                  Get.back();
                },
                child: button(
                  title: "Accept ✔",
                  fontsize: MySize.size18!,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget titleText({String title = ""}) {
    return Text(
      title,
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: MySize.size18,
      ),
    );
  }

  Widget bodyText({String title = ""}) {
    return Text(
      title,
      style: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: MySize.size16,
      ),
    );
  }
}
