import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:small_officer/constants/size_constant.dart';
import '../../../../constants/color_constant.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/home_drawer_controller.dart';

class HomeDrawerView extends GetWidget<HomeDrawerController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeDrawerController>(
      init: HomeDrawerController(),
      builder: (controller) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Container(
              width: MySize.getScaledSizeWidth(350),
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
              height: MySize.screenHeight,
              decoration: BoxDecoration(
                color: Color(0xffFEFEFE),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(MySize.size16!),
                  bottomRight: Radius.circular(MySize.size16!),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: MySize.size10),
                  Padding(
                    padding:
                        EdgeInsets.only(right: MySize.getScaledSizeWidth(10)),
                    child: Row(
                      children: [
                        Expanded(child: Container()),
                        InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: SvgPicture.asset(
                            "assets/arrow_back.svg",
                            width: MySize.getScaledSizeWidth(30),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: MySize.size40),
                  Container(
                    child: Image(
                      image: AssetImage('assets/logo1.jpg'),
                      width: MySize.getScaledSizeWidth(200),
                    ),
                  ),
                  Text(
                    "Work From Here",
                    style: TextStyle(fontSize: MySize.size16),
                  ),
                  SizedBox(height: MySize.size30),
                  Container(
                    height: MySize.size1,
                    width: double.infinity,
                    color: appTheme.textGrayColor,
                  ),
                  SizedBox(height: MySize.size10),
                  Expanded(
                    child: ListView.builder(
                      itemCount: controller.drawerList.value.length,
                      itemBuilder: (BuildContext context, int index) {
                        return InkWell(
                          onTap: () {
                            controller.isTrue.toggle();
                            controller.drawerList.value.forEach((element) {
                              element.isActive = false;
                            });
                            controller.drawerList.value[index].isActive = true;
                            controller.homeScreenController.selectedDrawerIndex!
                                .value = index;
                            controller
                                    .homeScreenController.locationName!.value =
                                controller.drawerList.value[index].name
                                    .toString();
                            controller.homeScreenController.locationId.value =
                                controller.drawerList.value[index].locationId!
                                    .toInt();
                            controller.homeScreenController.bannerWiseLocation =
                                controller
                                    .drawerList.value[index].announceMentList!;
                            GetStorage box = GetStorage();
                            box.write(
                                "locationId",
                                controller.drawerList.value[index].locationId!
                                    .toInt());
                            controller.allResourceController
                                .callApiForGetResourceData(
                              context: context,
                              LocationId: controller
                                  .drawerList.value[index].locationId!
                                  .toInt(),
                            );
                            controller.drawerList.refresh();
                            controller.homeScreenController.tabController!.value
                                .index = 0;
                            controller.update();
                            Get.back();
                          },
                          child: Container(
                            height: MySize.size85,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: MySize.getScaledSizeWidth(15),
                                  height: MySize.size85,
                                  decoration: BoxDecoration(
                                    color: (controller.drawerList.value[index]
                                                .isActive ==
                                            true)
                                        ? appTheme.textGrayColor
                                        : Colors.white,
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(MySize.size9!),
                                      bottomRight:
                                          Radius.circular(MySize.size9!),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: Container(
                                    alignment: Alignment.center,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: MySize.getScaledSizeWidth(15),
                                        ),
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: (!isNullEmptyOrFalse(controller
                                                  .drawerList.value[index].img))
                                              ? CommonNetworkImageView(
                                                  url: controller.drawerList
                                                      .value[index].img!,
                                                  height: MySize.size60!,
                                                  width:
                                                      MySize.getScaledSizeWidth(
                                                          70),
                                                  fit: BoxFit.fill,
                                                )
                                              : CommonNetworkImageView(
                                                  url:
                                                      "https://images.unsplash.com/photo-1548345680-f5475ea5df84?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1473&q=80",
                                                  height: MySize.size60!,
                                                  width:
                                                      MySize.getScaledSizeWidth(
                                                          70),
                                                  fit: BoxFit.fill,
                                                ),
                                        ),
                                        SizedBox(
                                          width: MySize.getScaledSizeWidth(12),
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              controller
                                                  .drawerList.value[index].name
                                                  .toString(),
                                              style: TextStyle(
                                                fontSize: MySize.size16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            SizedBox(
                                              height: MySize.size2,
                                            ),
                                            Text(
                                              controller.drawerList.value[index]
                                                  .subTitle
                                                  .toString(),
                                              style: TextStyle(
                                                fontSize: MySize.size15,
                                                color: appTheme.textGrayColor,
                                                fontWeight: FontWeight.normal,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                (controller.drawerList.value[index].isActive ==
                                        true)
                                    ? Icon(Icons.done)
                                    : SizedBox(),
                                SizedBox(
                                  width: MySize.getScaledSizeWidth(10),
                                ),
                                Container(
                                  width: MySize.getScaledSizeWidth(15),
                                  height: MySize.size85,
                                  decoration: BoxDecoration(
                                    color: (controller.drawerList.value[index]
                                                .isActive ==
                                            true)
                                        ? appTheme.textGrayColor
                                        : Colors.white,
                                    borderRadius: BorderRadius.only(
                                      bottomLeft:
                                          Radius.circular(MySize.size9!),
                                      topLeft: Radius.circular(MySize.size9!),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
