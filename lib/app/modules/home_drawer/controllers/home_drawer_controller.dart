import 'package:get/get.dart';
import 'package:small_officer/Models/banner_data_model.dart';
import 'package:small_officer/app/modules/home_screen/controllers/home_screen_controller.dart';

class HomeDrawerController extends GetxController {
  //TODO: Implement HomeDrawerController
  RxList<DrawerModel> drawerList = RxList();
  HomeScreenController homeScreenController = Get.find<HomeScreenController>();
  AllResourceController allResourceController =
      Get.put(AllResourceController());
  RxBool isTrue = false.obs;
  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
    if (homeScreenController.locationDataModel != null) {
      if (homeScreenController.locationDataModel!.data!.isNotEmpty) {
        homeScreenController.locationDataModel!.data!.forEach((element) {
          drawerList.add(DrawerModel(
            img: element.imageUrl,
            isActive: false,
            locationId: element.id,
            name: element.name,
            announceMentList: element.announcements,
            subTitle:
                element.state.toString() + " ," + element.country.toString(),
          ));
        });
        if (homeScreenController.selectedDrawerIndex != null) {
          for (int i = 0; i < drawerList.length; i++) {
            if (i == homeScreenController.selectedDrawerIndex!.value) {
              drawerList[i].isActive = true;
            }
          }
        } else {
          homeScreenController.selectedDrawerIndex = 0.obs;
          drawerList.first.isActive = true;
        }
      }
    }
    // drawerList.add(DrawerModel(
    //     img: "assets/table.png",
    //     isActive: true,
    //     name: "BloomField",
    //     subTitle: "BloomField,united States"));
    // drawerList.add(DrawerModel(
    //     img: "assets/table.png",
    //     isActive: false,
    //     name: "East Brunswick",
    //     subTitle: "East Brunswick,united States"));
    // drawerList.add(DrawerModel(
    //     img: "assets/table.png",
    //     isActive: false,
    //     name: "Newark",
    //     subTitle: "Newark,united States"));
    // drawerList.add(DrawerModel(
    //     img: "assets/table.png",
    //     isActive: false,
    //     name: "Union City",
    //     subTitle: "Union City,united States"));
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}
  void increment() => count.value++;
}

class DrawerModel {
  String? img;
  String? name;
  String? subTitle;
  bool? isActive;
  int? locationId;
  List<Bannerdata>? announceMentList;

  DrawerModel(
      {this.img,
      this.name,
      this.subTitle,
      this.isActive,
      this.locationId,
      this.announceMentList});
}
