import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';

class ReportIssueController extends GetxController {
  Rx<TextEditingController> titleController = TextEditingController().obs;
  Rx<TextEditingController> descController = TextEditingController().obs;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final count = 0.obs;
  /*@override
  void onInit() {
    super.onInit();
  }*/

  /*@override
  void onReady() {
    super.onReady();
  }*/

  callApiForReportIssue({required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    Map<String, dynamic> dict = {};
    dict["title"] = titleController.value.text;
    dict["description"] = descController.value.text;

    app.resolve<CustomDialogs>().showCircularDialog(context);

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.appreport,
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        Get.back();
        app.resolve<CustomDialogs>().getDialog(
              title: "Success",
              desc: "Report successfully created.",
            );

        //AuthModel authModel = AuthModel.fromJson(response);
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app.resolve<CustomDialogs>().getDialog(title: "Failed", desc: message);

        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
