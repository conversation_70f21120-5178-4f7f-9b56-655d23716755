import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:small_officer/utilities/submit_button.dart';
import 'package:small_officer/utilities/text_field.dart';

import '../../../../constants/size_constant.dart';
import '../../../routes/app_pages.dart';
import '../controllers/report_issue_controller.dart';

class ReportIssueView extends GetWidget<ReportIssueController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Padding(
            padding: EdgeInsets.only(
                left: MySize.getScaledSizeWidth(15),
                top: MySize.size17!,
                bottom: MySize.size17!),
            child: SvgPicture.asset(
              "assets/arrow_back.svg",
            ),
          ),
        ),
        title: Container(
          // color: Colors.red,
          width: MySize.getScaledSizeWidth(192),
          height: MySize.getScaledSizeHeight(55),
          child: Image(
            image: AssetImage("assets/logo1.jpg"),
            fit: BoxFit.fill,
          ),
        ),
        actions: [
          InkWell(
            onTap: () {
              Get.toNamed(Routes.notification);
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  right: MySize.getScaledSizeWidth(15),
                  top: MySize.size15!,
                  bottom: MySize.size15!),
              child: Image(
                image: AssetImage("assets/notification.png"),
              ),
              // child: SvgPicture.asset(
              //   "assets/notification.svg",
              //   height: MySize.size26,
              //   width: MySize.getScaledSizeWidth(37),
              // ),
            ),
          ),
        ],
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        padding: EdgeInsets.symmetric(
            horizontal: MySize.getScaledSizeWidth(15),
            vertical: MySize.size20!),
        child: Form(
          key: controller.formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Reports & Issues",
                style: TextStyle(
                  fontSize: MySize.size20,
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(
                height: MySize.size15,
              ),
              getTextFormField(
                  hintText: "Title",
                  isFillColor: true,
                  textEditingController: controller.titleController.value,
                  validation: (val) {
                    if (val != null) {
                      return null;
                    } else {
                      return "Please enter title";
                    }
                  },
                  prefixIcon: Padding(
                    padding: EdgeInsets.only(
                        left: MySize.getScaledSizeWidth(7),
                        top: MySize.size17!,
                        bottom: MySize.size17!),
                    child: SvgPicture.asset(
                      "assets/pin.svg",
                      height: MySize.size20,
                    ),
                  )),
              SizedBox(
                height: MySize.size15,
              ),
              getTextFormField(
                hintText: "Description",
                maxLine: 11,
                textEditingController: controller.descController.value,
                isFillColor: true,
                validation: (val) {
                  if (val != null) {
                    return null;
                  } else {
                    return "Please enter description";
                  }
                },
              ),
              Expanded(
                child: Container(),
              ),
              Center(
                child: InkWell(
                  onTap: () {
                    if (controller.formKey.currentState!.validate()) {
                      controller.callApiForReportIssue(context: context);
                    }
                  },
                  child: button(
                    title: "Send",
                    width: MySize.getScaledSizeWidth(400),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
