import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:small_officer/constants/color_constant.dart';

import '../../../../constants/app_images.dart';
import '../../../../constants/size_constant.dart';
import '../controllers/app_under_maintenance_controller.dart';

class AppUnderMaintenanceView extends GetView<AppUnderMaintenanceController> {
  const AppUnderMaintenanceView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
        body: Container(
      width: MySize.safeWidth,
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            AppImages.appLogo,
            height: 94,
            // width: 89,
          ),
          Space.height(170),
          Stack(
            clipBehavior: Clip.none,
            children: [
              Container(
                // height: 100,
                width: 280,
                height: 280,
                padding: EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                    // border: Border.all(),
                    borderRadius: BorderRadius.all(Radius.circular(20)),
                    gradient: LinearGradient(
                      colors: [BaseTheme().newPrimaryColor, BaseTheme().newPrimaryColor],
                    )),
                child: Column(
                  children: [
                    Space.height(90),
                    Text(
                      "Hello !",
                      style: TextStyle(
                        fontSize: 30,
                        fontWeight: FontWeight.w600,
                        color: BaseTheme().GrayColor,
                      ),
                    ),
                    Space.height(5),
                    Text(
                      "We are Under Maintenance.",
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.w600,
                        color: BaseTheme().GrayColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Space.height(10),
                    Text(
                      "Will be back soon !",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: BaseTheme().GrayColor,
                      ),
                    ),
                    Space.height(20),
                  ],
                ),
              ),
              Positioned(
                  top: -110,
                  left: 80,
                  child: Image.asset(
                    AppImages.rocket,
                    height: 180,
                  ))
            ],
          ),
        ],
      ),
    ));
  }
}
