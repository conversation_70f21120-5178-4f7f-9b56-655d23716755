import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OtpScreenController extends GetxController {
  final TextEditingController pinPutController = TextEditingController();
  final FocusNode pinPutFocusNode = FocusNode();
  BoxDecoration get pinPutDecoration {
    return BoxDecoration(
      border: Border.all(color: Colors.grey),
      borderRadius: BorderRadius.circular(15.0),
    );
  }

  /*@override
  void onInit() {
    super.onInit();
  }*/

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}
}
