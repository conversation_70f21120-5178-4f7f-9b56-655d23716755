import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:pinput/pin_put/pin_put.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:small_officer/utilities/submit_button.dart';

import '../../../../constants/color_constant.dart';
import '../controllers/otp_screen_controller.dart';

class OtpScreenView extends GetWidget<OtpScreenController> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Container(
            padding:
                EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(20)),
            child: <PERSON><PERSON><PERSON>(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: MySize.getScaledSizeHeight(15)),
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Icon(Icons.arrow_back),
                    ),
                    Spacer(),
                    Text("OTP",
                        style: TextStyle(
                            fontSize: MySize.getScaledSizeHeight(23),
                            fontWeight: FontWeight.w500)),
                    Spacer(),
                  ],
                ),
                SizedBox(height: MySize.getScaledSizeHeight(120)),
                SvgPicture.asset(
                  "assets/otp_back_logo.svg",
                  height: MySize.getScaledSizeHeight(188),
                  width: MySize.getScaledSizeWidth(188),
                ),
                SizedBox(height: MySize.getScaledSizeHeight(20)),
                Text(
                  "OTP Sent on",
                  style: TextStyle(color: appTheme.borderColor),
                ),
                SizedBox(height: MySize.getScaledSizeHeight(5)),
                Text(
                  "<EMAIL>",
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: MySize.getScaledSizeHeight(20)),
                ),
                SizedBox(height: MySize.getScaledSizeHeight(105)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                      child: Container(
                        width: MySize.getScaledSizeWidth(340),
                        alignment: Alignment.center,
                        child: PinPut(
                          fieldsCount: 6,
                          eachFieldWidth: MySize.getScaledSizeWidth(40),
                          // eachFieldMargin:
                          //     EdgeInsets.only(right: MySize.getScaledSizeWidth(13)),
                          onSubmit: (String pin) => {},
                          focusNode: controller.pinPutFocusNode,
                          controller: controller.pinPutController,
                          submittedFieldDecoration:
                              controller.pinPutDecoration.copyWith(
                            borderRadius: BorderRadius.circular(20.0),
                          ),
                          selectedFieldDecoration: controller.pinPutDecoration,
                          followingFieldDecoration:
                              controller.pinPutDecoration.copyWith(
                            borderRadius: BorderRadius.circular(5.0),
                            border: Border.all(
                              color: Colors.grey.withOpacity(.5),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(
                        Icons.cached,
                        color: appTheme.secondaryColor,
                      ),
                      SizedBox(width: MySize.getScaledSizeWidth(5)),
                      Text(
                        "Resend",
                        style: TextStyle(color: appTheme.secondaryColor),
                      ),
                      SizedBox(width: MySize.getScaledSizeWidth(20)),
                    ],
                  ),
                ),
                SizedBox(height: MySize.getScaledSizeHeight(150)),
                InkWell(
                    onTap: () {
                      Get.toNamed(Routes.privacyScreen);
                    },
                    child: button(
                        title: "Verify ✔",
                        backgroundColor: BaseTheme().newPrimaryColor,
                        width: MySize.getScaledSizeWidth(480))),
                SizedBox(height: MySize.getScaledSizeHeight(50)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
