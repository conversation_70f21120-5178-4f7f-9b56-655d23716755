import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

import '../../../../constants/constant.dart';
import '../../../../constants/size_constant.dart';
import '../../../../utilities/submit_button.dart';
import '../../../../utilities/utilities.dart';
import '../../../routes/app_pages.dart';
import '../controllers/past_booking_controller.dart';

class PastBookingView extends GetWidget<PastBookingController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<PastBookingController>(
      init: PastBookingController(),
      builder: (c) {
        return Obx(() {
          return Scaffold(
            body: Padding(
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
              child: Container(
                padding: EdgeInsets.symmetric(
                    horizontal: MySize.getScaledSizeWidth(15),
                    vertical: MySize.getScaledSizeHeight(10)),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Booking History",
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: MySize.size22,
                          ),
                        ),
                      ],
                    ),
                    Expanded(
                      child: (!c.hasData.value)
                          ? Center(
                              child: getShimerForBookingList(),
                            )
                          : ((c.bookingDataModel != null &&
                                  c.bookingDataModel!.data != null &&
                                  c.bookingDataModel!.data!.isNotEmpty)
                              ? Container(
                                  padding: EdgeInsets.only(top: MySize.size15!),
                                  child: ListView.separated(
                                    itemBuilder: (context, i) {
                                      return InkWell(
                                        onTap: () {
                                          // Get.toNamed(Routes.PRODUCT_DETAIL,
                                          //     arguments: {"isBooked": true});
                                        },
                                        child: Column(
                                          children: [
                                            Container(
                                              decoration: BoxDecoration(
                                                  border: Border.all(
                                                      color:
                                                          appTheme.borderColor),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          MySize.size10!)),
                                              padding: EdgeInsets.symmetric(
                                                horizontal:
                                                    MySize.getScaledSizeWidth(
                                                        10),
                                                vertical: MySize.size10!,
                                              ),
                                              child: Row(
                                                children: [
                                                  ClipRRect(
                                                    // child: Image(
                                                    //   image: AssetImage(
                                                    //       "assets/table.png"),
                                                    //   fit: BoxFit.cover,
                                                    //   height: MySize.size140,
                                                    //   width: MySize.size140,
                                                    // ),
                                                    child:
                                                        CommonNetworkImageView(
                                                      url: (c
                                                                  .bookingDataModel!
                                                                  .data![i]
                                                                  .resource!
                                                                  .imageUrl !=
                                                              null)
                                                          ? c
                                                              .bookingDataModel!
                                                              .data![i]
                                                              .resource!
                                                              .imageUrl
                                                              .toString()
                                                          : "",
                                                      height: MySize.size140!,
                                                      width: MySize.size140!,
                                                      fit: BoxFit.cover,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                  ),
                                                  SizedBox(
                                                    width: MySize
                                                        .getScaledSizeWidth(7),
                                                  ),
                                                  Expanded(
                                                    child: Container(
                                                      // height: MySize.size140,
                                                      width: MySize.size140,
                                                      child: Column(
                                                        children: [
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Text(
                                                                "#${c
                                                                        .bookingDataModel!
                                                                        .data![
                                                                            i]
                                                                        .id}",
                                                                style:
                                                                    TextStyle(
                                                                  color: appTheme
                                                                      .textGrayColor,
                                                                  fontSize: MySize
                                                                      .size16,
                                                                ),
                                                              ),
                                                              Text(
                                                                "\$${c.bookingDataModel!.data![i].resource!.ratePerHour} /Hour",
                                                                style:
                                                                    TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  fontSize: MySize
                                                                      .size16,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                          Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                c
                                                                    .bookingDataModel!
                                                                    .data![i]
                                                                    .resource!
                                                                    .name
                                                                    .toString(),
                                                                style:
                                                                    TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  fontSize: MySize
                                                                      .size16,
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                height: MySize
                                                                    .size10,
                                                              ),
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  SvgPicture
                                                                      .asset(
                                                                    "assets/location.svg",
                                                                    height: MySize
                                                                        .size14,
                                                                    color: appTheme
                                                                        .textGrayColor,
                                                                  ),
                                                                  Expanded(
                                                                    child: Text(
                                                                      " ${c.bookingDataModel!.data![i].resource!.address.toString()}",
                                                                      style: TextStyle(
                                                                          fontWeight: FontWeight
                                                                              .normal,
                                                                          fontSize: MySize
                                                                              .size16,
                                                                          color:
                                                                              appTheme.textGrayColor),
                                                                      maxLines:
                                                                          4,
                                                                    ),
                                                                  ),
                                                                ],
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                              ),
                                                              SizedBox(
                                                                height: MySize
                                                                    .size8,
                                                              ),
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .spaceBetween,
                                                                children: [
                                                                  Row(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      SvgPicture
                                                                          .asset(
                                                                        "assets/date.svg",
                                                                        height:
                                                                            MySize.size14,
                                                                      ),
                                                                      (c.bookingDataModel!.data![i].startAt !=
                                                                              null)
                                                                          ? Text(
                                                                              " ${DateFormat("MM.dd.yyyy").format(getDateFromStringFromUtc(
                                                                                    c.bookingDataModel!.data![i].startAt.toString(),
                                                                                  ).toLocal())} | ${DateFormat("hh:mm a").format(getDateFromStringFromUtc(
                                                                                    c.bookingDataModel!.data![i].startAt.toString(),
                                                                                  ).toLocal())}",
                                                                              style: TextStyle(
                                                                                fontSize: MySize.size16,
                                                                              ),
                                                                            )
                                                                          : SizedBox(),
                                                                    ],
                                                                  ),
                                                                  Row(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      SvgPicture
                                                                          .asset(
                                                                        "assets/time.svg",
                                                                        height:
                                                                            MySize.size14,
                                                                      ),
                                                                      Text(
                                                                        " ${getDateFromString(
                                                                              c.bookingDataModel!.data![i].endAt.toString(),
                                                                            )
                                                                                .difference(getDateFromString(
                                                                                  c.bookingDataModel!.data![i].startAt.toString(),
                                                                                ))
                                                                                .inHours} H",
                                                                        style: TextStyle(
                                                                            fontWeight:
                                                                                FontWeight.normal,
                                                                            fontSize: MySize.size16,
                                                                            color: appTheme.textGrayColor),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ],
                                                              ),
                                                              SizedBox(
                                                                height: MySize
                                                                    .size8,
                                                              ),
                                                              Row(
                                                                children: [
                                                                  // button(
                                                                  //     title:
                                                                  //     "Cancel",
                                                                  //     backgroundColor:
                                                                  //     Colors
                                                                  //         .white,
                                                                  //     textColor:
                                                                  //     appTheme
                                                                  //         .primaryTheme,
                                                                  //     borderColor:
                                                                  //     appTheme
                                                                  //         .primaryTheme,
                                                                  //     width: 90,
                                                                  //     height: 35),
                                                                  InkWell(
                                                                    child: button(
                                                                        title:
                                                                            "View",
                                                                        width:
                                                                            130,
                                                                        height:
                                                                            35),
                                                                    onTap: () {
                                                                      Get.toNamed(
                                                                          Routes
                                                                              .productDetail,
                                                                          arguments: {
                                                                            "isBooked":
                                                                                true,
                                                                            StringConstant.productData:
                                                                                c.bookingDataModel!.data![i].resource,
                                                                            StringConstant.bookingModel:
                                                                                c.bookingDataModel!.data![i],
                                                                          });
                                                                    },
                                                                  ),
                                                                ],
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .center,
                                                              )
                                                            ],
                                                          )
                                                        ],
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                            SizedBox(
                                              height: MySize.size5,
                                            ),
                                            Container(
                                              width: double.infinity,
                                              child: SfRangeSlider(
                                                activeColor: Colors.black,
                                                inactiveColor: Colors.grey,
                                                labelPlacement:
                                                    LabelPlacement.onTicks,
                                                enableTooltip: true,
                                                min: getDateFromStringFromUtc(
                                                  c.bookingDataModel!.data![i]
                                                      .resource!.openAt
                                                      .toString(),
                                                ).toLocal(),
                                                max: getDateFromStringFromUtc(
                                                  c.bookingDataModel!.data![i]
                                                      .resource!.closeAt
                                                      .toString(),
                                                ).toLocal(),
                                                values: SfRangeValues(
                                                    DateTime(
                                                        2000,
                                                        01,
                                                        01,
                                                        getDateFromStringFromUtc(
                                                          c.bookingDataModel!
                                                              .data![i].startAt
                                                              .toString(),
                                                        ).toLocal().hour,
                                                        getDateFromStringFromUtc(
                                                          c.bookingDataModel!
                                                              .data![i].startAt
                                                              .toString(),
                                                        ).toLocal().minute),
                                                    DateTime(
                                                        2000,
                                                        01,
                                                        01,
                                                        getDateFromStringFromUtc(
                                                          c.bookingDataModel!
                                                              .data![i].endAt
                                                              .toString(),
                                                        ).toLocal().hour,
                                                        getDateFromStringFromUtc(
                                                          c.bookingDataModel!
                                                              .data![i].endAt
                                                              .toString(),
                                                        ).toLocal().minute)),
                                                interval: 4,
                                                showTicks: true,
                                                showLabels: true,
                                                shouldAlwaysShowTooltip: false,
                                                dateFormat:
                                                    DateFormat('hh:mm\n  a'),
                                                dateIntervalType:
                                                    DateIntervalType.hours,
                                                onChanged: (SfRangeValues
                                                    newValues) {},
                                              ),
                                            )

                                            // Container(
                                            //   height: MySize.size15,
                                            //   width: double.infinity,
                                            //   decoration: BoxDecoration(
                                            //     borderRadius:
                                            //         BorderRadius.circular(10),
                                            //     color: appTheme.textGrayColor
                                            //         .withOpacity(0.5),
                                            //   ),
                                            //   child: Row(
                                            //     children: [
                                            //       SizedBox(
                                            //         width: MySize.size90,
                                            //       ),
                                            //       Container(
                                            //         height: MySize.size15,
                                            //         width:
                                            //             MySize.getScaledSizeWidth(
                                            //                 70),
                                            //         decoration: BoxDecoration(
                                            //           borderRadius:
                                            //               BorderRadius.circular(
                                            //                   6),
                                            //           color: Colors.black,
                                            //         ),
                                            //       ),
                                            //     ],
                                            //   ),
                                            // ),
                                            // SizedBox(
                                            //   height: MySize.size5,
                                            // ),
                                            // Row(
                                            //   children: [
                                            //     Text(
                                            //       "07:00am",
                                            //       style: TextStyle(
                                            //           fontWeight:
                                            //               FontWeight.normal,
                                            //           fontSize: MySize.size10,
                                            //           color:
                                            //               appTheme.textGrayColor),
                                            //     ),
                                            //     SizedBox(
                                            //       width:
                                            //           MySize.getScaledSizeWidth(
                                            //               30),
                                            //     ),
                                            //     Text(
                                            //       "09:00am",
                                            //       style: TextStyle(
                                            //           fontWeight:
                                            //               FontWeight.normal,
                                            //           fontSize: MySize.size10,
                                            //           color:
                                            //               appTheme.textGrayColor),
                                            //     ),
                                            //     SizedBox(
                                            //       width:
                                            //           MySize.getScaledSizeWidth(
                                            //               30),
                                            //     ),
                                            //     Text(
                                            //       "12:00am",
                                            //       style: TextStyle(
                                            //           fontWeight:
                                            //               FontWeight.normal,
                                            //           fontSize: MySize.size10,
                                            //           color:
                                            //               appTheme.textGrayColor),
                                            //     ),
                                            //     Expanded(
                                            //       child: Container(),
                                            //     ),
                                            //     Text(
                                            //       "09:00pm",
                                            //       style: TextStyle(
                                            //           fontWeight:
                                            //               FontWeight.normal,
                                            //           fontSize: MySize.size10,
                                            //           color:
                                            //               appTheme.textGrayColor),
                                            //     ),
                                            //   ],
                                            // )
                                          ],
                                        ),
                                      );
                                    },
                                    separatorBuilder: (context, i) {
                                      return SizedBox(
                                        height: MySize.size35,
                                      );
                                    },
                                    itemCount: c.bookingDataModel!.data!.length,
                                  ),
                                )
                              : Center(
                                  child: Text(
                                    "No booking data found",
                                    style: TextStyle(fontSize: MySize.size14!),
                                  ),
                                )),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }
}
