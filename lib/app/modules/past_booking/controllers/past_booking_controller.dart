import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import '../../../../Models/booking_data_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';

class PastBookingController extends GetxController {
  //TODO: Implement PastBookingController

  RxBool hasData = false.obs;
  BookingDataModel? bookingDataModel;
  SfRangeValues values = SfRangeValues(
      DateTime(2000, 01, 01, 00, 00), DateTime(2000, 01, 01, 01, 00));

  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      callApiForGetBookingList(context: Get.context!);
    });
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  callApiForGetBookingList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //  app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.bookingList}?past=true",
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        bookingDataModel = BookingDataModel.fromJson(response);
        print(bookingDataModel?.data?.length.toString());
      },
      failureCallback: (status, message) {
        hasData.value = true;

        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
