import 'dart:async';
import 'dart:typed_data';

import 'package:carousel_slider/carousel_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:small_officer/Models/resourceDataModel.dart';
import 'package:small_officer/constants/constant.dart';

import '../../../../Models/booking_data_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';

class ProductDetailController extends GetxController {
  CarouselController carouselController = CarouselController();
  RxInt sliderIndex = 0.obs;
  RxBool isBooked = false.obs;
  RxInt value = 0.obs;
  RxBool positive = false.obs;
  RxString name = "Small Office".obs;
  RxString password = "299Glenwood".obs;
  BookingData? bookingDataModel;
  RowsData? productDetailModel;
  ResourceDataModel? resourceDataModel;
  Uint8List? imageBytesMap;
  RxBool hasImageData = false.obs;
  Completer<GoogleMapController> controller = Completer();
  LatLng? center;
  Completer<GoogleMapController> controllerGoogle = Completer();
  LatLng? centerNew;

  void onMapCreated(GoogleMapController controller) {
    controllerGoogle.complete(controller);

    markers.add(
      Marker(
        markerId: MarkerId('place_name'),
        position: centerNew!,
        icon: BitmapDescriptor.defaultMarker,
      ),
    );
  }

  List sliders = [
    "https://cdn.pixabay.com/photo/2022/01/16/23/12/desert-6943430_1280.jpg",
    "https://cdn.pixabay.com/photo/2021/11/17/16/59/mountain-6804152_1280.jpg",
    "https://cdn.pixabay.com/photo/2022/01/16/23/12/desert-6943430_1280.jpg",
    "https://cdn.pixabay.com/photo/2021/11/17/16/59/mountain-6804152_1280.jpg",
    "https://cdn.pixabay.com/photo/2022/03/25/19/24/waterfall-7091641_1280.jpg"
  ];

  RxBool hasData = false.obs;
  BitmapDescriptor? pinLocationIcon;
  Set<Marker> markers = {};

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      print(Get.arguments);

      isBooked.value = Get.arguments["isBooked"];
      if (Get.arguments[StringConstant.productData] != null) {
        productDetailModel = Get.arguments[StringConstant.productData];
        callApiForGetProductData(Get.context!);
        callApiForGetResourceData(context: Get.context!);

        if (productDetailModel!.location!.latitude != null &&
            productDetailModel!.location!.longitude != null) {
          centerNew = LatLng(
              double.parse(productDetailModel!.location!.latitude!),
              double.parse(productDetailModel!.location!.longitude!));
          center = LatLng(double.parse(productDetailModel!.location!.latitude!),
              double.parse(productDetailModel!.location!.longitude!));
        } else {
          centerNew = LatLng(40.7128, -73.935242);
        }
        CameraPosition(target: centerNew!);
      }
      if (Get.arguments[StringConstant.bookingModel] != null) {
        bookingDataModel = Get.arguments[StringConstant.bookingModel];
      }

      markers.add(
        Marker(
          markerId: MarkerId(center.toString()),
          position: center!,
          icon: BitmapDescriptor.defaultMarker,
        ),
      );
    }
  }

  void setCustomMapPin() async {
    pinLocationIcon = await BitmapDescriptor.fromAssetImage(
        ImageConfiguration(devicePixelRatio: 2.5), 'assets/location_light.svg');
  }

  callApiForGetResourceData({required BuildContext context}) {
    hasData.value = false;
    FocusScope.of(context).unfocus();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.sourceAllDataApi}?ResourceTypeId=${productDetailModel!.resourceTypeId}",
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      successCallback: (response, message) {
        hasData.value = true;
        resourceDataModel = ResourceDataModel.fromJson(response);
      },
      failureCallback: (status, message) {
        hasData.value = true;
        print(" error");
      },
    );
  }

  callApiForGetProductData(BuildContext context) {
    FocusScope.of(context).unfocus();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.sourceAllDataApi}/${productDetailModel!.id}",
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      successCallback: (response, message) {
        productDetailModel = RowsData.fromJson(response["data"]);
      },
      failureCallback: (status, message) {
        print(" error");
      },
    );
  }
}
