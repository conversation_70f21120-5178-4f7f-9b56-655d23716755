import 'dart:convert';
import 'dart:developer';
import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:system_settings/system_settings.dart';
import '../../../../constants/constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import 'package:get/get.dart';
import 'package:small_officer/constants/color_constant.dart';
import '../../../../constants/size_constant.dart';
import '../../../../utilities/submit_button.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/product_detail_controller.dart';

class ProductDetailView extends GetWidget<ProductDetailController> {
  ScrollPhysics scrollPhysics = AlwaysScrollableScrollPhysics();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back_ios,
              color: appTheme.primaryTheme,
            ),
            // child: Padding(
            //   padding: EdgeInsets.only(
            //       left: MySize.getScaledSizeWidth(15),
            //       top: MySize.size17!,
            //       bottom: MySize.size17!),
            //   child: SvgPicture.asset(
            //     "assets/arrow_back.svg",
            //     width: MySize.getScaledSizeWidth(40),
            //   ),
            // ),
          ),
          title: Container(
            // color: Colors.red,
            width: MySize.getScaledSizeWidth(192),
            height: MySize.getScaledSizeHeight(55),
            child: Image(
              image: AssetImage("assets/logo1.jpg"),
              fit: BoxFit.fill,
            ),
          ),
          centerTitle: true,
          elevation: 0,
        ),
        body: Container(
          child: Stack(
            children: [
              Container(
                height: MySize.screenHeight,
                child: ListView(
                  physics: scrollPhysics,
                  children: [
                    SizedBox(
                      height: MySize.size20,
                    ),
                    // SizedBox(
                    //   height: 20,
                    //   child: Marquee(
                    //     text:
                    //         'This screen will show your reservations and allow you to unlock doors 15 minutes prior. For last minute bookings, please message us and we will open the door manually.',
                    //     style: TextStyle(
                    //       fontWeight: FontWeight.w500,
                    //       color: Colors.red,
                    //     ),
                    //     scrollAxis: Axis.horizontal,
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     blankSpace: 20.0,
                    //     velocity: 50.0,
                    //     pauseAfterRound: Duration(seconds: 1),
                    //     startPadding: 10.0,
                    //     accelerationDuration: Duration(seconds: 1),
                    //     accelerationCurve: Curves.linear,
                    //     decelerationDuration: Duration(milliseconds: 500),
                    //     decelerationCurve: Curves.easeOut,
                    //   ),
                    // ),
                    // SizedBox(
                    //   height: MySize.size10,
                    // ),
                    (controller.productDetailModel!.otherImageUrls != null)
                        ? Column(
                            children: [
                              Container(
                                height: MySize.getScaledSizeHeight(200),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    CarouselSlider(
                                      carouselController:
                                          controller.carouselController,
                                      options: CarouselOptions(
                                        viewportFraction: 0.9,
                                        //viewportFraction: MySize.getScaledSizeWidth(0.8),
                                        height: MySize.getScaledSizeHeight(200),
                                        autoPlay: true,
                                        onPageChanged: (index, reason) {
                                          controller.sliderIndex.value = index;
                                        },
                                      ),
                                      items: controller
                                          .productDetailModel!.otherImageUrls!
                                          .map((data) {
                                        return Builder(
                                          builder: (BuildContext context) {
                                            return ClipRRect(
                                              child: CommonNetworkImageView(
                                                url: data.toString(),
                                                // height:
                                                //     MySize.getScaledSizeHeight(150),
                                                width:
                                                    MySize.getScaledSizeWidth(
                                                        380),
                                                fit: BoxFit.cover,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      MySize.size15!),
                                              // child: Image(
                                              //   image: AssetImage(
                                              //     "assets/table.png",
                                              //   ),
                                              //   fit: BoxFit.cover,
                                              //   width: MySize.getScaledSizeWidth(380),
                                              // ),
                                            );
                                          },
                                        );
                                      }).toList(),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: MySize.size10,
                              ),
                              if (controller
                                          .productDetailModel!.otherImageUrls !=
                                      null &&
                                  controller.productDetailModel!.otherImageUrls!
                                      .isNotEmpty)
                                Container(
                                  // decoration: BoxDecoration(
                                  //     color: Colors.black.withOpacity(0.1),
                                  //     borderRadius: BorderRadius.circular(
                                  //         MySize.getScaledSizeHeight(15))),
                                  child: DotsIndicator(
                                    dotsCount: controller.productDetailModel!
                                        .otherImageUrls!.length,
                                    decorator: DotsDecorator(
                                      activeColor: appTheme.primaryTheme,
                                      color: Colors.black.withOpacity(0.1),
                                      activeSize: const Size(32.0, 9.0),
                                      activeShape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(5.0)),
                                    ),
                                    position: controller.sliderIndex.value,
                                  ),
                                ),
                            ],
                          )
                        : SizedBox(),
                    SizedBox(
                      height: MySize.size30,
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(20)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                controller.productDetailModel!.name.toString(),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: MySize.size20,
                                ),
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  // if (controller.bookingDataModel != null &&
                                  //     controller.bookingDataModel!.plan!)
                                  //   button(
                                  //     width: 100,
                                  //     title: "Plan",
                                  //     textColor: Colors.black,
                                  //     height: 35,
                                  //     fontsize: 22,
                                  //     borderColor: Colors.transparent,
                                  //     backgroundColor: Colors.white,
                                  //   ),
                                  // SizedBox(
                                  //   width: 10,
                                  // ),
                                  // button(
                                  //         width: 100,
                                  //         title: "Booked",
                                  //         textColor: Colors.green,
                                  //         height: 35, fontsize: 22,
                                  //         borderColor: Colors.transparent,
                                  //         backgroundColor: Colors.white,
                                  //       ),
                                  (controller.isBooked.value)
                                      ? button(
                                          width: 100,
                                          title: "Booked",
                                          textColor: Colors.green,
                                          height: 35,
                                          fontsize: 22,
                                          borderColor: Colors.transparent,
                                          backgroundColor: Colors.white,
                                        )
                                      : button(
                                          width: 100,
                                          textColor: Colors.black,
                                          fontsize: 22,
                                          title:
                                              "\$ ${controller.productDetailModel!.ratePerHour}/hr",
                                          height: 35,
                                          borderColor: Colors.transparent,
                                          backgroundColor: Colors.white,
                                        ),
                                ],
                              )
                            ],
                          ),
                          SizedBox(
                            height: MySize.size12,
                          ),
                          if ((controller.productDetailModel!.location != null))
                            Row(
                              children: [
                                Image(
                                  image: AssetImage("assets/pin_location.png"),
                                  height: MySize.size20,
                                ),
                                (controller.productDetailModel!.location !=
                                        null)
                                    ? Text(
                                        controller
                                            .productDetailModel!.location!.name
                                            .toString(),
                                        style: TextStyle(
                                          color: appTheme.textGrayColor,
                                          fontSize: MySize.size14,
                                        ),
                                      )
                                    : SizedBox(),
                              ],
                            ),
                          SizedBox(
                            height: MySize.size10,
                          ),
                          Text(
                            controller.productDetailModel!.description
                                .toString(),
                            style: TextStyle(
                              color: appTheme.primaryTheme,
                              fontSize: MySize.size16,
                            ),
                          ),
                          (controller.isBooked.value)
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: MySize.size18,
                                    ),
                                    getTitleText(title: "General Info"),
                                    SizedBox(
                                      height: MySize.size10,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            MapUtils.openMap(
                                                double.parse(controller
                                                    .productDetailModel!
                                                    .location!
                                                    .latitude
                                                    .toString()),
                                                double.parse(controller
                                                    .productDetailModel!
                                                    .location!
                                                    .longitude
                                                    .toString()));
                                          },
                                          child: button(
                                            backgroundColor: Color(0xffE4E4E4),
                                            width: 160,
                                            widget: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(
                                                  "assets/loc.svg",
                                                  color: appTheme.primaryTheme,
                                                  height: MySize.size20,
                                                ),
                                                SizedBox(
                                                  width:
                                                      MySize.getScaledSizeWidth(
                                                          10),
                                                ),
                                                getText(title: "Get Direction"),
                                              ],
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: MySize.getScaledSizeWidth(15),
                                        ),
                                        if (controller
                                            .productDetailModel!.wifi!)
                                          InkWell(
                                            onTap: () {
                                              showModalBottomSheet(
                                                  context: context,
                                                  backgroundColor: Colors.white,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            30.0),
                                                  ),
                                                  builder: (context) {
                                                    return Container(
                                                      height: MySize
                                                          .getScaledSizeHeight(
                                                              450),
                                                      padding: EdgeInsets.symmetric(
                                                          horizontal: MySize
                                                              .getScaledSizeWidth(
                                                                  15),
                                                          vertical:
                                                              MySize.size20!),
                                                      alignment:
                                                          Alignment.topCenter,
                                                      child: Center(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Row(
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .center,
                                                              children: [
                                                                SvgPicture
                                                                    .asset(
                                                                  "assets/wifi.svg",
                                                                  color: Color(
                                                                      0xff676767),
                                                                  height: MySize
                                                                      .size20,
                                                                ),
                                                                SizedBox(
                                                                  width: MySize
                                                                      .getScaledSizeWidth(
                                                                          10),
                                                                ),
                                                                Text(
                                                                  "Wi-Fi Network",
                                                                  style:
                                                                      TextStyle(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .normal,
                                                                    color: Color(
                                                                        0xff676767),
                                                                    fontSize: MySize
                                                                        .size18,
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                            SizedBox(
                                                              height:
                                                                  MySize.size20,
                                                            ),
                                                            getCopySelectionData(
                                                                title:
                                                                    "Name/SSD",
                                                                name: controller
                                                                    .productDetailModel!
                                                                    .wifiName
                                                                    .toString(),
                                                                context:
                                                                    context),
                                                            SizedBox(
                                                              height:
                                                                  MySize.size20,
                                                            ),
                                                            getCopySelectionData(
                                                                title:
                                                                    "Password",
                                                                name: controller
                                                                    .productDetailModel!
                                                                    .wifiPassword
                                                                    .toString(),
                                                                context:
                                                                    context),
                                                            SizedBox(
                                                              height:
                                                                  MySize.size40,
                                                            ),
                                                            InkWell(
                                                              onTap: () {
                                                                SystemSettings
                                                                    .wifi();
                                                                Get.back();
                                                              },
                                                              child: button(
                                                                title:
                                                                    "Connect",
                                                                width: double
                                                                    .infinity,
                                                                textColor: appTheme
                                                                    .primaryTheme,
                                                                backgroundColor:
                                                                    Colors
                                                                        .white,
                                                                borderColor:
                                                                    appTheme
                                                                        .primaryTheme,
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              height:
                                                                  MySize.size20,
                                                            ),
                                                            InkWell(
                                                              onTap: () {
                                                                Get.back();
                                                              },
                                                              child: button(
                                                                title: "Cancel",
                                                                width: double
                                                                    .infinity,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    );
                                                  });
                                            },
                                            child: button(
                                              backgroundColor:
                                                  Color(0xffE4E4E4),
                                              width: 160,
                                              widget: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  SvgPicture.asset(
                                                    "assets/wifi.svg",
                                                    color:
                                                        appTheme.primaryTheme,
                                                    height: MySize.size20,
                                                  ),
                                                  SizedBox(
                                                    width: MySize
                                                        .getScaledSizeWidth(10),
                                                  ),
                                                  getText(
                                                      title: "Connect WIFI"),
                                                ],
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                    SizedBox(
                                      height: MySize.size18,
                                    ),
                                  ],
                                )
                              : SizedBox(),
                          SizedBox(
                            height: MySize.size18,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Working Hours",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: MySize.size20,
                                    ),
                                  ),
                                  SizedBox(
                                    height: MySize.size10,
                                  ),
                                  (controller.productDetailModel!.openAt !=
                                          null)
                                      ? Text(
                                          "${DateFormat("h:mm a").format(getDateFromString(controller.productDetailModel!.openAt.toString(), formatter: 'HH:mm:ss'))} to ${DateFormat("h:mm a").format(getDateFromString(controller.productDetailModel!.closeAt.toString(), formatter: 'HH:mm:ss'))}",
                                          style: TextStyle(
                                            fontWeight: FontWeight.normal,
                                            fontSize: MySize.size15,
                                          ),
                                        )
                                      : SizedBox(),
                                ],
                              ),
                              if (controller.bookingDataModel != null &&
                                  controller.bookingDataModel!.plan!)
                                button(
                                  width: 100,
                                  title: "Plan",
                                  textColor: Colors.black,
                                  height: 35,
                                  fontsize: 22,
                                  borderColor: Colors.transparent,
                                  backgroundColor: Colors.white,
                                ),
                            ],
                          ),

                          if (controller.bookingDataModel != null &&
                              controller.bookingDataModel!.plan!)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: MySize.size18,
                                ),
                                Text(
                                  "Plan Duration",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: MySize.size20,
                                  ),
                                ),
                                SizedBox(
                                  height: MySize.size10,
                                ),
                                (controller.bookingDataModel!.startAt != null)
                                    ? Text(
                                        "${DateFormat("yyyy-MM-dd").format(getDateFromString(controller.bookingDataModel!.startAt.toString()))} to ${DateFormat("yyyy-MM-dd").format(getDateFromString(
                                          controller.bookingDataModel!.endAt
                                              .toString(),
                                        ))}",
                                        style: TextStyle(
                                          fontWeight: FontWeight.normal,
                                          fontSize: MySize.size15,
                                        ),
                                      )
                                    : SizedBox(),
                              ],
                            ),
                          SizedBox(
                            height: MySize.size18,
                          ),

                          Text(
                            "Amenities",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: MySize.size20,
                            ),
                          ),
                          SizedBox(height: MySize.size10),
                          controller.productDetailModel!.getIcons.isEmpty
                              ? Container(
                                  height: MySize.size60,
                                  child: Center(
                                    child: Text("No Data Found"),
                                  ),
                                )
                              : SizedBox(
                                  height: MySize.size82,
                                  child: ListView(
                                    scrollDirection: Axis.horizontal,
                                    shrinkWrap: true,
                                    children:
                                        controller.productDetailModel!.getIcons
                                            .map((e) => getAmenities(
                                                  name: e["name"],
                                                  img: e["icon"],
                                                ))
                                            .toList(),
                                  ),
                                ),
                          SizedBox(height: MySize.size18),
                          getTitleText(title: "Address"),
                          SizedBox(
                            height: MySize.size10,
                          ),
                          getText(
                              title: controller.productDetailModel!.address
                                  .toString()),
                          SizedBox(
                            height: MySize.size10,
                          ),
                          // Image(
                          //   image: AssetImage("assets/map.png"),
                          // ),
                          // if (controller.hasImageData.value)
                          //   Image.memory(controller.imageBytesMap!,
                          //       height: 300),
                          Container(
                            height: MySize.getScaledSizeHeight(200),
                            width: 500,
                            child: GoogleMap(
                              // key: UniqueKey(),
                              markers: controller.markers,
                              onMapCreated: controller.onMapCreated,
                              initialCameraPosition: CameraPosition(
                                target: controller.centerNew!,
                                zoom: 11.0,
                              ),
                              myLocationButtonEnabled: false,
                              compassEnabled: true,
                              buildingsEnabled: true,
                              scrollGesturesEnabled: true,
                            ),
                          ),
                          // child: GoogleMap(
                          //   onMapCreated: controller.onMapCreated,
                          //   markers: controller.markers,
                          //   initialCameraPosition: CameraPosition(
                          //     target: controller.center!,
                          //     zoom: 11.0,
                          //   ),
                          // ),
                          // ),
                          if (controller.bookingDataModel != null &&
                              !controller.bookingDataModel!.plan!)
                            SizedBox(
                              height: MySize.size18,
                            ),
                          if (controller.bookingDataModel != null &&
                              !controller.bookingDataModel!.plan!)
                            getTitleText(title: "Cancellation Policy"),
                          if (controller.bookingDataModel != null &&
                              !controller.bookingDataModel!.plan!)
                            SizedBox(
                              height: MySize.size10,
                            ),
                          if (controller.bookingDataModel != null &&
                              !controller.bookingDataModel!.plan!)
                            getText(
                                title: controller
                                    .productDetailModel!.cancellationPolicy
                                    .toString()),
                          SizedBox(height: MySize.size28),
                          getTitleText(title: "Similar Spaces"),
                          SizedBox(height: MySize.size10),
                          (controller.hasData.value)
                              ? ((controller
                                      .resourceDataModel!.data!.rows!.isEmpty)
                                  ? Center(
                                      child: Text("No Data Found.."),
                                    )
                                  : SingleChildScrollView(
                                      scrollDirection: Axis.horizontal,
                                      child: Row(
                                        children: [
                                          for (int i = 0;
                                              i <
                                                  controller.resourceDataModel!
                                                      .data!.rows!.length;
                                              i++)
                                            InkWell(
                                              child: Card(
                                                elevation: 1,
                                                shadowColor:
                                                    appTheme.textGrayColor,
                                                child: Container(
                                                  height: MySize.size220,
                                                  width:
                                                      MySize.getScaledSizeWidth(
                                                          180),
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            MySize.size8!),
                                                  ),
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: MySize
                                                          .getScaledSizeWidth(
                                                              7),
                                                      vertical: MySize
                                                          .getScaledSizeHeight(
                                                              7)),
                                                  child: InkWell(
                                                    onTap: () {
                                                      Get.offAndToNamed(
                                                          Routes.productDetail,
                                                          arguments: {
                                                            "isBooked": false,
                                                            StringConstant
                                                                    .productData:
                                                                controller
                                                                    .resourceDataModel!
                                                                    .data!
                                                                    .rows![i]
                                                          });
                                                      print("ss");
                                                    },
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Container(
                                                          height:
                                                              MySize.size120,
                                                          child: Stack(
                                                            children: [
                                                              ClipRRect(
                                                                child:
                                                                    CommonNetworkImageView(
                                                                  url: (controller
                                                                              .resourceDataModel!
                                                                              .data!
                                                                              .rows![
                                                                                  i]
                                                                              .imageUrl !=
                                                                          null)
                                                                      ? controller
                                                                          .resourceDataModel!
                                                                          .data!
                                                                          .rows![
                                                                              i]
                                                                          .imageUrl
                                                                          .toString()
                                                                      : "",
                                                                  height: MySize
                                                                      .getScaledSizeHeight(
                                                                          120),
                                                                  fit: BoxFit
                                                                      .fill,
                                                                ),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            MySize.size4!),
                                                              ),
                                                              Positioned(
                                                                child: InkWell(
                                                                  onTap: () {
                                                                    String msg =
                                                                        "";
                                                                    String
                                                                        name =
                                                                        "Name : ${controller.resourceDataModel!.data!.rows![i].name}\n";
                                                                    String img =
                                                                        "Image : ${controller.resourceDataModel!.data!.rows![i].imageUrl}\n";
                                                                    String dec =
                                                                        "Description : ${controller.resourceDataModel!.data!.rows![i].description}\n";

                                                                    String
                                                                        pCapacity =
                                                                        "Person Capacity : ${controller.resourceDataModel!.data!.rows![i].personCapacity}\n";
                                                                    String
                                                                        avaiblity =
                                                                        "Availability : ${DateFormat("h:mm").format(getDateFromString(controller.resourceDataModel!.data!.rows![i].openAt.toString(), formatter: 'HH:mm:ss'))} - ${DateFormat("h:mm").format(getDateFromString(controller.resourceDataModel!.data!.rows![i].closeAt.toString(), formatter: 'HH:mm:ss'))}\n";
                                                                    String
                                                                        rate =
                                                                        "Rate Per Hour : ${controller.resourceDataModel!.data!.rows![i].ratePerHour}\n";
                                                                    String
                                                                        location =
                                                                        "Location : ${controller.resourceDataModel!.data!.rows![i].location!.name}\n";
                                                                    msg = name +
                                                                        img +
                                                                        dec +
                                                                        pCapacity +
                                                                        avaiblity +
                                                                        rate +
                                                                        location;
                                                                    Share.share(
                                                                        msg);
                                                                  },
                                                                  child:
                                                                      CircleAvatar(
                                                                    radius: MySize
                                                                        .size14,
                                                                    backgroundColor: Colors
                                                                        .white
                                                                        .withOpacity(
                                                                            0.5),
                                                                    child: SvgPicture
                                                                        .asset(
                                                                      "assets/share.svg",
                                                                      height: MySize
                                                                          .size14,
                                                                    ),
                                                                  ),
                                                                ),
                                                                right: MySize
                                                                    .size5,
                                                                top: MySize
                                                                    .size5,
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          height: MySize.size8!,
                                                        ),
                                                        FittedBox(
                                                          fit: BoxFit.scaleDown,
                                                          child: Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Text(
                                                                controller
                                                                    .resourceDataModel!
                                                                    .data!
                                                                    .rows![i]
                                                                    .name
                                                                    .toString(),
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: MySize
                                                                      .size16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                              ),
                                                              Row(
                                                                children: [
                                                                  SvgPicture
                                                                      .asset(
                                                                    "assets/user.svg",
                                                                    height: MySize
                                                                        .size12,
                                                                  ),
                                                                  SizedBox(
                                                                    width: MySize
                                                                        .getScaledSizeWidth(
                                                                            6),
                                                                  ),
                                                                  Text(
                                                                    controller
                                                                        .resourceDataModel!
                                                                        .data!
                                                                        .rows![
                                                                            i]
                                                                        .personCapacity
                                                                        .toString(),
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          MySize
                                                                              .size15,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .bold,
                                                                    ),
                                                                  )
                                                                ],
                                                              )
                                                            ],
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          height: MySize.size8!,
                                                        ),
                                                        Row(
                                                          children: [
                                                            SvgPicture.asset(
                                                              "assets/location_light.svg",
                                                              height:
                                                                  MySize.size12,
                                                            ),
                                                            SizedBox(
                                                              width: MySize
                                                                  .getScaledSizeWidth(
                                                                      5),
                                                            ),
                                                            Text(
                                                              (controller
                                                                          .resourceDataModel!
                                                                          .data!
                                                                          .rows![
                                                                              i]
                                                                          .location !=
                                                                      null)
                                                                  ? controller
                                                                      .resourceDataModel!
                                                                      .data!
                                                                      .rows![i]
                                                                      .location!
                                                                      .name
                                                                      .toString()
                                                                  : "-",
                                                              style: TextStyle(
                                                                fontSize: MySize
                                                                    .size16,
                                                                color: appTheme
                                                                    .textGrayColor,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                        SizedBox(
                                                          height: MySize.size8!,
                                                        ),
                                                        FittedBox(
                                                          fit: BoxFit.scaleDown,
                                                          child: Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Row(
                                                                children: [
                                                                  CircleAvatar(
                                                                    backgroundColor:
                                                                        Colors
                                                                            .green,
                                                                    radius: MySize
                                                                        .size4,
                                                                  ),
                                                                  Text(
                                                                    " Available",
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          MySize
                                                                              .size11,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              (controller
                                                                          .resourceDataModel!
                                                                          .data!
                                                                          .rows![
                                                                              i]
                                                                          .openAt !=
                                                                      null)
                                                                  ? Text(
                                                                      "${DateFormat("h:mm a").format(getDateFromString(controller.resourceDataModel!.data!.rows![i].openAt.toString(), formatter: 'HH:mm:ss'))} - ${DateFormat("h:mm a").format(
                                                                        getDateFromString(
                                                                            controller.resourceDataModel!.data!.rows![i].closeAt
                                                                                .toString(),
                                                                            formatter:
                                                                                'HH:mm:ss'),
                                                                      )}",
                                                                      style:
                                                                          TextStyle(
                                                                        fontSize:
                                                                            MySize.size11,
                                                                      ),
                                                                    )
                                                                  : SizedBox(),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              onTap: () {
                                                print("as");
                                                // Get.toNamed(
                                                //     Routes.PRODUCT_DETAIL,
                                                //     arguments: {
                                                //       "isBooked": false,
                                                //       StringConstant
                                                //               .productData:
                                                //           controller
                                                //               .resourceDataModel!
                                                //               .data!
                                                //               .rows![i]
                                                //     });
                                              },
                                            )
                                        ],
                                      ),
                                    ))
                              : Container(
                                  height: MySize.size70,
                                  child: Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ),
                          SizedBox(height: MySize.size70),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              // Get.arguments["isFromHistory"] != null
              //     ? SizedBox()
              //     :
              Positioned(
                bottom: MySize.size0,
                child: Container(
                  width: MySize.screenWidth,
                  color: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: 12,
                  ),
                  child: Center(
                    // child: button(title: "Book Now"),
                    child: (controller.isBooked.value)
                        ? (getButtonVisibleValue(controller: controller))
                            ? AnimatedToggleSwitch<bool>.dual(
                                current: controller.positive.value,
                                first: false,

                                second: true,
                                dif: MySize.getScaledSizeWidth(200),
                                innerColor: (!controller.positive.value)
                                    ? appTheme.primaryTheme
                                    : Colors.green,
                                borderColor: Colors.transparent,
                                //indicatorColor: Colors.green,

                                borderWidth: 5.0,
                                height: 55,

                                // boxShadow: const [
                                //   BoxShadow(
                                //     color: Colors.black26,
                                //     spreadRadius: 1,
                                //     blurRadius: 2,
                                //     offset: Offset(0, 1.5),
                                //   ),
                                // ],
                                onChanged: (b) async {
                                  // var headers = {
                                  //   'Authorization':
                                  //       'KISI-LOGIN 1a91144f7a387c1009de22561372b8d6',
                                  //   'Content-Type': 'application/json'
                                  // };
                                  // var request = http.Request(
                                  //     'POST',
                                  //     Uri.parse(
                                  //         'https://api.kisi.io/locks/${controller.productDetailModel!.lockId!}/unlock'));
                                  // request.body = json.encode({
                                  //   "context": {
                                  //     "location": {"longitude": 0, "latitude": 0}
                                  //   },
                                  //   "lock": {"proximity_proof": "string"}
                                  // });
                                  // request.headers.addAll(headers);
                                  //
                                  // http.StreamedResponse response =
                                  //     await request.send();
                                  //
                                  // if (response.statusCode == 200) {
                                  //   print(await response.stream.bytesToString());
                                  // } else {
                                  //   print(response.reasonPhrase);
                                  // }
                                  if (b == true) {
                                    controller.positive.value = b;
                                    Map<String, dynamic> dict = {
                                      "context": {
                                        "location": {
                                          "longitude": 0,
                                          "latitude": 0
                                        }
                                      },
                                      "lock": {"proximity_proof": "string"}
                                    };
                                    app
                                        .resolve<CustomDialogs>()
                                        .showCircularDialog(context);

                                    return NetworkClient.getInstance.callApi(
                                      context,
                                      "https://api.kisi.io/locks/",
                                      '${controller.productDetailModel!.lockId!}/unlock',
                                      MethodType.post,
                                      headers: {
                                        'Authorization':
                                            'KISI-LOGIN 1a91144f7a387c1009de22561372b8d6',
                                        'Content-Type': 'application/json',
                                        "accept": "application/json",
                                      },
                                      params: jsonEncode(dict),
                                      successCallback: (response, message) {
                                        print(response);
                                        controller.positive.value = b;
                                        app
                                            .resolve<CustomDialogs>()
                                            .hideCircularDialog(context);
                                        Map<String, dynamic> dict = {
                                          "context": {
                                            "location": {
                                              "longitude": 0,
                                              "latitude": 0
                                            }
                                          },
                                          "lock": {"proximity_proof": "string"}
                                        };
                                        app
                                            .resolve<CustomDialogs>()
                                            .showCircularDialog(context);

                                        return NetworkClient.getInstance
                                            .callApi(
                                          context,
                                          "https://api.kisi.io/locks/",
                                          '${controller.productDetailModel!.location!.lockId!}/unlock',
                                          MethodType.post,
                                          headers: {
                                            'Authorization':
                                                'KISI-LOGIN 1a91144f7a387c1009de22561372b8d6',
                                            'Content-Type': 'application/json',
                                            "accept": "application/json",
                                          },
                                          params: jsonEncode(dict),
                                          successCallback: (response, message) {
                                            print(response);
                                            controller.positive.value = b;
                                            app
                                                .resolve<CustomDialogs>()
                                                .hideCircularDialog(context);
                                            Get.offAllNamed(Routes.homeScreen);
                                            Get.snackbar(
                                                "Success", "Please open door.");
                                            if (controller.productDetailModel
                                                        ?.lockId2 !=
                                                    null ||
                                                controller.productDetailModel
                                                        ?.lockId2 !=
                                                    "null") {
                                              return NetworkClient.getInstance
                                                  .callApi(
                                                context,
                                                "https://api.kisi.io/locks/",
                                                '${controller.productDetailModel!.lockId2!}/unlock',
                                                MethodType.post,
                                                headers: {
                                                  'Authorization':
                                                      'KISI-LOGIN 1a91144f7a387c1009de22561372b8d6',
                                                  'Content-Type':
                                                      'application/json',
                                                  "accept": "application/json",
                                                },
                                                params: jsonEncode(dict),
                                                successCallback:
                                                    (response, message) {
                                                  print(response);
                                                  controller.positive.value = b;
                                                  app
                                                      .resolve<CustomDialogs>()
                                                      .hideCircularDialog(
                                                          context);
                                                  Get.offAllNamed(
                                                      Routes.homeScreen);
                                                  Get.snackbar("Success",
                                                      "Please open door.");
                                                  if (controller
                                                              .productDetailModel
                                                              ?.lockId3 !=
                                                          "null" ||
                                                      controller
                                                              .productDetailModel
                                                              ?.lockId3 !=
                                                          null) {
                                                    return NetworkClient
                                                        .getInstance
                                                        .callApi(
                                                      context,
                                                      "https://api.kisi.io/locks/",
                                                      '${controller.productDetailModel!.lockId3!}/unlock',
                                                      MethodType.post,
                                                      headers: {
                                                        'Authorization':
                                                            'KISI-LOGIN 1a91144f7a387c1009de22561372b8d6',
                                                        'Content-Type':
                                                            'application/json',
                                                        "accept":
                                                            "application/json",
                                                      },
                                                      params: jsonEncode(dict),
                                                      successCallback:
                                                          (response, message) {
                                                        print(response);
                                                        controller
                                                            .positive.value = b;
                                                        app
                                                            .resolve<
                                                                CustomDialogs>()
                                                            .hideCircularDialog(
                                                                context);
                                                        Get.offAllNamed(
                                                            Routes.homeScreen);
                                                        Get.snackbar("Success",
                                                            "Please open door.");
                                                      },
                                                      failureCallback:
                                                          (status, message) {
                                                        controller.positive
                                                            .value = false;

                                                        app
                                                            .resolve<
                                                                CustomDialogs>()
                                                            .hideCircularDialog(
                                                                context);
                                                        Get.snackbar("Error",
                                                            "Please message us if the door is not opening..");

                                                        print(" error");
                                                      },
                                                    );
                                                  }
                                                },
                                                failureCallback:
                                                    (status, message) {
                                                  controller.positive.value =
                                                      false;

                                                  app
                                                      .resolve<CustomDialogs>()
                                                      .hideCircularDialog(
                                                          context);
                                                  Get.snackbar("Error",
                                                      "Please message us if the door is not opening..");

                                                  print(" error");
                                                },
                                              );
                                            }
                                          },
                                          failureCallback: (status, message) {
                                            controller.positive.value = false;

                                            app
                                                .resolve<CustomDialogs>()
                                                .hideCircularDialog(context);
                                            Get.snackbar("Error",
                                                "Please message us if the door is not opening..");

                                            print(" error");
                                          },
                                        );
                                      },
                                      failureCallback: (status, message) {
                                        controller.positive.value = false;
                                        Get.snackbar("Error",
                                            "Please message us if the door is not opening..");

                                        app
                                            .resolve<CustomDialogs>()
                                            .hideCircularDialog(context);

                                        print(" error");
                                      },
                                    );
                                  }
                                },
                                colorBuilder: (b) =>
                                    b ? Colors.white : Colors.white,
                                iconBuilder: (value) => value
                                    ? Icon(Icons.done)
                                    : Center(
                                        child: Icon(Icons.arrow_forward_ios)),
                                textBuilder: (value) => value
                                    ? Center(
                                        child: Text(
                                        'Door Unlocked',
                                        style: TextStyle(color: Colors.white),
                                      ))
                                    : Center(
                                        child: Text(
                                        'Swipe to Unlock the Door',
                                        style: TextStyle(color: Colors.white),
                                      )),
                              )
                            : SizedBox()
                        : Column(
                            children: [
                              // Container(
                              //   width: MySize.getScaledSizeWidth(341),
                              //   color: Colors.white,
                              //   child: Center(
                              //     child: Text(
                              //       "Book you first 4 hour free",
                              //       style: TextStyle(
                              //           color: Colors.red,
                              //           fontWeight: FontWeight.bold,
                              //           fontSize: 18),
                              //     ),
                              //   ),
                              // ),
                              // Space.height(8),
                              ///TODO : HOURS FREE
                              InkWell(
                                onTap: () {
                                  Get.toNamed(Routes.createBooking, arguments: {
                                    Constant.productDetailModel:
                                        controller.productDetailModel
                                  });
                                },
                                child: button(
                                  title: (freeBookingTime() == '0')
                                      ? "Book Now"
                                      : "First ${freeBookingTime()} free",
                                ),
                              ),
                            ],
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget getAmenities({String? img, String? name}) {
    return Padding(
      padding: EdgeInsets.only(right: MySize.getScaledSizeWidth(17)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(MySize.size19!),
            child: Container(
              height: MySize.size55,
              width: MySize.size55,
              color: Color(0xffFFEAEA),
              child: Center(
                child: Image.asset(img!),
              ),
            ),
          ),
          SizedBox(height: MySize.size8),
          Text(
            name!,
            style: TextStyle(fontSize: MySize.size14),
          ),
        ],
      ),
    );
  }

  Text getTitleText({String? title}) {
    return Text(
      title!,
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: MySize.size20,
      ),
    );
  }

  Text getText({String? title}) {
    return Text(
      title!,
      style: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: MySize.size15,
      ),
    );
  }

  getButtonVisibleValue({ProductDetailController? controller}) {
    if (controller!.bookingDataModel != null &&
        controller.bookingDataModel!.plan!) {
      if (DateTime.now().isAfter(
              getDateFromString(controller.bookingDataModel!.startAt.toString())
                  .subtract(Duration(minutes: 15))) &&
          DateTime.now().isBefore(getDateFromString(
              controller.bookingDataModel!.endAt.toString()))) {
        return true;
      } else {
        return false;
      }
    } else {
      if (getDateFromString(controller.bookingDataModel!.startAt.toString())
              .difference(DateTime.now())
              .inMinutes <=
          5) {
        return true;
      } else {
        return false;
      }
    }
  }

  String freeBookingTime() {
    double totalBookingHours =
        double.parse(box.read(Constant.totalBookingHours).toString());
    if (totalBookingHours < 0) {
      int remainingMinutes = ((0 - totalBookingHours) * 60).toInt();

      // Calculate the number of 15-minute intervals
      int intervals = (remainingMinutes / 15).ceil();

      // Calculate the new rounded-off remaining minutes
      int roundedRemainingMinutes = intervals * 15;

      int hours = roundedRemainingMinutes ~/ 60;
      int minutes = roundedRemainingMinutes % 60;
      return '$hours hours and $minutes minutes';
    } else {
      return '0';
    }
  }

  Widget getCopySelectionData(
      {String? title, String? name, BuildContext? context}) {
    return Container(
      height: MySize.size80,
      width: double.infinity,
      decoration: BoxDecoration(
          border: Border.all(
            width: 1,
            color: appTheme.borderColor,
          ),
          borderRadius: BorderRadius.circular(MySize.size4!)),
      padding: EdgeInsets.symmetric(
        horizontal: MySize.getScaledSizeWidth(20),
        vertical: MySize.size10!,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title!,
                style: TextStyle(
                    fontSize: MySize.size12, color: appTheme.textGrayColor),
              ),
              InkWell(
                onTap: () {
                  Clipboard.setData(ClipboardData(text: name!));
                  const snackBar = SnackBar(
                    content: Text('Copied..'),
                  );
                  Get.snackbar("Copied.", 'Copied Successfully..');

// Find the ScaffoldMessenger in the widget tree
// and use it to show a SnackBar.
                  //ScaffoldMessenger.of(context!).showSnackBar(snackBar);
                },
                child: Row(
                  children: [
                    SvgPicture.asset(
                      "assets/copy.svg",
                      height: MySize.size12,
                    ),
                    SizedBox(width: MySize.getScaledSizeWidth(8)),
                    Text(
                      "Copy",
                      style: TextStyle(
                          fontSize: MySize.size12,
                          color: appTheme.textGrayColor),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Text(
            name!,
            style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.bold,
                color: appTheme.primaryTheme),
          )
        ],
      ),
    );
  }
}
