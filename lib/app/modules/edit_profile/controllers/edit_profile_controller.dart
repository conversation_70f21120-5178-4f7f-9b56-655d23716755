import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:small_officer/Models/user_profile_model.dart';
import 'package:small_officer/constants/size_constant.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../../../routes/app_pages.dart';
import '../../my_profile/controllers/my_profile_controller.dart';

class EditProfileController extends GetxController {
  //TODO: Implement EditProfileController

  final count = 0.obs;
  late Rx<File> selectedImg;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  RxBool hasImg = false.obs;
  MyProfileController myProfileController = Get.find<MyProfileController>();
  RxBool hasImgInApi = false.obs;
  String? imgFileName;

  ProfileData? profiledata;
  Rx<TextEditingController> nameController = TextEditingController().obs;
  Rx<TextEditingController> emailController = TextEditingController().obs;
  Rx<TextEditingController> aboutController = TextEditingController().obs;
  Rx<TextEditingController> mobileNumber = TextEditingController().obs;
  Rx<TextEditingController> companyName = TextEditingController().obs;

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      profiledata = Get.arguments["userData"];
      if (profiledata != null) {
        if (!isNullEmptyOrFalse(profiledata!.firstName)) {
          nameController.value.text = profiledata!.firstName.toString();
        }
        if (!isNullEmptyOrFalse(profiledata!.email)) {
          emailController.value.text = profiledata!.email.toString();
        }
        if (!isNullEmptyOrFalse(profiledata!.about)) {
          aboutController.value.text = profiledata!.about.toString();
        }
        if (!isNullEmptyOrFalse(profiledata!.mobile)) {
          mobileNumber.value.text = profiledata!.mobile.toString();
        }
        if (!isNullEmptyOrFalse(profiledata!.imageUrl)) {
          hasImgInApi.value = true;
        }
      }
    }
  }

  callApiForUpdateUser({required BuildContext context}) async {
    FocusScope.of(context).unfocus();
    Map<String, dynamic> dict = {};
    dict["firstName"] = nameController.value.text;
    dict["lastName"] = "";
    dict["email"] = emailController.value.text;

    dict["mobile"] = mobileNumber.value.text;
    dict["image"] = hasImg.value
        ? await MultipartFile.fromFile(selectedImg.value.path,
            filename: imgFileName)
        : null;
    FormData formData = FormData.fromMap(dict);
    app.resolve<CustomDialogs>().showCircularDialog(context);

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.userProfile,
      MethodType.patch,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: formData,
      successCallback: (response, message) {
        myProfileController.hasData.value = false;
        myProfileController.callApiForGetSourceTypeList(context: context);
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app
            .resolve<CustomDialogs>()
            .getDialog(title: "Success", desc: "Profile Update Sucessfully.");
        //Get.back();

        //AuthModel authModel = AuthModel.fromJson(response);
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app.resolve<CustomDialogs>().getDialog(title: "Failed", desc: message);

        print(" error");
      },
    );
  }

  callApiForDeleteUser({
    required BuildContext context,
  }) {
    FocusScope.of(context).unfocus();

    Map<String, dynamic> dict = {};

    app.resolve<CustomDialogs>().showCircularDialog(context);
    FormData formData = FormData.fromMap(dict);
    NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.deleteMe,
      MethodType.delete,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: formData,
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);

        // Get.snackbar("Success", "User removed successfully.");
        Get.offAllNamed(Routes.loginScreen);
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app
            .resolve<CustomDialogs>()
            .getDialog(title: "Error", desc: status["message"]);
      },
    );
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}
  void increment() => count.value++;
}
