import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:path/path.dart' as p;
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:small_officer/utilities/submit_button.dart';

import '../../../../utilities/utilities.dart';
import '../../../routes/app_pages.dart';
import '../controllers/edit_profile_controller.dart';

class EditProfileView extends GetWidget<EditProfileController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  top: MySize.size17!,
                  right: MySize.size13!,
                  bottom: MySize.size17!),
              child: SvgPicture.asset(
                "assets/arrow_back.svg",
              ),
            ),
          ),
          title: Container(
            // color: Colors.red,
            width: MySize.getScaledSizeWidth(192),
            height: MySize.getScaledSizeHeight(55),
            child: Image(
              image: AssetImage("assets/logo1.jpg"),
              fit: BoxFit.fill,
            ),
          ),
          actions: [
            InkWell(
              onTap: () {
                Get.toNamed(Routes.notification);
              },
              child: Padding(
                padding: EdgeInsets.only(
                    left: MySize.getScaledSizeWidth(15),
                    right: MySize.getScaledSizeWidth(15),
                    top: MySize.size15!,
                    bottom: MySize.size15!),
                child: Image(
                  image: AssetImage("assets/notification.png"),
                ),
              ),
            ),
          ],
          centerTitle: true,
          elevation: 0,
        ),
        body: Container(
          height: MySize.screenHeight,
          width: MySize.screenWidth,
          padding: EdgeInsets.symmetric(
              horizontal: MySize.getScaledSizeWidth(30),
              vertical: MySize.size20!),
          child: Form(
            key: controller.formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Edit Profile",
                    style: TextStyle(
                      fontSize: MySize.size20,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(
                    height: MySize.size20,
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          blurRadius: 1,
                          color: appTheme.textGrayColor,
                        )
                      ],
                    ),
                    child: InkWell(
                      onTap: () async {
                        final ImagePicker _picker = ImagePicker();
                        final XFile? image = await _picker.pickImage(
                            source: ImageSource.gallery);
                        if (image != null) {
                          File imagepath = File(image.path);
                          controller.imgFileName =
                              p.basenameWithoutExtension(imagepath.path);
                          controller.selectedImg = File(image.path).obs;
                          controller.hasImg.value = true;
                          controller.selectedImg.refresh();
                          controller.hasImg.refresh();
                        }
                      },
                      child: Container(
                        child: Center(
                            child: (controller.hasImg.value)
                                ? CircleAvatar(
                                    backgroundColor: Color(0xffFFEAEA),
                                    radius: MySize.size50,
                                    backgroundImage:
                                        FileImage(controller.selectedImg.value),
                                  )
                                : (controller.hasImgInApi.value)
                                    ? ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(200),
                                        child: CircleAvatar(
                                          backgroundColor: Colors.white,
                                          radius: MySize.size50,
                                          child: Center(
                                            child: CommonNetworkImageView(
                                              url: controller
                                                  .profiledata!.imageUrl
                                                  .toString(),
                                            ),
                                          ),
                                        ),
                                      )
                                    : CircleAvatar(
                                        backgroundColor:
                                            Color(0xff000000).withOpacity(0.1),
                                        radius: MySize.size50,
                                        child: Center(
                                          child: Image(
                                            image:
                                                AssetImage("assets/user.png"),
                                            height: MySize.size40,
                                          ),
                                        ),
                                      )),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: MySize.size20,
                  ),
                  Container(
                    height: MySize.size1,
                    width: double.infinity,
                    color: appTheme.textGrayColor,
                  ),
                  SizedBox(
                    height: MySize.size20,
                  ),
                  getTextFiledData(
                      name: "Name:*",
                      validator: (val) {
                        if (val!.isEmpty) {
                          return "Please Enter Name";
                        }
                        return null;
                      },
                      textEditingController: controller.nameController.value),
                  // SizedBox(
                  //   height: MySize.size20,
                  // ),
                  // getTextFiledData(
                  //     name: "About:*",
                  //     textEditingController: controller.aboutController.value),
                  SizedBox(
                    height: MySize.size20,
                  ),
                  getTextFiledData(
                      name: "Phone Number:*",
                      inputType: TextInputType.number,
                      validator: (val) {
                        if (val!.isEmpty) {
                          return "Please Enter Mobile Number";
                        }
                        return null;
                      },
                      textEditingController: controller.mobileNumber.value),
                  SizedBox(
                    height: MySize.size20,
                  ),
                  getTextFiledData(
                    name: "Email:*",
                    validator: (val) {
                      if (val!.isEmpty) {
                        return "Please Enter Email";
                      }
                      return null;
                    },
                    action: TextInputAction.done,
                    inputType: TextInputType.emailAddress,
                    textEditingController: controller.emailController.value,
                  ),
                  Container(
                    height: 200,
                  ),
                  Center(
                    child: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            if (controller.formKey.currentState!.validate()) {
                              controller.callApiForUpdateUser(context: context);
                            }
                          },
                          child: button(
                            title: "Save",
                          ),
                        ),
                        SizedBox(
                            height: 25), // Add some spacing between the buttons
                        InkWell(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) => AlertDialog(
                                title: Text("Confirmation",
                                    style: TextStyle(color: Colors.red)),
                                content: Text(
                                    "Are you sure you want to delete your account?",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: MySize.size18)),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(
                                        context), // Close the dialog
                                    child: Text("Cancel",
                                        style: TextStyle(
                                            color: Colors.blue,
                                            fontSize: MySize.size16)),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      controller.callApiForDeleteUser(
                                          context: Get.context!);
                                      // Navigator.pop(
                                      //     context); // Close the dialog
                                    },
                                    child: Text("Delete",
                                        style: TextStyle(
                                            color: Colors.red,
                                            fontSize: MySize.size16)),
                                  ),
                                ],
                              ),
                            );
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.red, width: 1.5),
                              borderRadius: BorderRadius.circular(5.0),
                            ),
                            child: button(
                              title: "Delete Account",
                              backgroundColor: Colors.transparent,
                              textColor: Colors.red,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget getTextFiledData({
    String? name,
    TextInputAction action = TextInputAction.next,
    TextInputType inputType = TextInputType.name,
    TextEditingController? textEditingController,
    FormFieldValidator<String>? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          name!,
          style: TextStyle(
            color: Colors.black.withOpacity(0.6),
          ),
        ),
        Container(
          child: TextFormField(
            cursorColor: appTheme.primaryTheme,
            textInputAction: action,
            controller: textEditingController,
            keyboardType: inputType,
            validator: validator,
            decoration: InputDecoration(
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: appTheme.textGrayColor),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: BaseTheme().borderColor),
              ),
              border: UnderlineInputBorder(
                  borderSide: BorderSide(color: appTheme.textGrayColor)),
            ),
          ),
          height: MySize.size40,
        )
      ],
    );
  }
}
