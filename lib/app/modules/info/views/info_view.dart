import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:small_officer/constants/color_constant.dart';
import '../../../../constants/size_constant.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/info_controller.dart';

class InfoView extends GetWidget<InfoController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  top: MySize.size17!,
                  right: MySize.size13!,
                  bottom: MySize.size17!),
              child: SvgPicture.asset(
                "assets/arrow_back.svg",
              ),
            )),

        title: Container(
          // color: Colors.red,
          width: MySize.getScaledSizeWidth(192),
          height: MySize.getScaledSizeHeight(55),
          child: Image(
            image: AssetImage("assets/logo1.jpg"),
            fit: BoxFit.fill,
          ),
        ),
        // actions: [
        //   Padding(
        //     padding: EdgeInsets.only(
        //         left: MySize.getScaledSizeWidth(15),
        //         right: MySize.getScaledSizeWidth(15),
        //         top: MySize.size15!,
        //         bottom: MySize.size15!),
        //     child: Image(
        //       image: AssetImage("assets/notification.png"),
        //     ),
        //   ),
        // ],
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        height: MySize.screenHeight,
        width: MySize.screenWidth,
        padding: EdgeInsets.symmetric(
          horizontal: MySize.getScaledSizeWidth(15),
          vertical: MySize.size15!,
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  "assets/location.svg",
                  height: MySize.size23,
                  width: MySize.getScaledSizeWidth(37),
                ),
                SizedBox(
                  width: MySize.getScaledSizeWidth(14),
                ),
                Row(
                  children: [
                    Text(
                      controller.locationName.toString(),
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(
                      width: MySize.getScaledSizeWidth(7),
                    ),
                    SvgPicture.asset(
                      "assets/arrow_bottom.svg",
                      height: MySize.size10,
                      color: Colors.black.withOpacity(0.7),
                    ),
                  ],
                ),
                SizedBox(
                  width: MySize.getScaledSizeWidth(10),
                ),
              ],
            ),
            SizedBox(
              height: MySize.size20,
            ),
            Expanded(
              child: Container(
                child: (true)
                    ? ((!isNullEmptyOrFalse(controller.bannerList))
                        ? ListView.separated(
                            itemBuilder: (context, i) {
                              // return (isNullEmptyOrFalse(
                              //         controller.bannerList[i].imageUrl))
                              //     ? Column(
                              //         crossAxisAlignment:
                              //             CrossAxisAlignment.start,
                              //         children: [
                              //           Text(
                              //             controller.bannerList[i].title
                              //                 .toString(),
                              //             style: TextStyle(
                              //               color: appTheme.primaryTheme,
                              //               fontSize: MySize.size22,
                              //               fontWeight: FontWeight.bold,
                              //             ),
                              //           ),
                              //           // SizedBox(
                              //           //   height: MySize.size10,
                              //           // ),
                              //           // Text(
                              //           //   controller.bannerList[i].description
                              //           //       .toString(),
                              //           //   style: TextStyle(
                              //           //     color: appTheme.primaryTheme,
                              //           //     fontSize: MySize.size18,
                              //           //     fontWeight: FontWeight.normal,
                              //           //   ),
                              //           // ),
                              //         ],
                              //       )
                              //     : ClipRRect(
                              //         child: CommonNetworkImageView(
                              //           url: controller.bannerList[i].imageUrl
                              //               .toString(),
                              //           height: MySize.getScaledSizeHeight(200),
                              //           width: double.infinity,
                              //           fit: BoxFit.fill,
                              //         ),
                              //         borderRadius:
                              //             BorderRadius.circular(MySize.size15!),
                              //         // child: Image(
                              //         //   image: AssetImage(
                              //         //     "assets/table.png",
                              //         //   ),
                              //         //   fit: BoxFit.cover,
                              //         //   width: MySize.getScaledSizeWidth(380),
                              //         // ),
                              //       );
                              return Card(
                                shape: RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.circular(MySize.size15!),
                                ),
                                margin: EdgeInsets.symmetric(
                                    // horizontal: MySize.getScaledSizeWidth(10),
                                    vertical: MySize.size10!),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: MySize.getScaledSizeWidth(10),
                                      vertical: MySize.size10!),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (!isNullEmptyOrFalse(
                                          controller.bannerList[i].title))
                                        Text(
                                          controller.bannerList[i].title
                                              .toString(),
                                          style: TextStyle(
                                            color: appTheme.primaryTheme,
                                            fontSize: MySize.size22,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      if (!isNullEmptyOrFalse(
                                          controller.bannerList[i].description))
                                        Padding(
                                          padding: EdgeInsets.only(
                                              top: MySize.size12!),
                                          child: Text(
                                            controller.bannerList[i].description
                                                .toString(),
                                            style: TextStyle(
                                              color: appTheme.primaryTheme,
                                              fontSize: MySize.size18,
                                              fontWeight: FontWeight.normal,
                                            ),
                                          ),
                                        ),
                                      if (!isNullEmptyOrFalse(
                                          controller.bannerList[i].imageUrl))
                                        ClipRRect(
                                          child: CommonNetworkImageView(
                                            url: controller
                                                .bannerList[i].imageUrl
                                                .toString(),
                                            height:
                                                MySize.getScaledSizeHeight(200),
                                            width: double.infinity,
                                            fit: BoxFit.fill,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                              MySize.size15!),
                                          // child: Image(
                                          //   image: AssetImage(
                                          //     "assets/table.png",
                                          //   ),
                                          //   fit: BoxFit.cover,
                                          //   width: MySize.getScaledSizeWidth(380),
                                          // ),
                                        ),
                                    ],
                                  ),
                                ),
                              );
                            },
                            separatorBuilder: (context, i) {
                              return SizedBox(
                                height: MySize.size20,
                              );
                            },
                            itemCount: controller.bannerList.length)
                        : Center(
                            child: Text(
                              "No data found",
                              style: TextStyle(
                                color: appTheme.primaryTheme,
                              ),
                            ),
                          ))
                    : Center(
                        child: CircularProgressIndicator(
                          color: appTheme.primaryTheme,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
