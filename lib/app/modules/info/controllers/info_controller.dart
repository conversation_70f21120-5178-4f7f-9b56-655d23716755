import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:small_officer/Models/info_model.dart';
import 'package:small_officer/Models/location_data_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../data/network_client.dart';

class InfoController extends GetxController {
  //TODO: Implement InfoController

  final count = 0.obs;
  List<LocationData>? locationData;
  RxBool hasData = false.obs;
  String? locationName = "";
  InfoListModel? infoListModel;
  List<Infos> bannerList = [];
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      //locationData = Get.arguments[Constant.locationList];
      if (Get.arguments[Constant.announcementList] != null) {
        bannerList = Get.arguments[Constant.announcementList];
      }
      locationName = Get.arguments[Constant.locationName].toString();
      //print(locationData!.length.toString());
    }
    // WidgetsBinding.instance!.addPostFrameCallback((_) async {
    //   callApiForInfoList(context: Get.context!);
    // });
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  callApiForInfoList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    Map<String, dynamic> dict = {};

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.info,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        infoListModel = InfoListModel.fromJson(response);
      },
      failureCallback: (status, message) {
        hasData.value = true;
        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
