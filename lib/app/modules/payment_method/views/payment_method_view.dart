import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:small_officer/constants/color_constant.dart';
import '../../../../constants/size_constant.dart';
import '../controllers/payment_method_controller.dart';

class PaymentMethodView extends GetView<PaymentMethodController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Padding(
            padding: EdgeInsets.only(
                left: MySize.getScaledSizeWidth(15),
                top: MySize.size17!,
                bottom: MySize.size17!),
            child: Icon(
              Icons.arrow_back_ios,
              color: appTheme.primaryTheme,
            ),
          ),
        ),
        title: Container(
          // color: Colors.red,
          width: MySize.getScaledSizeWidth(192),
          height: MySize.getScaledSizeHeight(55),
          child: Image(
            image: AssetImage("assets/logo1.jpg"),
            fit: BoxFit.fill,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: true,
      ),
      body: Container(
        height: MySize.screenHeight,
        width: MySize.screenWidth,
        padding: EdgeInsets.symmetric(
          horizontal: MySize.getScaledSizeWidth(30),
          vertical: MySize.size30!,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Payment Method",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: MySize.size22,
              ),
            ),
            SizedBox(
              height: MySize.size15,
            ),
            Container(
              width: double.infinity,
              height: MySize.size1,
              color: appTheme.textGrayColor,
            ),
            SizedBox(
              height: MySize.size15,
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Row(
            //       children: [
            //         Container(
            //           height: MySize.size50,
            //           width: MySize.size50,
            //           decoration: BoxDecoration(
            //             border: Border.all(
            //               color: appTheme.textGrayColor,
            //             ),
            //             borderRadius: BorderRadius.circular(
            //               MySize.size4!,
            //             ),
            //           ),
            //           child: Padding(
            //             padding: EdgeInsets.all(MySize.size8!),
            //             child: SvgPicture.asset(
            //               "assets/credit_card.svg",
            //               height: MySize.size20,
            //               width: MySize.size20,
            //             ),
            //           ),
            //         ),
            //         SizedBox(
            //           width: MySize.getScaledSizeWidth(20),
            //         ),
            //         Text(
            //           "Credit Card",
            //           style: TextStyle(
            //             color: appTheme.primaryTheme,
            //             fontSize: MySize.size16,
            //             fontWeight: FontWeight.w400,
            //           ),
            //         )
            //       ],
            //     ),
            //     Icon(Icons.arrow_forward_ios),
            //   ],
            // ),
            // SizedBox(
            //   height: MySize.size20,
            // ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   children: [
            //     Row(
            //       children: [
            //         Container(
            //           height: MySize.size50,
            //           width: MySize.size50,
            //           decoration: BoxDecoration(
            //             border: Border.all(
            //               color: appTheme.textGrayColor,
            //             ),
            //             borderRadius: BorderRadius.circular(
            //               MySize.size4!,
            //             ),
            //           ),
            //           child: Padding(
            //             padding: EdgeInsets.all(MySize.size10!),
            //             child: SvgPicture.asset(
            //               "assets/apple.svg",
            //               height: MySize.size20,
            //               width: MySize.size20,
            //             ),
            //           ),
            //         ),
            //         SizedBox(
            //           width: MySize.getScaledSizeWidth(20),
            //         ),
            //         Text(
            //           "Apple Pay",
            //           style: TextStyle(
            //             color: appTheme.primaryTheme,
            //             fontSize: MySize.size16,
            //             fontWeight: FontWeight.w400,
            //           ),
            //         )
            //       ],
            //     ),
            //     Icon(Icons.arrow_forward_ios),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }
}
