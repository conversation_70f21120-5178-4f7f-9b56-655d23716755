import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

enum DRAG_TYPE { TOP, MIDDLE, BOTTOM }

class RenderWidget extends SingleChildRenderObjectWidget {
  final DateTime? time;
  final int index;
  final bool isMeeting;
  final String? meetingId;
  final DRAG_TYPE dragType;
  final bool enable;

  RenderWidget({
    required Widget child,
    this.time,
    required this.isMeeting,
    this.meetingId,
    this.dragType = DRAG_TYPE.MIDDLE,
    this.index = 0,
    this.enable = true
  }) : super(child: child);

  @override
  RenderProxyWidget createRenderObject(BuildContext context) {
    return RenderProxyWidget(
        time: time, index: index);
  }

  @override
  void updateRenderObject(
      BuildContext context, RenderProxyWidget renderObject) {
    renderObject..time = time;
    renderObject..index = index;

    renderObject..isMeeting = isMeeting;
    renderObject..meetingId = meetingId;

    renderObject..dragType = dragType;
    renderObject..enable = enable;
  }
}

class RenderProxyWidget extends RenderProxyBox {
  int index;
  DateTime? time;
  bool isMeeting;
  String? meetingId;
  DRAG_TYPE dragType;
  bool enable;

  RenderProxyWidget(
      {
      this.time,
      this.index = 0,
      this.isMeeting = false,
      this.dragType = DRAG_TYPE.MIDDLE,
        this.enable= true,
      this.meetingId});
}
