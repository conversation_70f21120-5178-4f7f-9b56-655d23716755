import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:linked_scroll_controller/linked_scroll_controller.dart';
import 'package:small_officer/app/modules/create_booking/controllers/create_booking_controller.dart';
import 'package:small_officer/app/modules/create_booking/views/multiplication_table_cell.dart';
import 'package:small_officer/app/modules/create_booking/views/render_widget.dart';

class TableBody extends StatefulWidget {
  TableBody(this.controller);

  final CreateBookingController controller;

  @override
  State<TableBody> createState() => _TableBodyState();
}

class _TableBodyState extends State<TableBody> {
  late LinkedScrollControllerGroup _controllers;
  late ScrollController _firstColumnController;
  late ScrollController _restColumnsController;
  late ScrollController _restColumnsController2;

  late double cellWidth = 75.0;
  final key = GlobalKey();

  DateFormat dateFormatter = DateFormat('dd/MM/yyyy HH:mm');
  DateFormat timeFormatter = DateFormat('hh:mm a');

  //List<Meeting> meetings = [];

  DateTime currentTime = DateTime.now();

  List<DateTime> get timelines => widget.controller.timelines; //fullTimelineList();

  List<Meeting> get meetings => widget.controller.meetingData.value;

  List<DateTime> fullTimelineList() {
    //final diff = widget.controller.openTime.value.difference(widget.controller.closeTime.value);

    DateTime dateTime = DateTime(
      currentTime.year,
      currentTime.month,
      currentTime.day,
      0,
      0,
    );
    return List.generate(24, (index) {
      dateTime = dateTime.add(Duration(hours: 1));
      return dateTime;
    });
  }

  //final Set<RenderProxyWidget> selectedTimes = Set<RenderProxyWidget>();
  final Set<RenderProxyWidget> _trackTaped = <RenderProxyWidget>{};

  bool activeSelection = false;
  String? activeMeetingId;
  DRAG_TYPE? dragType;
  DateTime? activeDateTime;

  @override
  void initState() {
    _controllers = LinkedScrollControllerGroup();
    _firstColumnController = _controllers.addAndGet();
    _restColumnsController = _controllers.addAndGet();
    _restColumnsController2 = _controllers.addAndGet();

    super.initState();

    // update meeting
    WidgetsBinding.instance.addPostFrameCallback((_) {
      updateMeeting("0", DateTime.now().roundDown(), DateTime.now().roundDown().add(Duration(hours: 1)));
    });

    widget.controller.selectedDate.listen((p0) {
      updateMeeting("0", DateTime.now().roundDown(), DateTime.now().roundDown().add(Duration(hours: 1)));
      _restColumnsController.animateTo(
        0,
        curve: Curves.easeOut,
        duration: Duration(milliseconds: 200),
      );
    });
  }

  _detectTapedItem(PointerEvent event) {
    final RenderBox box = key.currentContext!.findRenderObject() as RenderBox;
    final result = BoxHitTestResult();
    Offset local = box.globalToLocal(event.position);

    if (box.hitTest(result, position: local)) {
      for (final hit in result.path) {
        /// temporary variable so that the [is] allows access of [index]
        final target = hit.target;
        if (target is RenderProxyWidget &&
            //!_trackTaped.contains(target) &&
            activeSelection) {
          _trackTaped.add(target);

          if (target.enable) {
            _selectTime(target);
          }
        }
      }
    }
  }

  //manage tapped time range
  _selectTime(RenderProxyWidget time) async {
    print("select time - ${time.isMeeting}");

    Meeting? activeMeeting;

    if (time.isMeeting) {
      activeMeetingId = time.meetingId;
      dragType = time.dragType;
    }

    activeMeeting = meetings.firstWhereOrNull((element) => element.id == activeMeetingId);

    if (activeMeeting != null) {
      if (dragType == DRAG_TYPE.MIDDLE) {
        if (activeMeeting.from.isAfter(time.time!)) {
          print("moving to top");
          updateMeeting(activeMeeting.id, time.time!, time.time!.add(activeMeeting.to.difference(activeMeeting.from)));
        } else {
          print("moving to bottom");
          updateMeeting(
            activeMeeting.id,
            time.time!.subtract(activeMeeting.to.difference(activeMeeting.from)),
            time.time!,
          );
        }
      } else if (dragType == DRAG_TYPE.TOP) {
        updateMeeting(activeMeeting.id, time.time!, activeMeeting.to);
      } else if (dragType == DRAG_TYPE.BOTTOM) {
        print("Tap on bottom");

        updateMeeting(activeMeeting.id, activeMeeting.from, time.time!);
      }
    }
  }

  void updateMeeting(String id, DateTime start, DateTime end) {
    int index = meetings.indexWhere((element) => element.id == id);

    if (index != -1) {
      DateTime newStartDate = start;
      DateTime newEndTime = end;

      // minimum 60 minute required
      if (newEndTime.isAfter(newStartDate) && newEndTime.difference(newStartDate).inMinutes >= 60) {
        setState(() {
          meetings[index].from = newStartDate;
          meetings[index].to = newEndTime;

          widget.controller.startTime = newStartDate;
          widget.controller.endTime = newEndTime;
        });
        widget.controller.checkIsTimeAvailForNewBooking();
      }
    }
  }

  //clear all dragged items
  void _clearSelection(PointerUpEvent event) {
    _trackTaped.clear();
    setState(() {
      activeMeetingId = null;
      activeSelection = false;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _firstColumnController.dispose();
    _restColumnsController.dispose();
    super.dispose();
  }

  //convert color hex text to color
  Color getColorFromHex(String hexColor) {
    hexColor = hexColor.replaceAll("#", "");
    final Random random = Random();
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor";
    }
    if (hexColor.length == 8) {
      return Color(int.parse("0x$hexColor"));
    } else {
      return Color.fromRGBO(random.nextInt(255), random.nextInt(255), random.nextInt(255), 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return bodyWidget();
  }

  Widget bodyWidget() {
    return Listener(
      onPointerDown: _detectTapedItem,
      onPointerMove: _detectTapedItem,
      onPointerUp: _clearSelection,
      behavior: HitTestBehavior.deferToChild,
      child: Row(
        children: [
          SizedBox(
            width: 70,
            child: ListView(
              controller: _firstColumnController,
              physics: ClampingScrollPhysics(),
              children: List.generate(timelines.length, (index) {
                DateTime datetime = timelines[index];
                return MultiplicationTableCell(
                  color: Colors.white,
                  cellWidth: 70,
                  cellHeight: 50,
                  child: Text(
                    timeFormatter.format(datetime),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                );
              }),
            ),
          ),
          const SizedBox(width: 5),
          Expanded(
            child: Stack(
              children: [
                ListView(
                  key: key,
                  controller: _restColumnsController,
                  physics: const ClampingScrollPhysics(),
                  children: List.generate(
                    timelines.length,
                    (timelineIndex) {
                      DateTime timeline = timelines[timelineIndex];

                      return Container(
                        height: 50,
                        width: double.infinity,
                        child: Stack(
                          children: [
                            Positioned(
                              bottom: 0,
                              top: 0,
                              left: 0,
                              right: 0,
                              child: Container(
                                alignment: Alignment.bottomCenter,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border.all(
                                    color: Colors.black12,
                                    width: 1.0,
                                  ),
                                ),
                              ),
                            ),

                            //timer listener
                            Column(
                              children: List.generate(
                                4,
                                (index) {
                                  DateTime newTime = timeline.add(Duration(minutes: (index * 15)));

                                  return Stack(
                                    children: [
                                      RenderWidget(
                                        isMeeting: false,
                                        index: index,
                                        time: newTime,
                                        meetingId: null,
                                        child: Container(
                                          height: 50 / 4,
                                          width: double.infinity,

                                          //grey border
                                          // decoration: BoxDecoration(
                                          //   color: Colors.white,
                                          //   border: Border.all(
                                          //     color: Colors.grey,
                                          //     width: 0.5,
                                          //   ),
                                          // ),
                                          child: Text(
                                            "",
                                            //"${timeFormatter.format(newTime)}",
                                            style: TextStyle(color: Colors.black, fontSize: 10),
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                ListView(
                  controller: _restColumnsController2,
                  physics: activeSelection ? const NeverScrollableScrollPhysics() : const ClampingScrollPhysics(),
                  children: [
                    SizedBox(
                      height: timelines.length * 51,
                      child: Stack(
                        children: [
                          ...List.generate(meetings.length, (x) {
                            Meeting meeting = meetings[x];

                            Widget middleUI = InkWell(
                              onTapDown: (_) {
                                if (meeting.isNewBooking) {
                                  print("${meeting.eventName} tap on middle");
                                  setState(() {
                                    activeMeetingId = meeting.id;
                                    dragType = DRAG_TYPE.MIDDLE;
                                    activeSelection = true;
                                  });
                                }
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 35, top: 2, bottom: 2),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      child: Text(
                                        meeting.eventName,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    ),
                                    SizedBox(height: 1),
                                    Container(
                                      child: Text(
                                        meeting.isNewBooking
                                            ? "Start Time: ${timeFormatter.format(meeting.from)}\nEnd Time: ${timeFormatter.format(meeting.to)}"
                                            : "",
                                        style: TextStyle(fontSize: 10),
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 2,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );

                            Widget child = Stack(
                              clipBehavior: Clip.none,
                              children: [
                                //Middle
                                Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: widget.controller.isTimeAvail.value
                                        ? meeting.background
                                        : meeting.isNewBooking
                                            ? Colors.redAccent
                                            : meeting.background,
                                    borderRadius: BorderRadius.all(Radius.circular(10)),
                                  ),
                                  margin: EdgeInsets.only(top: 5, bottom: 5, right: 1, left: 1),
                                  child: activeMeetingId == null || !meeting.isNewBooking
                                      ? middleUI
                                      : RenderWidget(
                                          enable: activeMeetingId == null,
                                          time: meeting.from,
                                          isMeeting: true,
                                          dragType: DRAG_TYPE.MIDDLE,
                                          child: middleUI,
                                          meetingId: meeting.id,
                                        ),
                                ),

                                //Top
                                if (meeting.isNewBooking)
                                  Positioned(
                                    top: -8,
                                    right: 15,
                                    child: SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: RenderWidget(
                                        enable: activeMeetingId == null || !meeting.isNewBooking,
                                        time: meeting.from,
                                        isMeeting: true,
                                        dragType: DRAG_TYPE.TOP,
                                        child: Listener(
                                          onPointerDown: (_) {
                                            print("${meeting.eventName} tap on top");
                                            setState(() {
                                              activeMeetingId = meeting.id;
                                              dragType = DRAG_TYPE.TOP;
                                              activeSelection = true;
                                            });
                                          },
                                          onPointerUp: (_) {
                                            setState(() {
                                              activeMeetingId = null;
                                              dragType = DRAG_TYPE.TOP;
                                              activeSelection = false;
                                            });
                                          },
                                          child: Container(
                                            width: 25,
                                            height: 25,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                color: meeting.background,
                                                width: 1,
                                              ),
                                              borderRadius: BorderRadius.circular(25),
                                            ),
                                            child: Icon(
                                              Icons.arrow_upward_outlined,
                                              color: Colors.greenAccent,
                                            ),
                                          ),
                                        ),
                                        meetingId: meeting.id,
                                      ),
                                    ),
                                  ),

                                //Bottom
                                if (meeting.isNewBooking)
                                  Positioned(
                                    bottom: -8,
                                    left: 15,
                                    child: SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: RenderWidget(
                                        //index: y,
                                        enable: activeMeetingId == null || !meeting.isNewBooking,
                                        time: meeting.from,
                                        isMeeting: true,
                                        dragType: DRAG_TYPE.BOTTOM,
                                        child: Listener(
                                          onPointerDown: (_) {
                                            print("${meeting.eventName} tap on bottom");
                                            setState(() {
                                              activeMeetingId = meeting.id;
                                              dragType = DRAG_TYPE.BOTTOM;
                                              activeSelection = true;
                                            });
                                          },
                                          onPointerUp: (_) {
                                            setState(() {
                                              activeMeetingId = null;
                                              dragType = DRAG_TYPE.BOTTOM;
                                              activeSelection = false;
                                            });
                                          },
                                          child: Row(
                                            children: [
                                              Container(
                                                width: 25,
                                                height: 25,
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  border: Border.all(
                                                    color: meeting.background,
                                                    width: 1,
                                                  ),
                                                  borderRadius: BorderRadius.circular(25),
                                                ),
                                                child: Icon(
                                                  Icons.arrow_downward,
                                                  color: Colors.greenAccent,
                                                ),
                                              ),
                                              Expanded(child: SizedBox())
                                            ],
                                          ),
                                        ),
                                        meetingId: meeting.id,
                                      ),
                                    ),
                                  )
                              ],
                            );

                            return Positioned(
                              bottom: ((timelines.length * 50) *
                                      ((timelines.length * 60) - meeting.to.difference(timelines.first).inMinutes)) /
                                  (timelines.length * 60),
                              // top: ((timelines.length * 50) *
                              //         meeting.from
                              //             .difference(timelines.first)
                              //             .inMinutes) /
                              //     (timelines.length * 60),

                              top: ((timelines.length * 50) * meeting.from.difference(timelines.first).inMinutes) /
                                  (timelines.length * 60),
                              left: 0,
                              right: 0,
                              child: child,
                            );
                          }),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
