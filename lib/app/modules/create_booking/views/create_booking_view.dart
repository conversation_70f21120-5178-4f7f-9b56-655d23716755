import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/app/modules/create_booking/views/table_body.dart';
import 'package:small_officer/constants/constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:small_officer/main.dart';
import 'package:small_officer/utilities/submit_button.dart';

import '../../../../constants/color_constant.dart';
import '../../../../constants/config.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/create_booking_controller.dart';

class CreateBookingView extends GetWidget<CreateBookingController> {
  Map<String, dynamic>? paymentIntentData;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // title: Text('CreateBookingView'),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back_ios,
            color: appTheme.primaryTheme,
          ),
          // child: Padding(
          //   padding: EdgeInsets.only(
          //       left: MySize.getScaledSizeWidth(15),
          //       top: MySize.size17!,
          //       bottom: MySize.size17!),
          //   child: SvgPicture.asset(
          //     "assets/arrow_back.svg",
          //     width: MySize.getScaledSizeWidth(40),
          //   ),
          // ),
        ),
      ),
      body: Obx(() {
        return Container(
          height: MySize.screenHeight,
          width: MySize.screenWidth,
          padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(15)),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: appTheme.textGrayColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(
                    MySize.size10!,
                  ),
                ),
                padding: EdgeInsets.symmetric(horizontal: MySize.size10!, vertical: MySize.size10!),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ClipRRect(
                      child: CommonNetworkImageView(
                        url: (controller.productDetailModel!.imageUrl != null)
                            ? controller.productDetailModel!.imageUrl.toString()
                            : "",
                        height: MySize.getScaledSizeHeight(80),
                        width: MySize.getScaledSizeHeight(80),
                        fit: BoxFit.fill,
                      ),
                      borderRadius: BorderRadius.circular(MySize.size4!),
                    ),
                    SizedBox(
                      width: MySize.getScaledSizeWidth(10),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            "${controller.productDetailModel!.name} (\$${controller.productDetailModel!.ratePerHour} hourly)",
                            style: TextStyle(
                              color: appTheme.primaryTheme,
                              fontSize: MySize.size16!,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            controller.productDetailModel!.description.toString(),
                            maxLines: 3,
                            style: TextStyle(
                                color: appTheme.primaryTheme,
                                fontSize: MySize.size14!,
                                overflow: TextOverflow.ellipsis),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: MySize.size20!,
              ),
              (controller.productDetailModel!.plans != null && controller.productDetailModel!.plans!.isNotEmpty)
                  ? Column(
                      children: [
                        Center(
                          child: Text(
                            "Plans",
                            style: TextStyle(
                              color: appTheme.primaryTheme,
                              fontSize: MySize.size20!,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Space.height(30),
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(MySize.size18!),
                          decoration: BoxDecoration(
                              border: Border.all(color: appTheme.primaryTheme),
                              borderRadius: BorderRadius.circular(15)),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                controller.productDetailModel!.plans!.first.name.toString(),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: MySize.size16!,
                                ),
                              ),
                              SizedBox(
                                height: MySize.size10,
                              ),
                              Text(
                                controller.productDetailModel!.plans!.first.description.toString(),
                                style: TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: MySize.size15!,
                                ),
                              ),
                              SizedBox(
                                height: MySize.size10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        "Durations : ",
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: MySize.size15!,
                                        ),
                                      ),
                                      Text(
                                        controller.productDetailModel!.plans!.first.duartion.toString(),
                                        style: TextStyle(
                                          fontWeight: FontWeight.normal,
                                          fontSize: MySize.size15!,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        "Price : ",
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: MySize.size15!,
                                        ),
                                      ),
                                      Text(
                                        "${controller.productDetailModel!.plans!.first.price} \$",
                                        style: TextStyle(
                                          fontWeight: FontWeight.normal,
                                          fontSize: MySize.size15!,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                        Space.height(30),
                        InkWell(
                          onTap: () async {
                            DateTime? newSelectedDate = await showDatePicker(
                                context: context,
                                initialDate: DateTime.now(),
                                firstDate: DateTime.now(),
                                lastDate: DateTime(6000),
                                builder: (BuildContext context, Widget? child) {
                                  return Theme(
                                    data: ThemeData.dark(),
                                    child: child!,
                                  );
                                });

                            if (newSelectedDate != null) {
                              if (controller.productDetailModel!.bookingList != null &&
                                  controller.productDetailModel!.bookingList!.isNotEmpty) {
                                bool base = false;
                                for (var element in controller.productDetailModel!.bookingList!) {
                                  if (newSelectedDate.isAfter(getDateFromString(element.startAt.toString())) &&
                                      newSelectedDate.isBefore(getDateFromString(element.endAt.toString()))) {
                                    base = true;
                                  }
                                }
                                if (base) {
                                  Get.snackbar("Select Another Slot", "Selected slot is already block.");
                                } else {
                                  controller.startPlanDate.value = newSelectedDate;
                                }
                              } else {
                                controller.startPlanDate.value = newSelectedDate;
                              }
                            }
                          },
                          child: button(
                            title: "Select Booking Start Date",
                          ),
                        ),
                        Space.height(20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Plan Start Date  :",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: MySize.size15!,
                              ),
                            ),
                            Text(
                              "  ${DateFormat("yyyy-MM-dd").format(controller.startPlanDate.value)}",
                              style: TextStyle(
                                fontWeight: FontWeight.normal,
                                fontSize: MySize.size15!,
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Expanded(
                      child: Column(
                        children: [
                          Align(
                            alignment: Alignment.centerRight,
                            child: InkWell(
                              onTap: () async {
                                DateTime? newSelectedDate = await showDatePicker(
                                  context: context,
                                  initialDate: DateTime.now(),
                                  firstDate: DateTime.now(),
                                  lastDate: DateTime(6000),
                                  builder: (BuildContext context, Widget? child) {
                                    return Theme(
                                      data: ThemeData.dark(),
                                      child: child!,
                                    );
                                  },
                                );

                                if (newSelectedDate != null) {
                                  for (var element in controller.dateList) {
                                    element.isActive = false;
                                  }
                                  controller.selectedDate.value = newSelectedDate;
                                  controller.dateNew.value = newSelectedDate;

                                  controller.callApiToGetBookedSlot(newSelectedDate);
                                }
                              },
                              child: Text(
                                "Select Other Date",
                                style: TextStyle(
                                  color: appTheme.primaryTheme,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: MySize.size20!),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              for (int i = 0; i < controller.dateList.length; i++)
                                InkWell(
                                  onTap: () {
                                    if (controller.dateList[i].enabled == true) {
                                      for (var element in controller.dateList) {
                                        element.isActive = false;
                                      }
                                      controller.dateNew.value = controller.dateList[i].dateTime!;

                                      controller.selectedDate.value = controller.dateList[i].dateTime!;

                                      controller.callApiToGetBookedSlot(controller.dateList[i].dateTime!);
                                      controller.dateList[i].isActive = true;
                                    }
                                  },
                                  child: Container(
                                    height: MySize.size75,
                                    width: MySize.getScaledSizeWidth(50),
                                    decoration: BoxDecoration(
                                      color: (controller.dateList[i].enabled == true)
                                          ? (controller.dateList[i].isActive == true)
                                              ? appTheme.newPrimaryColor
                                              : Colors.white
                                          : Colors.grey.withOpacity(0.8),
                                      borderRadius: BorderRadius.circular(MySize.size14!),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          controller.dateList.value[i].weekDay.toString()[0],
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontSize: MySize.size12,
                                            color: (controller.dateList[i].isActive == true ||
                                                    controller.dateList[i].enabled == false)
                                                ? Colors.white
                                                : appTheme.primaryTheme,
                                          ),
                                        ),
                                        SizedBox(
                                          height: MySize.size6,
                                        ),
                                        Text(
                                          controller.dateList.value[i].date.toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: MySize.size20,
                                            color: (controller.dateList[i].isActive == true ||
                                                    controller.dateList[i].enabled == false)
                                                ? Colors.white
                                                : appTheme.primaryTheme,
                                          ),
                                        ),
                                        SizedBox(height: MySize.size6),
                                        Text(
                                          controller.dateList.value[i].month.toString(),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontSize: MySize.size12,
                                            color: (controller.dateList[i].isActive == true ||
                                                    controller.dateList[i].enabled == false)
                                                ? Colors.white
                                                : appTheme.primaryTheme,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          SizedBox(height: MySize.size10),
                          Expanded(
                            child: TableBody(controller),
                          ),
                        ],
                      ),
                    ),
              if ((controller.productDetailModel!.plans != null && controller.productDetailModel!.plans!.isNotEmpty))
                Expanded(child: Container()),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.getScaledSizeWidth(10),
                  vertical: MySize.size20!,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        /*showModalBottomSheet<void>(
                            context: context,
                            builder: (BuildContext context) {
                              return SizedBox(
                                height: 200,
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: <Widget>[

                                    ],
                                  ),
                                ),
                              );
                            });*/

                        if (controller.isTimeAvail.value) {
                          // PaymentSIntegration.stripePayment(
                          //     context: context,
                          //     paymentRequestModel: PaymentRequestModel(
                          //         currencyCode: "USD",
                          //         amount: 5,
                          //         countryCode: "US",
                          //         label: "test",
                          //         merchantDisplayName: "yess"));
                          makePayment(context: context);
                        } else {
                          Get.snackbar("Error", "Timeslot not available");
                        }
                      },
                      child: button(
                        title: (freeBookingTime() == '0') ? "Book Now" : "First ${freeBookingTime()} free",
                        fontsize: 16,
                        backgroundColor:
                            controller.isTimeAvail.value ? Color(0xffCA0222) : Colors.grey.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      }),
    );
  }

  String freeBookingTime() {
    double totalBookingHours = double.parse(box.read(Constant.totalBookingHours).toString());
    if (totalBookingHours < 0) {
      int remainingMinutes = ((0 - totalBookingHours) * 60).toInt();

      // Calculate the number of 15-minute intervals
      int intervals = (remainingMinutes / 15).ceil();

      // Calculate the new rounded-off remaining minutes
      int roundedRemainingMinutes = intervals * 15;

      int hours = roundedRemainingMinutes ~/ 60;
      int minutes = roundedRemainingMinutes % 60;

      return '$hours hours and $minutes minutes';
    } else {
      return '0';
    }
  }

  Future<void> makePayment({required BuildContext context}) async {
    double finalAmount = 0.0;
    if (controller.productDetailModel!.plans != null && controller.productDetailModel!.plans!.isNotEmpty) {
      int n = controller
          .getLastDateOfMonth(nowD: controller.startPlanDate.value)
          .difference(controller.startPlanDate.value)
          .inDays;
      int noOfDays = DateTime(controller.startPlanDate.value.year, controller.startPlanDate.value.month + 1, 0).day;
      num totalPerDay = controller.productDetailModel!.plans!.first.price! / noOfDays;

      finalAmount = totalPerDay * (n + 1);
      controller.amount = finalAmount;
    } else {
      if (controller.productDetailModel!.plans != null && controller.productDetailModel!.plans!.isNotEmpty) {
        // Code for plans
        // ...
      } else {
        // Code for hourly-based pricing
        int totalMinutes = controller.endTime.difference(controller.startTime).inMinutes;
        double n = 0.0;

        if (box.read(Constant.totalBookingHours) != null && box.read(Constant.totalBookingHours) < 0) {
          n = 0 - double.parse(box.read(Constant.totalBookingHours).toString());
        }

        // Calculate the total hours and remaining minutes separately
        int totalHours = totalMinutes ~/ 60;
        int remainingMinutes = totalMinutes % 60;

        // Calculate the total minutes in 15-minute intervals
        int total15MinMinutesCounts = (totalHours * 4) + (remainingMinutes / 15).ceil();

        if (total15MinMinutesCounts <= n * 4) {
          finalAmount = 0.0;
        } else {
          double ratePer15Minutes = controller.productDetailModel!.ratePerHour! / 4.0;
          finalAmount = (total15MinMinutesCounts - n * 4) * ratePer15Minutes;
        }
        log("FINAL AMOUNT :: $finalAmount");
        controller.amount = finalAmount;
      }
    }

    print(finalAmount);

    if (finalAmount == 0.0) {
      controller.callApiForBookSlot(context: context);
    } else {
      try {
        String calculatedAmount = calculateAmount(finalAmount.toString());
        paymentIntentData = await createPaymentIntent(calculatedAmount, 'USD', finalAmount.toString());
        print("Payment Intent Data: $paymentIntentData"); // Debugging
        controller.paymentIntentData = paymentIntentData;

        // New integration
        var googlePay = PaymentSheetGooglePay(
          merchantCountryCode: "US",
          currencyCode: "USD",
          // testEnv: false,
          testEnv: true,
        );
        //"GB
        //"Test Product" "100.00"
        var applePay = PaymentSheetApplePay(
            merchantCountryCode: "US",
            cartItems: [ApplePayCartSummaryItem.immediate(label: "Small Officer", amount: finalAmount.toString())]);
        var appearance = const PaymentSheetAppearance(
          colors: PaymentSheetAppearanceColors(),
        );

        await Stripe.instance
            .initPaymentSheet(
              paymentSheetParameters: SetupPaymentSheetParameters(
                paymentIntentClientSecret: paymentIntentData!['client_secret'],
                applePay: applePay,
                googlePay: googlePay,
                // testEnv: true,
                style: ThemeMode.dark,

                // merchantCountryCode: 'US',
                merchantDisplayName: 'Small Officer',
              ),
            )
            .then((value) {});

        ///now finally display payment sheeet
        displayPaymentSheet(context: context);
      } catch (e, s) {
        print('exception:$e$s');
      }
    }
  }

  displayPaymentSheet({
    required BuildContext context,
  }) async {
    try {
      await Stripe.instance
          .presentPaymentSheet(
              // parameters: PresentPaymentSheetParameters(
              // clientSecret: paymentIntentData!['client_secret'],
              // confirmPayment: true,
              // )
              )
          .then((newValue) {
        // print('payment intent' + paymentIntentData!['id'].toString());
        // print(
        //     'payment intent' + paymentIntentData!['client_secret'].toString());
        // print('payment intent' + paymentIntentData!['amount'].toString());
        // print('payment intent' + paymentIntentData.toString());
        //orderPlaceApi(paymentIntentData!['id'].toString());
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("paid successfully")));
        // controller.callSelectUserPlanApi(context: context, data: data);
        controller.callApiForBookSlot(context: context);
        print(paymentIntentData);
        paymentIntentData = null;
      }).onError((error, stackTrace) {
        print('Exception/DISPLAYPAYMENTSHEET==> $error $stackTrace');
      });
    } on StripeException catch (e) {
      print('Exception/DISPLAYPAYMENTSHEET==> $e');
      showDialog(
          context: context,
          builder: (_) => AlertDialog(
                content: Text("Cancelled "),
              ));
    } catch (e) {
      print('$e');
    }
  }

  //  Future<Map<String, dynamic>>
  createPaymentIntent(String amount, String currency, String a) async {
    try {
      Map<String, dynamic> body = {
        'amount': calculateAmount(a),
        'currency': currency,
        'payment_method_types[]': 'card'
      };
      print(body);
      var response = await http.post(Uri.parse('https://api.stripe.com/v1/payment_intents'), body: body, headers: {
        "Authorization": "Bearer ${CONFIG().secreteKey}",
        'Content-Type': 'application/x-www-form-urlencoded'
      });
      print('Create Intent reponse ===> ${response.body.toString()}');
      return jsonDecode(response.body);
    } catch (err) {
      print('err charging user: ${err.toString()}');
    }
  }

  // calculateAmount(String amount) {
  //   final a = (double.parse(amount)) * 100;
  //   return a.toString();
  // }

  String calculateAmount(String amount) {
    // final double amountDouble = double.parse(amount);
    final a = (double.parse(amount) * 100).round();
    return a.toString();
  }

  String formatTimeOfDay(TimeOfDay tod) {
    final now = DateTime.now();
    final dt = DateTime(now.year, now.month, now.day, tod.hour, tod.minute);
    final format = DateFormat.jm(); //"6:00 AM"
    return format.format(dt);
  }
}

class DateModel {
  String? name;
  Duration? time;

  DateModel({this.name, this.time});
}

const String defaultGooglePay = '''{
  "provider": "google_pay",
  "data": {
    "environment": "TEST",
    "apiVersion": 2,
    "apiVersionMinor": 0,
    "allowedPaymentMethods": [
      {
        "type": "CARD",
        "tokenizationSpecification": {
          "type": "PAYMENT_GATEWAY",
          "parameters": {
            "gateway": "example",
            "gatewayMerchantId": "gatewayMerchantId"
          }
        },
        "parameters": {
          "allowedCardNetworks": ["VISA", "MASTERCARD"],
          "allowedAuthMethods": ["PAN_ONLY", "CRYPTOGRAM_3DS"],
          "billingAddressRequired": true,
          "billingAddressParameters": {
            "format": "FULL",
            "phoneNumberRequired": true
          }
        }
      }
    ],
    "merchantInfo": {
      "merchantId": "01234567890123456789",
      "merchantName": "Example Merchant Name"
    },
    "transactionInfo": {
      "countryCode": "US",
      "currencyCode": "USD"
    }
  }
}''';

const String defaultApplePay = '''{
  "provider": "apple_pay",
  "data": {
    "merchantIdentifier": "merchant.com.sams.fish",
    "displayName": "Sam's Fish",
    "merchantCapabilities": ["3DS", "debit", "credit"],
    "supportedNetworks": ["amex", "visa", "discover", "masterCard"],
    "countryCode": "US",
    "currencyCode": "USD",
    "requiredBillingContactFields": ["emailAddress", "name", "phoneNumber", "postalAddress"],
    "requiredShippingContactFields": [],
    "shippingMethods": [
      {
        "amount": "0.00",
        "detail": "Available within an hour",
        "identifier": "in_store_pickup",
        "label": "In-Store Pickup"
      },
      {
        "amount": "4.99",
        "detail": "5-8 Business Days",
        "identifier": "flat_rate_shipping_id_2",
        "label": "UPS Ground"
      },
      {
        "amount": "29.99",
        "detail": "1-3 Business Days",
        "identifier": "flat_rate_shipping_id_1",
        "label": "FedEx Priority Mail"
      }
    ]
  }
}''';
