import 'package:flutter/material.dart';

class MultiplicationTableCell extends StatelessWidget {
  final Color color;
  final Widget child;
  final double cellWidth;
  final double cellHeight;

  MultiplicationTableCell(
      {required this.child,
      required this.color,
      required this.cellWidth,
      this.cellHeight = 50});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: cellWidth,
      height: cellHeight,
      decoration: BoxDecoration(color: color),
      alignment: Alignment.topCenter,
      child: child,
    );
  }
}
