import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;

import '../../../../constants/config.dart';

class PaymentSIntegration {
  // static setUpStripeConfiguration() {
  //   Stripe.publishableKey = "";
  //   Stripe.merchantIdentifier = "";
  //   Stripe.instance.applySettings();
  // }

  static Future<bool> isPlatformPaySupported() async {
    return await Stripe.instance.isPlatformPaySupported();
  }

  static Future<void> paypalPayment({required BuildContext context}) async {
    // PageNavigator.pushToPageName(context: context, routeName: PaypalPaymentScreen.routeName);
  }

  static Future<String> getClientSecret(
      {required BuildContext context, required double amount, required String currencyCode}) async {
    Map<String, dynamic>? paymentIntent;
    try {
      //"1000","INR"
      Map<String, dynamic> body = {"amount": (amount * 100).toStringAsFixed(0), "currency": currencyCode.toUpperCase()};
      var response = await http.post(Uri.parse("https://api.stripe.com/v1/payment_intents"),
          headers: {
            "Authorization": "Bearer ${CONFIG().secreteKey}",
            "Content-type": "application/x-www-form-urlencoded",
          },
          body: body);
      paymentIntent = json.decode(response.body);
    } catch (e) {
      throw Exception(e);
    }
    if (paymentIntent != null) {
      // showLogs(message: "INTENT ID : ${paymentIntent["id"]}");
      if (context.mounted) {
        ///SET STRIPE INTENT PAYMENT KEY TO BACKEND
        // await BlocProvider.of<ValuationProcessCubit>(context)
        //     .setStripeReference(context: context, paymentIntentId: paymentIntent["id"]);
      }
      return paymentIntent["client_secret"];
    } else {
      return "";
    }
  }

  static Future<void> stripePayment(
      {required BuildContext context, required PaymentRequestModel paymentRequestModel}) async {
    print("PAYMENT REQUEST MODEL : ${paymentRequestModel.amount}");
    print("PAYMENT REQUEST MODEL : ${paymentRequestModel.label}");
    print("PAYMENT REQUEST MODEL : ${paymentRequestModel.currencyCode}");
    print("PAYMENT REQUEST MODEL : ${paymentRequestModel.countryCode}");
    print("PAYMENT REQUEST MODEL : ${paymentRequestModel.merchantDisplayName}");
    //"GB","GBP"
    var googlePay = PaymentSheetGooglePay(
      merchantCountryCode: paymentRequestModel.countryCode,
      currencyCode: paymentRequestModel.currencyCode,
      // testEnv: false, // #TODO
      testEnv: true,
    );
    //"GB
    //"Test Product" "100.00"
    var applePay = PaymentSheetApplePay(merchantCountryCode: paymentRequestModel.countryCode, cartItems: [
      ApplePayCartSummaryItem.immediate(
          label: paymentRequestModel.label, amount: (paymentRequestModel.amount).toStringAsFixed(2))
    ]);
    var appearance = const PaymentSheetAppearance(
      colors: PaymentSheetAppearanceColors(),
    );

    try {
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: await getClientSecret(
            context: context,
            currencyCode: paymentRequestModel.currencyCode,
            amount: (paymentRequestModel.amount),
          ),
          style: ThemeMode.system,
          merchantDisplayName: paymentRequestModel.merchantDisplayName,
          googlePay: googlePay,
          appearance: appearance,
          applePay: applePay,
        ),
      );
      await Stripe.instance.presentPaymentSheet().then((value) {
        // PageNavigator.pushToPageName(context: context, routeName: OrderConfirmScreen.routeName);
        // PageNavigator.goToPageName(
        //     context: context, routeName: AppRouteKeys.thankYouRouterName, paramsValue: AppConstant.orderSuccess);
      });
    } catch (e) {
      print("sdasdada    $e");
      throw Exception(e);
    }
  }

  static Future<void> googlePayPayment(
      {required BuildContext context, required PaymentRequestModel paymentRequestModel}) async {
    final googlePaySupported = await Stripe.instance.isPlatformPaySupported(
      googlePay: const IsGooglePaySupportedParams(
        // testEnv: false, // #TODO
        testEnv: true,
      ),
    );
    if (googlePaySupported) {
      try {
        if (context.mounted) {
          await Stripe.instance
              .confirmPlatformPayPaymentIntent(
                  clientSecret: await getClientSecret(
                    context: context,
                    amount: paymentRequestModel.amount,
                    currencyCode: paymentRequestModel.currencyCode,
                  ),
                  confirmParams: PlatformPayConfirmParams.googlePay(
                      googlePay: GooglePayParams(
                          // amount: (paymentRequestModel.amount).toInt(),
                          // testEnv: false, // #TODO
                          testEnv: true,
                          merchantName: paymentRequestModel.merchantDisplayName,
                          merchantCountryCode: paymentRequestModel.countryCode,
                          currencyCode: paymentRequestModel.currencyCode,
                          allowCreditCards: true,
                          isEmailRequired: false)))
              .then((v) {
            print("GOOGLE PAY RESPONSE : ${v.toJson()}");
            // showMessage(
            //     context: context,
            //     message: 'Google Pay payment successfully completed ${v.toJson()}');
            // PageNavigator.pushToPageName(context: context, routeName: OrderConfirmScreen.routeName);
            // PageNavigator.goToPageName(
            //     context: context, routeName: AppRouteKeys.thankYouRouterName, paramsValue: AppConstant.orderSuccess);
          });
        }
      } catch (e) {
        if (e is StripeException) {
          if (context.mounted) {
            // showMessage(context: context, message: 'Error: ${e.error}');
          }
        } else {
          if (context.mounted) {
            // showMessage(context: context, message: 'Error: $e');
          }
        }
      }
    } else {
      if (context.mounted) {
        // showMessage(
        //     context: context,
        //     message: "This device is not supported Google Pay",
        //     isErrorMessage: true);
      }
    }
  }

  static Future<void> applePayPayment(
      {required BuildContext context, required PaymentRequestModel paymentRequestModel}) async {
    final applePaySupported = await Stripe.instance.isPlatformPaySupported();
    if (applePaySupported) {
      try {
        if (context.mounted) {
          await Stripe.instance
              .confirmPlatformPayPaymentIntent(
                  clientSecret: await getClientSecret(
                      context: context,
                      currencyCode: paymentRequestModel.currencyCode,
                      amount: paymentRequestModel.amount),
                  confirmParams: PlatformPayConfirmParams.applePay(
                    applePay: ApplePayParams(
                        merchantCountryCode: paymentRequestModel.countryCode,
                        currencyCode: paymentRequestModel.currencyCode,
                        cartItems: [
                          ApplePayCartSummaryItem.immediate(
                            label: paymentRequestModel.label,
                            amount: (paymentRequestModel.amount).toStringAsFixed(2),
                          ),
                        ]),
                  ))
              .then((v) {
            print("APPLE PAY RESPONSE : ${v.toJson()}");
            // showMessage(
            //     context: context,
            //     message: 'Apple Pay payment successfully completed ${v.toJson()}');
            // PageNavigator.pushToPageName(context: context, routeName: OrderConfirmScreen.routeName);
            // PageNavigator.goToPageName(
            //     context: context, routeName: AppRouteKeys.thankYouRouterName, paramsValue: AppConstant.orderSuccess);
          });
        }
      } catch (e) {
        if (e is StripeException) {
          if (context.mounted) {
            // showMessage(context: context, message: 'Error: ${e.error}', isErrorMessage: true);
          }
        } else {
          if (context.mounted) {
            // showMessage(context: context, message: 'Error: $e', isErrorMessage: true);
          }
        }
      }
    } else {
      if (context.mounted) {
        // showMessage(
        //     context: context,
        //     message: "This device is not supported Apple Pay",
        //     isErrorMessage: true);
      }
    }
  }
}

class PaymentRequestModel {
  final double amount;
  final String label;
  final String currencyCode;
  final String countryCode;
  final String merchantDisplayName;

  PaymentRequestModel(
      {required this.amount,
      required this.currencyCode,
      required this.label,
      required this.countryCode,
      required this.merchantDisplayName});
}
