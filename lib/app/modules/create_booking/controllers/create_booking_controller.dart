import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/Models/booking_data_model.dart';
import 'package:small_officer/constants/constant.dart';
import 'package:small_officer/utilities/date_utilities.dart';
import 'package:time_range_picker/time_range_picker.dart';

import '../../../../Models/resourceDataModel.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../../../../utilities/utilities.dart';
import '../../home_screen/controllers/home_screen_controller.dart';
import '../../my_booking/controllers/my_booking_controller.dart';

class CreateBookingController extends GetxController {
  Rx<TimeRange>? result = TimeRange(
    startTime: TimeOfDay(minute: 0, hour: 0),
    endTime: TimeOfDay(minute: 0, hour: 0),
  ).obs;

  DateTime startTime = DateTime.now();
  DateTime endTime = DateTime.now();

  RxBool tSelected = false.obs;
  final count = 0.obs;
  num amount = 0;
  RxList<DateModel> dateList = RxList();

  Rx<DateTime> selectedDate = DateTime.now().obs;

  Rx<DateTime> dateNew = DateTime.now().obs;
  Rx<DateTime> dummySelectedTime = DateTime.now().obs;
  Rx<DateTime> startPlanDate = DateTime.now().obs;
  RowsData? productDetailModel;
  GetStorage box = GetStorage();
  Map<String, dynamic>? paymentIntentData;
  HomeScreenController? homeScreenController;
  Rx<Duration> selectedDuration = Duration(hours: 1).obs;

  RxBool isTimeAvail = true.obs;

  final RxList<Meeting> meetingData = RxList<Meeting>([]);
  List<DateTime> timelines = RxList<DateTime>([]);

  @override
  void onInit() {
    Get.lazyPut(() => HomeScreenController());
    homeScreenController = Get.find<HomeScreenController>();

    super.onInit();

    if (Get.arguments != null) {
      productDetailModel = Get.arguments[Constant.productDetailModel];
    }

    /*DateFormat formatter = DateFormat('MMM');
    DateFormat dateformatter = DateFormat('EEEE');

    // Weed date time
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final now = DateTime.now().firstDayOfWeek;
      for (int i = 0; i < 7; i++) {
        final duration = Duration(days: i);

        final weekDay = DateFormat('EEEE').format(now.add(duration));
        WeekDayTime? weekDayTime = productDetailModel!.weekDayTimes
            ?.firstWhere((element) => element.weekDay == weekDay);

        dateList.add(
          DateModel(
            dateTime: now.add(duration),
            date: now.add(duration).day,
            month: formatter.format(now.add(duration)),
            weekDay: dateformatter.format(now.add(duration)),
            isActive:
                now.add(duration).isSameDate(DateTime.now()) ? true : false,
            enabled: weekDayTime?.isOpen ?? true,
          ),
        );
      }
    });*/

    resetWeekDateList();
    refreshDataList();
  }

  void resetWeekDateList() {
    dateList.clear();

    DateFormat formatter = DateFormat('MMM');
    DateFormat dateformatter = DateFormat('EEEE');

    final today = selectedDate.value; // DateTime.now();
    final firstDayOfWeek = today.firstDayOfWeek;

    for (int i = 0; i < 7; i++) {
      final duration = Duration(days: i);
      final dateTime = firstDayOfWeek.add(duration);

      WeekDayTime? weekDayTime = productDetailModel!.weekDayTimes
          ?.firstWhereOrNull(
              (element) => element.weekDay == dateformatter.format(dateTime));

      dateList.add(
        DateModel(
          dateTime: dateTime,
          date: dateTime.day,
          month: formatter.format(dateTime),
          weekDay: dateformatter.format(dateTime),
          isActive: dateTime.isSameDate(today),
          enabled: weekDayTime?.isOpen ?? true,
        ),
      );
    }
  }

  void resetTimeline() {
    timelines.clear();

    DateTime today = DateTime(
      selectedDate.value.year,
      selectedDate.value.month,
      selectedDate.value.day,
    );

    List.generate(24, (index) {
      today = today.add(Duration(hours: 1));
      timelines.add(today);
    });
  }

  void refreshDataList() {
    resetTimeline();
    getDataSource();
  }

  checkIsTimeAvailForNewBooking() {
    isTimeAvail.value = true;

    Meeting? meeting =
        meetingData.firstWhereOrNull((element) => element.isNewBooking);

    for (var m in meetingData) {
      if (isTimeAvail.value &&
          !m.isNewBooking &&
          (meeting?.from.difference(m.from).inMinutes == 0 &&
                  meeting?.to.difference(m.to).inMinutes == 0 ||
              ((meeting!.from.isAfter(m.from) && meeting.from.isBefore(m.to)) ||
                  (meeting.to.isAfter(m.from) && meeting.to.isBefore(m.to))))) {
        isTimeAvail.value = false;
      }
    }
  }

  callApiForBookSlot({
    required BuildContext context,
  }) {

    print("-----------------callApiForBookSlot");


    Meeting? meeting =
        meetingData.firstWhereOrNull((element) => element.isNewBooking);

    FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();
    dict["UserId"] = box.read(Constant.userId);
    dict["ResourceId"] = productDetailModel?.id;

    final startAt = DateTime(dateNew.value.year, dateNew.value.month,
            dateNew.value.day, meeting!.from.hour, meeting.from.minute)
        .toUtc()
        .toIso8601String();

    final endAt = DateTime(dateNew.value.year, dateNew.value.month,
            dateNew.value.day, meeting.to.hour, meeting.to.minute)
        .toUtc()
        .toIso8601String();

    dict["startAt"] = startAt;
    dict["endAt"] = endAt;

    if (productDetailModel!.plans != null &&
        productDetailModel!.plans!.isNotEmpty) {
      dict["plan"] = true;

      dict["startAt"] = DateTime(startPlanDate.value.year,
              startPlanDate.value.month, startPlanDate.value.day, 00, 00, 00)
          .toUtc()
          .toIso8601String();

      dict["endAt"] = DateTime(
              getLastDateOfMonth(nowD: startPlanDate.value).year,
              getLastDateOfMonth(nowD: startPlanDate.value).month,
              getLastDateOfMonth(nowD: startPlanDate.value).day,
              23,
              59,
              59)
          .toUtc()
          .toIso8601String();
    }
    dict["amount"] = amount;
    if (amount == 0) {
      dict["paymentIntent"] = null;
    } else {
      dict["paymentIntent"] = paymentIntentData!["id"];
    }

    print("----------11-------$dict");
    print("----------11-------$baseURL${ApiConstant.bookingList}");

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.bookingList,
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {

        debugPrint("----------11-------${baseURL}");
        debugPrint("----------11-------${response}");

        app.resolve<CustomDialogs>().hideCircularDialog(context);
        // update all resources & update totalBookingHours and move back to two step
        homeScreenController!.callApiForGetProfile(context: context);
        Get.find<AllResourceController>()
            .callApiForGetResourceData(context: context);
        Get.close(2);
        Get.snackbar("Success", "Your slot is successfully booked.");
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app
            .resolve<CustomDialogs>()
            .getDialog(title: "Failed", desc: status["message"]);

        print(" error");
      },
    );
  }

  DateTime dayStart(DateTime other) {
    return DateTime(other.year, other.month, other.day);
  }

  DateTime dayEnd(DateTime other) {
    return DateTime(other.year, other.month, other.day, 23, 59);
  }

  DateTime getLastDateOfMonth({DateTime? nowD}) {
    var now = (nowD != null) ? nowD : DateTime.now();

    // Find the last day of the month.
    var beginningNextMonth = (now.month < 12)
        ? DateTime(now.year, now.month + 1, 1)
        : DateTime(now.year + 1, 1, 1);
    DateTime lastDay = beginningNextMonth.subtract(Duration(days: 1));
    return lastDay;
  }

  getDataSource() {
    meetingData.clear();

    // Booked slot
    for (var element in productDetailModel!.bookingList!) {
      if (selectedDate.value
          .isSameDate(getDateFromString(element.startAt.toString()))) {
        DateTime from = parseDatetimeFromUtc(
            isoFormattedString: element.startAt.toString());
        DateTime to =
            parseDatetimeFromUtc(isoFormattedString: element.endAt.toString());

        meetingData.add(
          Meeting(
            id: "${element.id}",
            eventName: '',
            from: from,
            to: to,
            background: Color(0xFFF4F4F4),
          ),
        );
      }
    }

    final weekDay = DateFormat('EEEE').format(selectedDate.value);
    WeekDayTime? weekDayTime = productDetailModel!.weekDayTimes
        ?.firstWhereOrNull((element) => element.weekDay == weekDay);
    DateTime openTime = DateTime.now();

    if (weekDayTime != null) {
      DateTime today = DateTime(
        selectedDate.value.year,
        selectedDate.value.month,
        selectedDate.value.day,
      );

      // Book full day & remove new booking slot
      print(weekDayTime.isOpen);
      if (weekDayTime.isOpen == false) {
        print(today);
        meetingData.add(
          Meeting(
            id: "9999999999",
            eventName: "",
            from: today,
            to: today.add(const Duration(hours: 25)),
            background: Color(0xFFF4F4F4),
            isNewBooking: false,
          ),
        );

        return;
      }

      openTime = DateTime(
        today.year,
        today.month,
        today.day,
        int.parse(weekDayTime.openAt!.split(":")[0]),
        int.parse(weekDayTime.openAt!.split(":")[1]),
        0,
      );

      DateTime closeTime = DateTime(
        today.year,
        today.month,
        today.day,
        int.parse(weekDayTime.closeAt!.split(":")[0]),
        int.parse(weekDayTime.closeAt!.split(":")[1]),
        0,
      );

      meetingData.add(
        Meeting(
          id: "9999999999",
          eventName: "",
          from: today,
          to: openTime,
          background: Color(0xFFF4F4F4),
          isNewBooking: false,
        ),
      );

      print(closeTime);
      print(today.add(const Duration(hours: 25)));

      meetingData.add(
        Meeting(
          id: "9999999999",
          eventName: "",
          from: closeTime,
          to: today.add(const Duration(hours: 25)),
          background: Color(0xFFF4F4F4),
          isNewBooking: false,
        ),
      );
    }

    if (selectedDate.value.isSameDate(DateTime.now())) {
      meetingData.add(
        Meeting(
          id: "9999999999",
          eventName: "",
          from: openTime,
          to: DateTime.now(),
          background: Color(0xFFF4F4F4),
          isNewBooking: false,
        ),
      );

      meetingData.add(
        Meeting(
          id: "0",
          eventName: "New Booking",
          from: DateTime.now().roundDown(),
          to: DateTime.now().roundDown().add(Duration(hours: 1)),
          background: Colors.greenAccent,
          isNewBooking: true,
        ),
      );
    } else {
      DateTime dateTime = DateTime(
        selectedDate.value.year,
        selectedDate.value.month,
        selectedDate.value.day,
        openTime.hour,
        0,
      );

      meetingData.add(
        Meeting(
          id: "0",
          eventName: "New Booking",
          from: dateTime,
          to: dateTime.add(Duration(hours: 1)),
          background: Colors.greenAccent,
          isNewBooking: true,
        ),
      );
    }

    update();
  }

  callApiToGetBookedSlot(DateTime dateTime) async {
    app.resolve<CustomDialogs>().showCircularDialog(Get.context!);

    final startTime =
        DateTime(dateTime.year, dateTime.month, dateTime.day).toIso8601String();
    final endTime =
        DateTime(dateTime.year, dateTime.month, dateTime.day, 23, 59, 59)
            .toIso8601String();

    NetworkClient.getInstance.callApi(
      Get.context!,
      baseURL,
      '${ApiConstant.bookingList}/forUser?from=$startTime&to=$endTime&ResourceId=${productDetailModel?.id}',
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(Get.context!);

        final bookingDataModel = BookingDataModel.fromJson(response);
        productDetailModel?.bookingList = bookingDataModel.data;

        resetWeekDateList();
        dateList.refresh();
        update();
        refreshDataList();
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(Get.context!);

        app.resolve<CustomDialogs>().getDialog(
              title: "Failed",
              desc: status["message"],
            );
      },
    );
  }
}

class Meeting {
  Meeting({
    required this.id,
    required this.eventName,
    required this.from,
    required this.to,
    required this.background,
    this.isNewBooking = false,
  });

  String id;
  String eventName;
  DateTime from;
  DateTime to;
  Color background;
  bool isNewBooking;
}

extension DateTimeExtension on DateTime {
  DateTime roundDown({Duration delta = const Duration(minutes: 15)}) {
    return DateTime.fromMillisecondsSinceEpoch(
        millisecondsSinceEpoch - millisecondsSinceEpoch % delta.inMilliseconds);
  }

  /// Return first day of week, if current date is sunday then return same
  DateTime get firstDayOfWeek {
    final weekday = this.weekday;
    if (weekday == 7) return this;

    final firstDayOfWeek = subtract(Duration(days: weekday));

    return firstDayOfWeek;
  }
}
