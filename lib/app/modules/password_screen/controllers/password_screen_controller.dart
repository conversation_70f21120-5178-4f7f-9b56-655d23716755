import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:small_officer/constants/constant.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../../../routes/app_pages.dart';
import '../../login_screen/controllers/login_screen_controller.dart';

class PasswordScreenController extends GetxController with CodeAutoFill {
  //TODO: Implement PasswordScreenController
  //Rx<TextEditingController> emailController = TextEditingController().obs;
  Rx<TextEditingController> otpController = TextEditingController().obs;

  final count = 0.obs;
  int? resendToken;
  String? verificationId;
  String? mobileNumber;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      resendToken = Get.arguments[StringConstant.resendToken];
      verificationId = Get.arguments[StringConstant.verificationId];
      mobileNumber = Get.arguments[StringConstant.mobileNumber];
    }
    listenForCode();
  }

  @override
  void codeUpdated() {
    otpController.value.text = code!;
  }

  callApiForRegisterFirebaseToken(
      {required BuildContext context, String? token, User? user}) {
    FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();
    dict["firebase_token"] = token;
    print(token);

    return NetworkClient.getInstance.callApi(
      context,
      authUrl,
      ApiConstant.verifyUser,
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        AuthModel authModel = AuthModel.fromJson(response);
        otpController.value.text = "";
        if (authModel.userType == "new") {
          // box.write(Constant.tokenKey, authModel.token);
          // box.write(Constant.tokenKey, authModel.token);

          Get.toNamed(Routes.registrationScreen, arguments: {
            "userData": user,
            "logInType": "mobile",
            "token": authModel.token
          });
        } else {
          box.write(Constant.tokenKey, authModel.token);
          Get.offAllNamed(Routes.homeScreen);
          // print("===>" + authModel.token.toString());
          box.write(Constant.loginMethod, "mobile");
        }
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app.resolve<CustomDialogs>().getDialog(title: "Failed", desc: message);
        print(" error");
      },
    );
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}
  void increment() => count.value++;
}
