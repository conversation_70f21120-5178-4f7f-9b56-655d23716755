import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../controllers/password_screen_controller.dart';

class PasswordScreenView extends GetWidget<PasswordScreenController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MySize.screenHeight,
        width: MySize.screenWidth,
        child: SingleChildScrollView(
          child: Container(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    left: MySize.getScaledSizeWidth(36),
                    right: MySize.getScaledSizeWidth(36),
                    top: MySize.getScaledSizeHeight(220),
                  ),
                  child: Container(
                    // color: Colors.red,
                    width: MySize.getScaledSizeWidth(350),
                    height: MySize.getScaledSizeHeight(90),
                    child: Image(
                      image: AssetImage("assets/logo1.jpg"),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                    left: MySize.getScaledSizeWidth(36),
                    right: MySize.getScaledSizeWidth(36),
                    top: MySize.getScaledSizeHeight(140),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            "assets/login_icon.svg",
                            height: MySize.size20,
                            width: MySize.getScaledSizeWidth(20),
                          ),
                          SizedBox(
                            width: MySize.getScaledSizeWidth(10),
                          ),
                          Text(
                            "OTP",
                            style: TextStyle(fontSize: MySize.size20),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: MySize.size18,
                      ),
                      Container(
                          // width: MySize.getScaledSizeWidth(327.00),
                          child: Obx(() => PinCodeTextField(
                              appContext: context,
                              controller: controller.otpController.value,
                              length: 6,
                              obscureText: false,
                              obscuringCharacter: '*',
                              keyboardType: TextInputType.number,
                              autoDismissKeyboard: true,
                              enableActiveFill: true,
                              onChanged: (value) {},
                              textStyle: TextStyle(
                                fontSize: MySize.getScaledSizeHeight(14.0),
                              ),
                              pinTheme: PinTheme(
                                  fieldHeight: MySize.getScaledSizeWidth(54.00),
                                  fieldWidth: MySize.getScaledSizeWidth(47.83),
                                  shape: PinCodeFieldShape.box,
                                  borderRadius: BorderRadius.circular(
                                      MySize.getScaledSizeWidth(8.00)),
                                  selectedFillColor: Color(0xff1212121D),
                                  activeFillColor: Color(0xff1212121D),
                                  inactiveFillColor: Color(0xff1212121D),
                                  inactiveColor: appTheme.textGrayColor,
                                  selectedColor: appTheme.textGrayColor,
                                  activeColor: appTheme.textGrayColor)))),
                      // getTextField(
                      //   prefixIcon: Padding(
                      //     padding: EdgeInsets.all(
                      //       MySize.size12!,
                      //     ),
                      //     child: SvgPicture.asset(
                      //       "assets/password_key.svg",
                      //       height: MySize.size24,
                      //       // color: Colors.black,
                      //     ),
                      //   ),
                      //   hintText: "Password",
                      //   textEditingController: controller.emailController.value,
                      // ),
                      SizedBox(
                        height: MySize.size8,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: Container(),
                          ),
                          InkWell(
                            onTap: () async {
                              await FirebaseAuth.instance
                                  .verifyPhoneNumber(
                                    phoneNumber: "${controller.mobileNumber}",
                                    verificationCompleted:
                                        (PhoneAuthCredential credential) {},
                                    verificationFailed:
                                        (FirebaseAuthException e) {
                                      if (e.code == 'invalid-phone-number') {
                                        app.resolve<CustomDialogs>().getDialog(
                                            title: "Phone Number Invalid",
                                            desc:
                                                "Please Check Your Phone Number.");
                                      } else if (e.code ==
                                          "too-many-requests") {
                                        app.resolve<CustomDialogs>().getDialog(
                                            title: "Too many requests",
                                            desc:
                                                "We have blocked all requests from this device due to unusual activity. Try again later");
                                        // errorSnackBar("Too many requests",
                                        //     "We have blocked all requests from this device due to unusual activity. Try again later");
                                      }
                                    },
                                    codeSent: (String verificationId,
                                        int? resendToken) {
                                      controller.resendToken = resendToken;
                                      controller.verificationId =
                                          verificationId;
                                      // Get.to(
                                      //         () => OtpScreen(verificationId, widget.link));
                                      // Get.toNamed(Routes.PASSWORD_SCREEN,
                                      //     arguments: {
                                      //       StringConstant.verificationId:
                                      //       verificationId,
                                      //       StringConstant.resendToken:
                                      //       resendToken,
                                      //     });
                                    },
                                    codeAutoRetrievalTimeout:
                                        (String verificationId) {
                                      print("CART ::: $verificationId");
                                    },
                                  )
                                  .catchError((e) => print(e.toString()));
                            },
                            child: Text("Resend OTP"),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: MySize.size50,
                      ),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "By Accepting",
                            style: TextStyle(
                                fontSize: MySize.size12,
                                color: BaseTheme()
                                    .secondaryColor
                                    .withOpacity(0.8)),
                          ),
                          Text(
                            " Terms & Condition",
                            style: TextStyle(
                                fontSize: MySize.size12,
                                color: BaseTheme().primaryTheme),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: MySize.size14,
                      ),
                      InkWell(
                        onTap: () async {
                          // Get.toNamed(Routes.REGISTRATION_SCREEN);
                          await FirebaseAuth.instance
                              .signInWithCredential(
                                  PhoneAuthProvider.credential(
                                      verificationId:
                                          controller.verificationId!,
                                      smsCode:
                                          controller.otpController.value.text))
                              .then((value) async {
                            // print(value.user!.getIdToken());
                            if (value != null) {
                              String? token = await value.user!.getIdToken();
                              controller.callApiForRegisterFirebaseToken(
                                  context: context,
                                  token: token,
                                  user: value.user!);
                            }
                          }).catchError((e) {
                            print(e.toString());
                            Get.snackbar(
                                "Invalid OTP", "Please provide right OTP",
                                colorText: Colors.white,
                                backgroundColor: Colors.redAccent,
                                borderRadius: 0,
                                snackPosition: SnackPosition.BOTTOM,
                                margin: EdgeInsets.zero);
                          });
                        },
                        child: Container(
                          height: MySize.getScaledSizeWidth(50),
                          width: double.infinity,
                          decoration: BoxDecoration(
                              color: BaseTheme().newPrimaryColor,
                              borderRadius:
                                  BorderRadius.circular(MySize.size5!)),
                          child: Center(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  "Login",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: MySize.size16),
                                ),
                                SizedBox(
                                  width: MySize.getScaledSizeWidth(5),
                                ),
                                Icon(
                                  Icons.arrow_forward,
                                  color: Colors.white,
                                  size: MySize.getScaledSizeHeight(20),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: MySize.size40,
                      ),
                      // Row(
                      //   children: [
                      //     Expanded(
                      //       child: Container(
                      //         height: MySize.size1,
                      //         color: Colors.red,
                      //       ),
                      //     ),
                      //     Text(
                      //       "  Or  ",
                      //       style: TextStyle(
                      //           color: Colors.red, fontSize: MySize.size14),
                      //     ),
                      //     Expanded(
                      //       child: Container(
                      //         height: MySize.size1,
                      //         color: Colors.red,
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      // SizedBox(
                      //   height: MySize.size20,
                      // ),
                      // Row(
                      //   children: [
                      //     Expanded(
                      //       child: Container(
                      //         height: MySize.size44,
                      //         decoration: BoxDecoration(
                      //             borderRadius:
                      //                 BorderRadius.circular(MySize.size4!),
                      //             border: Border.all(
                      //               color: BaseTheme().borderColor,
                      //             )),
                      //         child: Center(
                      //           child: Row(
                      //             mainAxisAlignment: MainAxisAlignment.center,
                      //             crossAxisAlignment: CrossAxisAlignment.center,
                      //             children: [
                      //               SvgPicture.asset(
                      //                 "assets/apple.svg",
                      //                 height: MySize.size19,
                      //               ),
                      //               SizedBox(
                      //                 width: MySize.getScaledSizeWidth(7),
                      //               ),
                      //               Text(
                      //                 "Apple",
                      //                 style: TextStyle(fontSize: MySize.size19),
                      //               ),
                      //             ],
                      //           ),
                      //         ),
                      //       ),
                      //     ),
                      //     SizedBox(
                      //       width: MySize.getScaledSizeWidth(15),
                      //     ),
                      //     Expanded(
                      //       child: Container(
                      //         height: MySize.size44,
                      //         decoration: BoxDecoration(
                      //             borderRadius:
                      //                 BorderRadius.circular(MySize.size4!),
                      //             border: Border.all(
                      //               color: BaseTheme().borderColor,
                      //             )),
                      //         child: Center(
                      //           child: Row(
                      //             mainAxisAlignment: MainAxisAlignment.center,
                      //             crossAxisAlignment: CrossAxisAlignment.center,
                      //             children: [
                      //               SvgPicture.asset(
                      //                 "assets/google.svg",
                      //                 height: MySize.size19,
                      //               ),
                      //               SizedBox(
                      //                 width: MySize.getScaledSizeWidth(7),
                      //               ),
                      //               Text(
                      //                 "Google",
                      //                 style: TextStyle(fontSize: MySize.size19),
                      //               ),
                      //             ],
                      //           ),
                      //         ),
                      //       ),
                      //     ),
                      //   ],
                      // )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
