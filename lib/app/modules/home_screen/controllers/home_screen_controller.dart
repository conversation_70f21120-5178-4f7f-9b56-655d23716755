// ignore_for_file: non_constant_identifier_names, unused_local_variable

import 'dart:developer';
import 'dart:io';

import 'package:carousel_slider/carousel_controller.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:small_officer/Models/notification_model.dart';
import 'package:small_officer/Models/banner_data_model.dart';
import 'package:small_officer/Models/location_data_model.dart';
import 'package:small_officer/Models/resourceDataModel.dart';
import 'package:small_officer/Models/resourceTypeModel.dart';
import 'package:small_officer/constants/size_constant.dart';
import '../../../../Models/user_profile_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../data/network_client.dart';
import '../../no_internet/views/no_internet_view.dart';

class HomeScreenController extends GetxController
    with SingleGetTickerProviderMixin {
  CarouselController carouselController = CarouselController();
  RxInt sliderIndex = 0.obs;

  final count = 0.obs;
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  RxInt selectedIndex = 0.obs;
  RxInt selectedIndex1 = 0.obs;
  RxBool isEnable = false.obs;
  RxBool hasDataForBanner = false.obs;
  RxBool hasData = false.obs;
  RxBool hasNotificationData = false.obs;
  RxString? locationName;
  RxInt locationId = 1.obs;
  RxInt? selectedDrawerIndex;

  Rx<BannerDataModel>? bannerDataModel;
  LocationDataModel? locationDataModel;
  Rx<TabController>? tabController;
  Rx<ResourceTypeModel>? resourceTypeModel;
  List<Bannerdata> bannerWiseLocation = [];
  RxInt countNewNoti = 0.obs;
  RxInt countNewNotiAlert = 0.obs;
  DatabaseReference databaseRef = FirebaseDatabase(
          databaseURL: "https://small-officer-default-rtdb.firebaseio.com/")
      .reference();
  SharedPreferences? prefs;
  GetStorage box = GetStorage();

  @override
  void onInit() async {
    super.onInit();

    // Initialize tabController first
    tabController?.value = TabController(length: 0, vsync: this);
    tabController?.value.addListener(() {
      selectedIndex1.value = tabController!.value.index;
      // ignore: prefer_interpolation_to_compose_strings
      print("Selected Index: " + tabController!.value.index.toString());
      tabController?.refresh();
    });

    prefs = await SharedPreferences.getInstance();

    // box.remove('locationId');
    print(prefs!.getInt(Constant.notiCount));

    if (isNullEmptyOrFalse(prefs!.getInt(Constant.notiCount))) {
      prefs!.setInt(Constant.notiCount, 0);
    }

    if (isNullEmptyOrFalse(prefs!.getInt(Constant.notiCountForAlert))) {
      prefs!.setInt(Constant.notiCountForAlert, 0);
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await callApiForGetLocationList(context: Get.context!);
      callApiForSendFCMToken(context: Get.context!);
      await callApiForGetSourceTypeList(context: Get.context!);
      callApiForGetBannerTypeList(context: Get.context!);
      callApiForGetProfile(context: Get.context!);
      callApiForGetNotificationData(context: Get.context!);
      isDashBoardOpen.value = true;
    });
  }

  callApiForGetNotificationData(
      {required BuildContext context, int? LocationId}) {
    hasNotificationData.value = false;
    // FocusScope.of(context).unfocus();
    // app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.notification,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasNotificationData.value = true;
        // app.resolve<CustomDialogs>().hideCircularDialog(context);
        NotificationModel notificationModel =
            NotificationModel.fromJson(response);
        countNewNotiAlert.value = notificationModel.data!.length;
        print(prefs!.getInt(Constant.notiCountForAlert));
        int n = countNewNotiAlert.value -
            prefs!.getInt(Constant.notiCountForAlert)!.toInt();
        // box.write("notiCount", n.toInt());
        countNewNotiAlert.value = n.toInt();
      },
      failureCallback: (status, message) {
        hasNotificationData.value = true;

        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForSendFCMToken({
    required BuildContext context,
    num? mobileNo,
    num? charges,
  }) async {
    String? fcmToken;
    if (Platform.isAndroid) {
      fcmToken = (await FirebaseMessaging.instance.getToken())!;
    } else if (Platform.isIOS) {
      fcmToken = (await FirebaseMessaging.instance.getToken());
    }

    if (fcmToken != null) {
      log("fcm token ---> $fcmToken");
    } else {
      print("fcm token is null");
    }
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();
    // dict["advisor_mobile"] = mobileNo!;
    dict["FcmToken"] = fcmToken!;

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.fcmToken,
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        // app.resolve<CustomDialogs>().hideCircularDialog(context);
        // //"msg" -> "Campaign Submitted Successfully"
        // app.resolve<CustomDialogs>().getDialog(
        //     title: "Success", desc: "Campaign Submitted Successfully");
        // callApiForGetAllCallHistoryList(context: context);

        // box.write(Constant.tokenKey, response["token"]);
        // showOtp.value = true;
      },
      failureCallback: (status, message) {
        // app.resolve<CustomDialogs>().hideCircularDialog(context);
        // app.resolve<CustomDialogs>().getDialog(title: "Failed", desc: message);
        // print(" error");
      },
    );
  }

  callApiForGetProfile({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.userProfile,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        UserProfileModel userProfileModel = UserProfileModel.fromJson(response);
        print(userProfileModel.data?.firstName);
        box.write(Constant.userId, userProfileModel.data?.id);
        box.write(
            Constant.totalBookingHours, userProfileModel.data?.totalBookingH);
        databaseRef
            .child("chats")
            .child(box.read(Constant.userId).toString())
            .once()
            .then((onValue) {
          DataSnapshot dataValues = onValue.snapshot;
          if (dataValues.value != null) {
            Map<dynamic, dynamic> values =
                dataValues.value as Map<dynamic, dynamic>;
            int i = 0;
            values.forEach((key, value) {
              i++;
            });
            countNewNoti.value = i;
            print(prefs!.getInt(Constant.notiCount));
            int n =
                countNewNoti.value - prefs!.getInt(Constant.notiCount)!.toInt();
            // box.write("notiCount", n.toInt());
            countNewNoti.value = n.toInt();
            print("as${countNewNoti.value}");
          }
        });
      },
      failureCallback: (status, message) {
        //app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForGetSourceTypeList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.sourceTypeApi,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        //   app.resolve<CustomDialogs>().hideCircularDialog(context);
        resourceTypeModel = ResourceTypeModel.fromJson(response).obs;
        tabController = TabController(
                length: 1 + resourceTypeModel!.value.data!.rows!.length,
                vsync: this)
            .obs;

        print(resourceTypeModel);
        update();
        refresh();
        tabController!.refresh();
        // AllResourceController resourceController =
        //     Get.put(dependency)<AllResourceController>();
        // resourceController
        //     .callApiForGetSourceTypeList(context: context)!
        //     .refresh();
        if (response["response"] == "success") {
          print("ok");
        } else {}
      },
      failureCallback: (status, message) {
        hasData.value = true;

        //app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  _handleTabSelection() {
    if (tabController!.value.indexIsChanging) {
      tabController!.value.index = tabController!.value.index;
    }
    tabController!.refresh();
  }

  callApiForGetBannerTypeList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.announcement,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasDataForBanner.value = true;
        bannerDataModel = BannerDataModel.fromJson(response).obs;
        print(bannerDataModel?.value);
        bannerDataModel?.refresh();
        //   app.resolve<CustomDialogs>().hideCircularDialog(context);
        if (response["response"] == "success") {
          print("ok");
        } else {}
      },
      failureCallback: (status, message) {
        hasDataForBanner.value = false;

        //app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForGetLocationList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.location,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        // hasData.value = true;
        //  app.resolve<CustomDialogs>().hideCircularDialog(context);
        locationDataModel = LocationDataModel.fromJson(response);
        if (locationDataModel != null) {
          if (locationDataModel!.data != null &&
              locationDataModel!.data!.isNotEmpty) {
            locationId.value = locationDataModel!.data!.first.id ?? 1;
            locationName = locationDataModel!.data!.first.name?.obs;
            bannerWiseLocation = (isNullEmptyOrFalse(
                    locationDataModel!.data!.first.announcements))
                ? []
                : locationDataModel!.data!.first.announcements!;
            box.write("locationId", locationId.value);
          }
        }
        print(locationDataModel);
      },
      failureCallback: (status, message) {
        // hasData.value = true;

        //app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}

  void increment() => count.value++;
}

class AllResourceController extends GetxController {
  final _obj = ''.obs;

  set obj(value) => _obj.value = value;

  get obj => _obj.value;
  RxBool hasData = false.obs;
  Rx<ResourceDataModel>? resourceDataModel;

  @override
  void onInit() {
    super.onInit();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      callApiForGetResourceData(context: Get.context!);
    });
  }

  Rx<List<RowsData>> getList({int? id}) {
    Rx<List<RowsData>> idWiseResource = RxList<RowsData>([]).obs;

    idWiseResource.value.clear();

    for (var element in resourceDataModel!.value.data!.rows!) {
      if (element.resourceType?.id == id) {
        idWiseResource.value.add(element);
      }
    }

    return idWiseResource;
  }

  callApiForGetResourceData({required BuildContext context, int? LocationId}) {
    hasData.value = false;
    FocusScope.of(context).unfocus();
    // app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      (box.read("locationId") != null)
          ? "${ApiConstant.sourceAllDataApi}?LocationId=${box.read("locationId")}&isActive=1"
          : ApiConstant.sourceAllDataApi,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        // app.resolve<CustomDialogs>().hideCircularDialog(context);
        resourceDataModel = ResourceDataModel.fromJson(response).obs;

        print(resourceDataModel!.value);
        print("----------->    LocationId=${box.read("locationId")}");
      },
      failureCallback: (status, message) {
        hasData.value = true;

        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }
}

// class ResourceIdWiseController extends GetxController {
//   final AllResourceController homeScreenController =
//       Get.put(AllResourceController());
//   int? id;
//   ResourceIdWiseController({this.id});
//   final _obj = ''.obs;
//   set obj(value) => _obj.value = value;
//   get obj => _obj.value;
//   RxBool hasData = true.obs;
//   Rx<ResourceDataModel>? resourceDataModel;
//   Rx<List<RowsData>> idWiseResource = RxList<RowsData>().obs;
//
//   @override
//   void onInit() {
//     super.onInit();
//     WidgetsBinding.instance!.addPostFrameCallback((_) async {
//       // homeScreenController.resourceDataModel!.value.data!.rows!
//       //     .forEach((element) {
//       //   if (element.resourceType?.id == id) {
//       //     idWiseResource.value.add(element);
//       //   }
//       // });
//       getList(id: id);
//       // callApiForGetSourceTypeList(context: Get.context!);
//     });
//   }
//
//   getList({int? id}) {
//     idWiseResource.value.clear();
//     homeScreenController.resourceDataModel!.value.data!.rows!
//         .forEach((element) {
//       if (element.resourceType?.id == id) {
//         idWiseResource.value.add(element);
//       }
//     });
//     idWiseResource.refresh();
//   }
//
//   callApiForGetSourceTypeList({required BuildContext context, int? id}) {
//     FocusScope.of(context).unfocus();
//     app.resolve<CustomDialogs>().showCircularDialog(context);
//     Map<String, dynamic> dict = {};
//     GetStorage box = GetStorage();
//
//     return NetworkClient.getInstance.callApi(
//       context,
//       baseURL,
//       ApiConstant.sourceAllDataApi + "/$id",
//       MethodType.Get,
//       headers: NetworkClient.getInstance.getAuthHeaders(),
//       params: dict,
//       successCallback: (response, message) {
//         hasData.value = true;
//         app.resolve<CustomDialogs>().hideCircularDialog(context);
//         resourceDataModel = ResourceDataModel.fromJson(response).obs;
//
//         print(resourceDataModel!.value);
//       },
//       failureCallback: (status, message) {
//         hasData.value = true;
//
//         app.resolve<CustomDialogs>().hideCircularDialog(context);
//
//         print(" error");
//       },
//     );
//   }
// }
