import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:small_officer/app/modules/home_drawer/views/home_drawer_view.dart';
import 'package:small_officer/app/modules/my_booking/views/my_booking_view.dart';
import 'package:small_officer/app/modules/my_profile/views/my_profile_view.dart';
import 'package:small_officer/app/modules/past_booking/views/past_booking_view.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/constant.dart';

import '../../../../constants/size_constant.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/home_screen_controller.dart';

class HomeScreenView extends GetWidget<HomeScreenController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        key: controller.scaffoldKey,
        drawer: HomeDrawerView(),
        body: [
          Padding(
            padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
            child: Container(
              width: MySize.screenWidth,
              height: MySize.screenHeight,
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        // width: 200,
                        padding: EdgeInsets.all(15),
                        color: appTheme.newPrimaryColor,
                        child: Text(
                          "Find Nearby \nWorkspace",
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                      ),
                      Container(
                        // color: Colors.red,
                        margin: EdgeInsets.only(
                          right: 10,
                        ),
                        width: MySize.getScaledSizeWidth(200),
                        height: MySize.getScaledSizeHeight(58),
                        child: Image(
                          image: AssetImage("assets/logo1.jpg"),
                          fit: BoxFit.fill,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          Get.toNamed(Routes.notification);
                        },
                        child: Stack(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                  left: MySize.getScaledSizeWidth(0),
                                  right: MySize.getScaledSizeWidth(15),
                                  top: MySize.size17!,
                                  bottom: MySize.size17!),
                              child: Image(
                                image: AssetImage("assets/notification.png"),
                                height: MySize.getScaledSizeHeight(25),
                              ),
                              // child: SvgPicture.asset(
                              //   "assets/notification.svg",
                              //   height: MySize.size26,
                              //   width: MySize.getScaledSizeWidth(37),
                              // ),
                            ),
                            if (controller.countNewNotiAlert.value != 0)
                              Positioned(
                                top: 10,
                                right: 14,
                                child: CircleAvatar(
                                  radius: MySize.size7,
                                  backgroundColor: Colors.red,
                                  // child: Text(
                                  //   controller.countNewNotiAlert.value.toString(),
                                  //   style: TextStyle(
                                  //     color: Colors.white,
                                  //     fontSize: MySize.size12,
                                  //   ),
                                  // ),
                                ),
                              )
                          ],
                        ),
                      ),
                    ],
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(23)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                              top: MySize.size20!,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Text(
                                //   "Find Nearby Workspace",
                                //   style: TextStyle(
                                //     fontSize: MySize.size25,
                                //   ),
                                // ),
                                // Text(
                                //   "Workspace",
                                //   style: TextStyle(
                                //     fontSize: MySize.size25,
                                //     color: Color(0xffCA0222),
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                          (controller.locationName != null)
                              ? Padding(
                                  padding: EdgeInsets.only(
                                    top: MySize.size1!,
                                  ),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        "assets/pin1.svg",
                                        height: MySize.size30,
                                        width: MySize.getScaledSizeWidth(37),
                                      ),
                                      SizedBox(
                                        width: MySize.getScaledSizeWidth(14),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          controller.scaffoldKey.currentState!
                                              .openDrawer();
                                        },
                                        child: Row(
                                          children: [
                                            Text(
                                              controller.locationName!.value,
                                              style: TextStyle(
                                                fontSize: MySize.size21,
                                                fontWeight: FontWeight.w700,
                                                color: Color(0xff216e9b),
                                              ),
                                            ),
                                            SizedBox(
                                              width:
                                                  MySize.getScaledSizeWidth(7),
                                            ),
                                            SvgPicture.asset(
                                              "assets/arrow_bottom.svg",
                                              height: MySize.size14,
                                              color:
                                                  Colors.black.withOpacity(0.7),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        width: MySize.getScaledSizeWidth(15),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          Get.toNamed(Routes.info, arguments: {
                                            Constant.locationName:
                                                controller.locationName,
                                            Constant.announcementList:
                                                controller
                                                    .locationDataModel?.data!
                                                    .firstWhere((element) =>
                                                        element.id ==
                                                        controller
                                                            .locationId.value)
                                                    .infos,
                                          });
                                        },
                                        child: SvgPicture.asset(
                                          "assets/info.svg",
                                          height: MySize.size24,
                                          color: Colors.black.withOpacity(0.7),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : SizedBox(),
                          Container(
                            margin: EdgeInsets.only(top: MySize.size20!),
                            height: MySize.size1,
                            width: double.infinity,
                            color: appTheme.textGrayColor,
                          ),
                          SizedBox(
                            height: MySize.size20,
                          ),
                          // (controller.isEnable.value)
                          //     ? Container(
                          //         height: MySize.size100,
                          //         decoration: BoxDecoration(
                          //           color: Color(0xffBF6BFF),
                          //           borderRadius: BorderRadius.circular(MySize.size10!),
                          //         ),
                          //         padding:
                          //             EdgeInsets.symmetric(horizontal: MySize.size5!),
                          //         child: Row(
                          //           children: [
                          //             Image(
                          //               image: AssetImage("assets/speker.png"),
                          //             ),
                          //             SizedBox(
                          //               width: MySize.getScaledSizeWidth(10),
                          //             ),
                          //             Expanded(
                          //                 child: Text(
                          //               "Hey, here's the announcement for the new space!",
                          //               style: TextStyle(
                          //                 color: Colors.white,
                          //                 fontSize: MySize.size14,
                          //               ),
                          //             )),
                          //           ],
                          //         ),
                          //       )
                          //     : SizedBox(),
                          (controller.hasDataForBanner.value)
                              ? ((controller.bannerDataModel != null &&
                                      controller.bannerDataModel?.value.data!
                                              .where((element) =>
                                                  (element.isActive == true &&
                                                      element.locationId ==
                                                          controller.locationId
                                                              .value))
                                              .length !=
                                          0)
                                  ? Container(
                                      height: MySize.getScaledSizeHeight(150),
                                      child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          CarouselSlider(
                                            carouselController:
                                                controller.carouselController,
                                            options: CarouselOptions(
                                              viewportFraction: 1,
                                              //viewportFraction: MySize.getScaledSizeWidth(0.8),
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      150),

                                              autoPlay: true,
                                              onPageChanged: (index, reason) {
                                                controller.sliderIndex.value =
                                                    index;
                                              },
                                            ),
                                            items: controller
                                                .bannerDataModel?.value.data!
                                                .where((element) =>
                                                    (element.isActive == true &&
                                                        element.locationId ==
                                                            controller
                                                                .locationId
                                                                .value))
                                                .map((data) {
                                              return Builder(
                                                builder:
                                                    (BuildContext context) {
                                                  return ClipRRect(
                                                    child:
                                                        CommonNetworkImageView(
                                                      url: data.imageUrl
                                                          .toString(),
                                                      height: MySize
                                                          .getScaledSizeHeight(
                                                              150),
                                                      width: MySize
                                                          .getScaledSizeWidth(
                                                              370),
                                                      fit: BoxFit.fill,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            MySize.size15!),
                                                    // child: Image(
                                                    //   image: AssetImage(
                                                    //     "assets/table.png",
                                                    //   ),
                                                    //   fit: BoxFit.cover,
                                                    //   width: MySize.getScaledSizeWidth(380),
                                                    // ),
                                                  );
                                                },
                                              );
                                            }).toList(),
                                          ),
                                        ],
                                      ),
                                    )
                                  : SizedBox())
                              : SizedBox(),
                          Expanded(
                            child: (!controller.hasData.value)
                                ? Center(
                                    child: getShimerForHome(),
                                  )
                                : ((controller.resourceTypeModel != null &&
                                        !isNullEmptyOrFalse(controller
                                            .resourceTypeModel!.value.data) &&
                                        !isNullEmptyOrFalse(controller
                                                .resourceTypeModel!
                                                .value
                                                .data!
                                                .count ==
                                            0))
                                    ? Center(
                                        child: Text("No any resource found.."),
                                      )
                                    : ((controller.resourceTypeModel != null &&
                                            !isNullEmptyOrFalse(controller
                                                .resourceTypeModel!
                                                .value
                                                .data) &&
                                            !isNullEmptyOrFalse(controller
                                                .resourceTypeModel!
                                                .value
                                                .data!
                                                .rows))
                                        ? DefaultTabController(
                                            length: controller
                                                .tabController!.value.length,
                                            child: Column(
                                              children: [
                                                Container(
                                                  padding:
                                                      EdgeInsets.only(top: 0),
                                                  height: MySize.size41,
                                                  width: MySize.screenWidth,
                                                  color: Colors.white,
                                                  child: TabBar(
                                                    isScrollable: true,
                                                    controller: controller
                                                        .tabController!.value,
                                                    indicatorColor:
                                                        Colors.white,
                                                    automaticIndicatorColorAdjustment:
                                                        true,
                                                    unselectedLabelColor:
                                                        Colors.grey,
                                                    onTap: (index) {
                                                      controller.tabController!
                                                          .value.index = index;
                                                      controller.tabController!
                                                          .refresh();
                                                    },
                                                    tabs: [
                                                      Text(
                                                        "All",
                                                        style: TextStyle(
                                                            color: (controller
                                                                        .tabController!
                                                                        .value
                                                                        .index ==
                                                                    0)
                                                                ? Colors.black
                                                                : appTheme
                                                                    .textGrayColor,
                                                            fontSize:
                                                                MySize.size18),
                                                      ),
                                                      for (int i = 0;
                                                          i <
                                                              controller
                                                                  .resourceTypeModel!
                                                                  .value
                                                                  .data!
                                                                  .rows!
                                                                  .length;
                                                          i++)
                                                        Text(
                                                          controller
                                                              .resourceTypeModel!
                                                              .value
                                                              .data!
                                                              .rows![i]
                                                              .name
                                                              .toString(),
                                                          style: TextStyle(
                                                              color: (controller
                                                                          .tabController!
                                                                          .value
                                                                          .index ==
                                                                      i + 1)
                                                                  ? Colors.black
                                                                  : appTheme
                                                                      .textGrayColor,
                                                              fontSize: MySize
                                                                  .size18),
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                                Expanded(
                                                  child: TabBarView(
                                                    controller: controller
                                                        .tabController!.value,
                                                    children: [
                                                      GetBuilder<
                                                          AllResourceController>(
                                                        init:
                                                            AllResourceController(),
                                                        builder: (l) {
                                                          return Obx(() {
                                                            return (l.hasData
                                                                    .value)
                                                                ? ((l
                                                                        .resourceDataModel!
                                                                        .value
                                                                        .data!
                                                                        .rows!
                                                                        .isEmpty)
                                                                    ? Center(
                                                                        child: Text(
                                                                            "No Data Found.."),
                                                                      )
                                                                    : GridView
                                                                        .count(
                                                                        crossAxisCount:
                                                                            2,
                                                                        padding:
                                                                            EdgeInsets.zero,
                                                                        scrollDirection:
                                                                            Axis.vertical,
                                                                        crossAxisSpacing:
                                                                            MySize.getScaledSizeWidth(15),
                                                                        childAspectRatio:
                                                                            0.94,
                                                                        mainAxisSpacing:
                                                                            MySize.getScaledSizeHeight(20),
                                                                        children:
                                                                            List.generate(
                                                                          l
                                                                              .resourceDataModel!
                                                                              .value
                                                                              .data!
                                                                              .rows!
                                                                              .length,
                                                                          (ind) =>
                                                                              InkWell(
                                                                            onTap:
                                                                                () {
                                                                              Get.toNamed(Routes.productDetail, arguments: {
                                                                                "isBooked": false,
                                                                                StringConstant.productData: l.resourceDataModel!.value.data!.rows![ind]
                                                                              });
                                                                            },
                                                                            child:
                                                                                Card(
                                                                              elevation: 1,
                                                                              shadowColor: appTheme.textGrayColor,
                                                                              child: Container(
                                                                                height: MySize.size90,
                                                                                width: 100,
                                                                                decoration: BoxDecoration(
                                                                                  color: Colors.white,
                                                                                  borderRadius: BorderRadius.circular(MySize.size8!),
                                                                                ),
                                                                                padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(7), vertical: MySize.getScaledSizeHeight(7)),
                                                                                child: Column(
                                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                                  children: [
                                                                                    Container(
                                                                                      height: MySize.size120,
                                                                                      child: Stack(
                                                                                        children: [
                                                                                          ClipRRect(
                                                                                            child: CommonNetworkImageView(
                                                                                              url: (l.resourceDataModel!.value.data!.rows![ind].imageUrl != null) ? l.resourceDataModel!.value.data!.rows![ind].imageUrl.toString() : "",
                                                                                              height: MySize.getScaledSizeHeight(120),
                                                                                              fit: BoxFit.fill,
                                                                                            ),
                                                                                            borderRadius: BorderRadius.circular(MySize.size4!),
                                                                                          ),
                                                                                          Positioned(
                                                                                            child: InkWell(
                                                                                              onTap: () {
                                                                                                String msg = "";
                                                                                                String name = "Name : ${l.resourceDataModel!.value.data!.rows![ind].name}\n";
                                                                                                String img = "Image : ${l.resourceDataModel!.value.data!.rows![ind].imageUrl}\n";
                                                                                                String dec = "Description : ${l.resourceDataModel!.value.data!.rows![ind].description}\n";

                                                                                                String pCapacity = "Person Capacity : ${l.resourceDataModel!.value.data!.rows![ind].personCapacity}\n";
                                                                                                String avaiblity = "Availability : ${DateFormat("hh:mm a").format(getDateFromString(l.resourceDataModel!.value.data!.rows![ind].openAt.toString(), formatter: 'HH:mm:ss'))} - ${DateFormat("hh:mm a").format(getDateFromString(l.resourceDataModel!.value.data!.rows![ind].closeAt.toString(), formatter: 'HH:mm:ss'))}\n";
                                                                                                String rate = "Rate Per Hour : ${l.resourceDataModel!.value.data!.rows![ind].ratePerHour}\$\n";
                                                                                                String location = "Location : ${l.resourceDataModel!.value.data!.rows![ind].location!.name}\n";
                                                                                                msg = name + img + dec + pCapacity + avaiblity + rate + location;
                                                                                                Share.share(msg);
                                                                                              },
                                                                                              child: CircleAvatar(
                                                                                                radius: MySize.size14,
                                                                                                backgroundColor: Colors.white.withOpacity(0.5),
                                                                                                child: SvgPicture.asset(
                                                                                                  "assets/share.svg",
                                                                                                  height: MySize.size14,
                                                                                                ),
                                                                                              ),
                                                                                            ),
                                                                                            right: MySize.size5,
                                                                                            top: MySize.size5,
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                    SizedBox(
                                                                                      height: MySize.size5!,
                                                                                    ),
                                                                                    Row(
                                                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                                      children: [
                                                                                        Expanded(
                                                                                          child: Text(
                                                                                            l.resourceDataModel!.value.data!.rows![ind].name.toString(),
                                                                                            maxLines: 1,
                                                                                            overflow: TextOverflow.ellipsis,
                                                                                            style: TextStyle(
                                                                                              fontSize: MySize.size16,
                                                                                              fontWeight: FontWeight.bold,
                                                                                            ),
                                                                                          ),
                                                                                        ),
                                                                                        Row(
                                                                                          children: [
                                                                                            SvgPicture.asset(
                                                                                              "assets/user.svg",
                                                                                              height: MySize.size12,
                                                                                            ),
                                                                                            SizedBox(
                                                                                              width: MySize.getScaledSizeWidth(6),
                                                                                            ),
                                                                                            Text(
                                                                                              l.resourceDataModel!.value.data!.rows![ind].personCapacity.toString(),
                                                                                              style: TextStyle(
                                                                                                fontSize: MySize.size15,
                                                                                                fontWeight: FontWeight.bold,
                                                                                              ),
                                                                                            )
                                                                                          ],
                                                                                        )
                                                                                      ],
                                                                                    ),
                                                                                    SizedBox(
                                                                                      height: MySize.size10!,
                                                                                    ),
                                                                                    // Row(
                                                                                    //   children: [
                                                                                    //     SvgPicture.asset(
                                                                                    //       "assets/location_light.svg",
                                                                                    //       height: MySize.size12,
                                                                                    //     ),
                                                                                    //     SizedBox(
                                                                                    //       width: MySize.getScaledSizeWidth(5),
                                                                                    //     ),
                                                                                    //     Text(
                                                                                    //       (l.resourceDataModel!.value.data!.rows![ind].location != null) ? l.resourceDataModel!.value.data!.rows![ind].location!.name.toString() : "-",
                                                                                    //       style: TextStyle(
                                                                                    //         fontSize: MySize.size16,
                                                                                    //         color: appTheme.textGrayColor,
                                                                                    //       ),
                                                                                    //     ),
                                                                                    //   ],
                                                                                    // ),
                                                                                    // SizedBox(
                                                                                    //   height:
                                                                                    //       MySize.size8!,
                                                                                    // ),
                                                                                    FittedBox(
                                                                                      fit: BoxFit.scaleDown,
                                                                                      child: Row(
                                                                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                                        children: [
                                                                                          Row(
                                                                                            children: [
                                                                                              CircleAvatar(
                                                                                                backgroundColor: Colors.green,
                                                                                                radius: MySize.size4,
                                                                                              ),
                                                                                              Text(
                                                                                                " Available",
                                                                                                style: TextStyle(
                                                                                                  fontSize: MySize.size11,
                                                                                                ),
                                                                                              ),
                                                                                            ],
                                                                                          ),
                                                                                          (l.resourceDataModel!.value.data!.rows![ind].openAt != null)
                                                                                              ? Text(
                                                                                                  "${DateFormat("h:mm a").format(getDateFromString(l.resourceDataModel!.value.data!.rows![ind].openAt.toString(), formatter: 'HH:mm:ss'))} - ${DateFormat("h:mm a").format(
                                                                                                        getDateFromString(l.resourceDataModel!.value.data!.rows![ind].closeAt.toString(), formatter: 'HH:mm:ss'),
                                                                                                      )}",
                                                                                                  style: TextStyle(
                                                                                                    fontSize: MySize.size11,
                                                                                                  ),
                                                                                                )
                                                                                              : SizedBox(),
                                                                                        ],
                                                                                      ),
                                                                                    ),
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        ),
                                                                      ))
                                                                : Center(
                                                                    child:
                                                                        getShimerForHome(),
                                                                  );
                                                          });
                                                        },
                                                      ),
                                                      for (int i = 0;
                                                          i <
                                                              controller
                                                                  .resourceTypeModel!
                                                                  .value
                                                                  .data!
                                                                  .rows!
                                                                  .length;
                                                          i++)
                                                        GetBuilder<
                                                            AllResourceController>(
                                                          init:
                                                              AllResourceController(),
                                                          builder: (logic) {
                                                            Get.put(
                                                                AllResourceController());
                                                            return Obx(() {
                                                              return (logic
                                                                      .hasData
                                                                      .value)
                                                                  ? (logic
                                                                          .getList(
                                                                              id: controller.resourceTypeModel!.value.data!.rows![i].id)
                                                                          .value
                                                                          .isEmpty)
                                                                      ? Center(
                                                                          child:
                                                                              Text("No Data Found.."),
                                                                        )
                                                                      : GridView.count(
                                                                          padding:
                                                                              EdgeInsets.zero,
                                                                          crossAxisCount:
                                                                              2,
                                                                          scrollDirection:
                                                                              Axis.vertical,
                                                                          crossAxisSpacing:
                                                                              MySize.getScaledSizeWidth(20),
                                                                          childAspectRatio:
                                                                              0.94,
                                                                          mainAxisSpacing:
                                                                              MySize.getScaledSizeHeight(20),
                                                                          children:
                                                                              List.generate(
                                                                            logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value.length,
                                                                            (index) =>
                                                                                InkWell(
                                                                              onTap: () {
                                                                                Get.toNamed(Routes.productDetail, arguments: {
                                                                                  "isBooked": false,
                                                                                  StringConstant.productData: logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index]
                                                                                });
                                                                              },
                                                                              child: Card(
                                                                                elevation: 1,
                                                                                shadowColor: appTheme.textGrayColor,
                                                                                child: Container(
                                                                                  height: MySize.size90,
                                                                                  width: 100,
                                                                                  decoration: BoxDecoration(
                                                                                    color: Colors.white,
                                                                                    borderRadius: BorderRadius.circular(MySize.size8!),
                                                                                  ),
                                                                                  padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(7), vertical: MySize.getScaledSizeHeight(7)),
                                                                                  child: Column(
                                                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                                                    children: [
                                                                                      Container(
                                                                                        height: MySize.size120,
                                                                                        child: Stack(
                                                                                          children: [
                                                                                            // Image(
                                                                                            //   image: AssetImage(
                                                                                            //     "assets/table.png",
                                                                                            //   ),
                                                                                            //   height: MySize.size120,
                                                                                            //   fit: BoxFit.fill,
                                                                                            // ),
                                                                                            ClipRRect(
                                                                                              child: CommonNetworkImageView(
                                                                                                url: (logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].imageUrl != null) ? logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].imageUrl.toString() : "",
                                                                                                height: MySize.getScaledSizeHeight(120),
                                                                                                fit: BoxFit.fill,
                                                                                              ),
                                                                                              borderRadius: BorderRadius.circular(MySize.size4!),
                                                                                            ),
                                                                                            Positioned(
                                                                                              child: InkWell(
                                                                                                onTap: () {
                                                                                                  String msg = "";
                                                                                                  String name = "Name : ${logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].name}\n";
                                                                                                  String img = "Image : ${logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].imageUrl}\n";
                                                                                                  String dec = "Description : ${logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].description}\n";

                                                                                                  String pCapacity = "Person Capacity : ${logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].personCapacity}\n";
                                                                                                  String avaiblity = "Availability : ${DateFormat("hh:mm a").format(getDateFromString(logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].openAt.toString(), formatter: 'HH:mm:ss'))} - ${DateFormat("hh:mm a").format(getDateFromString(logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].closeAt.toString(), formatter: 'HH:mm:ss'))}\n";
                                                                                                  String rate = "Rate Per Hour : ${logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].ratePerHour}\$\n";
                                                                                                  String location = "Location : ${logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].location!.name}\n";
                                                                                                  msg = name + img + dec + pCapacity + avaiblity + rate + location;
                                                                                                  Share.share(msg);
                                                                                                },
                                                                                                child: CircleAvatar(
                                                                                                  radius: MySize.size14,
                                                                                                  backgroundColor: Colors.white.withOpacity(0.5),
                                                                                                  child: SvgPicture.asset(
                                                                                                    "assets/share.svg",
                                                                                                    height: MySize.size14,
                                                                                                  ),
                                                                                                ),
                                                                                              ),
                                                                                              right: MySize.size5,
                                                                                              top: MySize.size5,
                                                                                            ),
                                                                                          ],
                                                                                        ),
                                                                                      ),
                                                                                      SizedBox(
                                                                                        height: MySize.size7!,
                                                                                      ),
                                                                                      Row(
                                                                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                                        children: [
                                                                                          Expanded(
                                                                                            child: Text(
                                                                                              logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].name.toString(),
                                                                                              maxLines: 1,
                                                                                              overflow: TextOverflow.ellipsis,
                                                                                              style: TextStyle(
                                                                                                fontSize: MySize.size16,
                                                                                                fontWeight: FontWeight.bold,
                                                                                              ),
                                                                                            ),
                                                                                          ),
                                                                                          Row(
                                                                                            children: [
                                                                                              SvgPicture.asset(
                                                                                                "assets/user.svg",
                                                                                                height: MySize.size12,
                                                                                              ),
                                                                                              SizedBox(
                                                                                                width: MySize.getScaledSizeWidth(6),
                                                                                              ),
                                                                                              Text(
                                                                                                logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].personCapacity.toString(),
                                                                                                style: TextStyle(
                                                                                                  fontSize: MySize.size15,
                                                                                                  fontWeight: FontWeight.bold,
                                                                                                ),
                                                                                              )
                                                                                            ],
                                                                                          )
                                                                                        ],
                                                                                      ),
                                                                                      // SizedBox(
                                                                                      //   height: MySize.size8!,
                                                                                      // ),
                                                                                      // Row(
                                                                                      //   children: [
                                                                                      //     SvgPicture.asset(
                                                                                      //       "assets/location_light.svg",
                                                                                      //       height: MySize.size12,
                                                                                      //     ),
                                                                                      //     SizedBox(
                                                                                      //       width: MySize.getScaledSizeWidth(5),
                                                                                      //     ),
                                                                                      //     Text(
                                                                                      //       (logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].location != null) ? logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].location!.name.toString() : "-",
                                                                                      //       style: TextStyle(
                                                                                      //         fontSize: MySize.size16,
                                                                                      //         color: appTheme.textGrayColor,
                                                                                      //       ),
                                                                                      //     ),
                                                                                      //   ],
                                                                                      // ),
                                                                                      SizedBox(
                                                                                        height: MySize.size10!,
                                                                                      ),
                                                                                      FittedBox(
                                                                                        fit: BoxFit.scaleDown,
                                                                                        child: Row(
                                                                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                                                          children: [
                                                                                            Row(
                                                                                              children: [
                                                                                                CircleAvatar(
                                                                                                  backgroundColor: Colors.green,
                                                                                                  radius: MySize.size4,
                                                                                                ),
                                                                                                Text(
                                                                                                  " Available",
                                                                                                  style: TextStyle(
                                                                                                    fontSize: MySize.size11,
                                                                                                  ),
                                                                                                ),
                                                                                              ],
                                                                                            ),
                                                                                            (logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].openAt != null)
                                                                                                ? Text(
                                                                                                    "${DateFormat("h:mm a").format(getDateFromString(logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].openAt.toString(), formatter: 'HH:mm:ss'))} - ${DateFormat("h:mm a").format(getDateFromString(logic.getList(id: controller.resourceTypeModel!.value.data!.rows![i].id).value[index].closeAt.toString(), formatter: 'HH:mm:ss'))}",
                                                                                                    style: TextStyle(
                                                                                                      fontSize: MySize.size11,
                                                                                                    ),
                                                                                                  )
                                                                                                : SizedBox(),
                                                                                          ],
                                                                                        ),
                                                                                      ),
                                                                                    ],
                                                                                  ),
                                                                                ),
                                                                              ),
                                                                            ),
                                                                          ),
                                                                        )
                                                                  : Center(
                                                                      child:
                                                                          getShimerForHome(),
                                                                    );
                                                            });
                                                          },
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                        : SizedBox())),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          MyBookingView(),
          PastBookingView(),
          PastBookingView(),
          MyProfileView(),
        ].elementAt(controller.selectedIndex.value),
        bottomNavigationBar: Container(
          padding: EdgeInsets.symmetric(
            horizontal: MySize.getScaledSizeWidth(10),
          ),
          decoration: BoxDecoration(
              border: Border(
                  top: BorderSide(
            color: appTheme.borderColor,
          ))),
          child: BottomNavigationBar(
              // backgroundColor: Colors.red,
              items: [
                BottomNavigationBarItem(
                  icon: SvgPicture.asset(
                    (controller.selectedIndex.value == 0)
                        ? "assets/home_feed.svg"
                        : "assets/home_feed_unselected.svg",
                    height: MySize.size25,
                    color: ((controller.selectedIndex.value == 0))
                        ? appTheme.newPrimaryColor
                        : appTheme.primaryTheme,
                  ),
                  label: "",
                ),
                BottomNavigationBarItem(
                  icon: SvgPicture.asset(
                    (controller.selectedIndex.value == 1)
                        ? "assets/calander_selected.svg"
                        : "assets/calendar.svg",
                    height: MySize.size25,
                    color: ((controller.selectedIndex.value == 1))
                        ? appTheme.newPrimaryColor
                        : appTheme.primaryTheme,
                  ),
                  label: "",
                ),
                BottomNavigationBarItem(
                  icon: CircleAvatar(
                    radius: MySize.size27,
                    backgroundColor: appTheme.newPrimaryColor,
                    child: Center(
                      child: SvgPicture.asset(
                        "assets/unlock.svg",
                        height: MySize.size25,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  label: "",
                ),
                BottomNavigationBarItem(
                  icon: Stack(
                    children: [
                      SvgPicture.asset(
                        (controller.selectedIndex.value == 3)
                            ? "assets/comment.svg"
                            : "assets/comment.svg",
                        height: MySize.size25,
                        color: ((controller.selectedIndex.value == 3))
                            ? appTheme.newPrimaryColor
                            : appTheme.primaryTheme,
                      ),
                      if (controller.countNewNoti.value != 0)
                        Positioned(
                          top: 0,
                          right: 0,
                          child: CircleAvatar(
                            radius: MySize.size9,
                            backgroundColor: Colors.red,
                            child: Text(
                              controller.countNewNoti.value.toString(),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: MySize.size12,
                              ),
                            ),
                          ),
                        )
                    ],
                  ),
                  label: "",
                ),
                BottomNavigationBarItem(
                  icon: CircleAvatar(
                    radius: MySize.size17,
                    backgroundColor: ((controller.selectedIndex.value == 4))
                        ? appTheme.newPrimaryColor
                        : appTheme.primaryTheme,
                    child: CircleAvatar(
                      backgroundColor: Colors.white,
                      radius: MySize.size15,
                      child: CircleAvatar(
                        radius: MySize.size12,
                        backgroundColor: Colors.white,
                        // backgroundImage: AssetImage(
                        //   "assets/user.png",
                        // ),
                        child: Image(
                          image: AssetImage(
                            "assets/user.png",
                          ),
                          color: ((controller.selectedIndex.value == 4))
                              ? appTheme.newPrimaryColor
                              : appTheme.primaryTheme,
                        ),
                      ),
                    ),
                  ),
                  label: "",
                ),
              ],
              type: BottomNavigationBarType.fixed,
              // type: BottomNavigationBarType.shifting,
              currentIndex: controller.selectedIndex.value,
              selectedItemColor: Colors.black,
              unselectedItemColor: appTheme.textGrayColor,
              iconSize: MySize.size20!,
              onTap: (index) {
                if (index == 3) {
                  Get.toNamed(Routes.chatDetail);
                  // Navigator.of(context)
                  //     .push(MaterialPageRoute(builder: (context) {
                  //   return SnapshotBody();
                  // }));
                } else if (index == 2) {
                  Get.toNamed(Routes.lockScreen);
                } else {
                  controller.selectedIndex.value = index;
                }
              },
              elevation: 0),
        ),
      );
    });
  }
}
