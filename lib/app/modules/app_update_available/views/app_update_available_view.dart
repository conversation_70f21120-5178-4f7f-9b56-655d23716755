import 'dart:io';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/api_constant.dart';
import 'package:small_officer/constants/constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/app_button.dart';
import '../../../../constants/app_images.dart';
import '../../../../main.dart';
import '../controllers/app_update_available_controller.dart';

class AppUpdateAvailableView extends GetWidget<AppUpdateAvailableController> {
  const AppUpdateAvailableView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
        body: Container(
      width: MySize.safeWidth,
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            AppImages.appLogo,
            height: 94,
            // width: 89,
          ),
          Space.height(60),
          Image.asset(
            AppImages.appUpdate,
            height: 264,
            // color: BaseTheme,
          ),
          Space.height(50),
          Text(
            "Application Update Available",
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          Space.height(10),
          // Text(
          //   "Please update app for new features ",
          //   style: TextStyle(fontSize: 16, color: BaseTheme().secondaryColor, fontWeight: FontWeight.w400),
          //   textAlign: TextAlign.center,
          // ),
          Space.height(30),
          Obx(() => controller.isSoftUpdate.value == false
              ? appButton(
                  btnText: "Update Now",
                  width: 150,
                  onTap: () async {
                    if (Platform.isAndroid) {
                      if (!await launchUrl(Uri.parse(playStoreLink))) {
                        throw Exception('Could not launch $playStoreLink');
                      }
                    } else {
                      if (!await launchUrl(Uri.parse(appStoreLink))) {
                        throw Exception('Could not launch $playStoreLink');
                      }
                    }
                  })
              : Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    children: [
                      Expanded(
                        child: appButton(
                            btnText: "Update Now",
                            width: 150,
                            onTap: () async {
                              if (Platform.isAndroid) {
                                if (!await launchUrl(Uri.parse(playStoreLink))) {
                                  throw Exception('Could not launch $playStoreLink');
                                }
                              } else {
                                if (!await launchUrl(Uri.parse(appStoreLink))) {
                                  throw Exception('Could not launch $playStoreLink');
                                }
                              }
                            }),
                      ),
                      Space.width(20),
                      Expanded(
                        child: appButton(
                            btnText: "Not now",
                            width: 150,
                            onTap: () {
                              Get.offAllNamed((!isNullEmptyOrFalse(box.read(Constant.tokenKey)))
                                  ? Routes.homeScreen
                                  : Routes.loginScreen);
                            }),
                      ),
                    ],
                  ),
                ))
        ],
      ),
    ));
  }
}
