import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../Models/booking_data_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';

class AllMyBookingController extends GetxController {
  //TODO: Implement AllMyBookingController
  RxBool hasData = false.obs;
  BookingDataModel? bookingDataModel;
  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      callApiForGetBookingList(context: Get.context!);
    });
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  callApiForGetBookingList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //  app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.bookingList}?past=false",
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        bookingDataModel = BookingDataModel.fromJson(response);
        print(bookingDataModel?.data?.length.toString());
      },
      failureCallback: (status, message) {
        hasData.value = true;

        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForDeleteBookingList({required BuildContext context, int? id}) {
    FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    print(id.toString());
    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.bookingList}/$id",
      MethodType.delete,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        hasData.value = false;
        callApiForGetBookingList(context: context);
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
