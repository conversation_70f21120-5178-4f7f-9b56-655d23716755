import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';

import '../../../../constants/color_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../constants/size_constant.dart';
import '../../../../utilities/submit_button.dart';
import '../../../../utilities/utilities.dart';
import '../../../routes/app_pages.dart';
import '../controllers/all_my_booking_controller.dart';

class AllMyBookingView extends GetWidget<AllMyBookingController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  right: MySize.size13!,
                  top: MySize.size17!,
                  bottom: MySize.size17!),
              child: SvgPicture.asset(
                "assets/arrow_back.svg",
              ),
            ),
          ),
          title: Container(
            // color: Colors.red,
            width: MySize.getScaledSizeWidth(192),
            height: MySize.getScaledSizeHeight(55),
            child: Image(
              image: AssetImage("assets/logo1.jpg"),
              fit: BoxFit.fill,
            ),
          ),
          actions: [
            InkWell(
              onTap: () {
                Get.toNamed(Routes.notification);
              },
              child: Padding(
                padding: EdgeInsets.only(
                    left: MySize.getScaledSizeWidth(15),
                    right: MySize.getScaledSizeWidth(15),
                    top: MySize.size17!,
                    bottom: MySize.size17!),
                child: Image(
                  image: AssetImage("assets/notification.png"),
                ),
              ),
            ),
          ],
          centerTitle: true,
          elevation: 0,
        ),
        body: Container(
          width: MySize.screenWidth,
          height: MySize.screenHeight,
          padding: EdgeInsets.symmetric(
              horizontal: MySize.getScaledSizeWidth(15),
              vertical: MySize.size20!),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "All Booking",
                style: TextStyle(
                  fontSize: MySize.size20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Expanded(
                  child: (!controller.hasData.value)
                      ? Center(
                          child: getShimerForBookingList(),
                        )
                      : ((controller.bookingDataModel != null &&
                              controller.bookingDataModel!.data != null &&
                              controller.bookingDataModel!.data!.isNotEmpty)
                          ? Container(
                              padding: EdgeInsets.only(top: MySize.size15!),
                              child: ListView.separated(
                                itemBuilder: (context, i) {
                                  return InkWell(
                                    onTap: () {
                                      Get.toNamed(Routes.productDetail,
                                          arguments: {
                                            "isBooked": true,
                                            StringConstant.productData:
                                                controller.bookingDataModel!
                                                    .data![i].resource,
                                            StringConstant.bookingModel:
                                                controller
                                                    .bookingDataModel!.data![i],
                                          });
                                    },
                                    child: Column(
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                              border: Border.all(
                                                  color: appTheme.borderColor),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      MySize.size10!)),
                                          padding: EdgeInsets.symmetric(
                                            horizontal:
                                                MySize.getScaledSizeWidth(10),
                                            vertical: MySize.size10!,
                                          ),
                                          child: Row(
                                            children: [
                                              ClipRRect(
                                                // child: Image(
                                                //   image: AssetImage(
                                                //       "assets/table.png"),
                                                //   fit: BoxFit.cover,
                                                //   height: MySize.size140,
                                                //   width: MySize.size140,
                                                // ),
                                                child: CommonNetworkImageView(
                                                  url: (controller
                                                              .bookingDataModel!
                                                              .data![i]
                                                              .resource!
                                                              .imageUrl !=
                                                          null)
                                                      ? controller
                                                          .bookingDataModel!
                                                          .data![i]
                                                          .resource!
                                                          .imageUrl
                                                          .toString()
                                                      : "",
                                                  height: MySize.size140!,
                                                  width: MySize.size140!,
                                                  fit: BoxFit.cover,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              SizedBox(
                                                width:
                                                    MySize.getScaledSizeWidth(
                                                        7),
                                              ),
                                              Expanded(
                                                child: Container(
                                                  // height: MySize.size140,
                                                  width: MySize.size140,
                                                  child: Column(
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text(
                                                            "#${controller
                                                                    .bookingDataModel!
                                                                    .data![i]
                                                                    .id}",
                                                            style: TextStyle(
                                                              color: appTheme
                                                                  .textGrayColor,
                                                              fontSize:
                                                                  MySize.size16,
                                                            ),
                                                          ),
                                                          Text(
                                                            "\$${controller.bookingDataModel!.data![i].resource!.ratePerHour} /Hour",
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize:
                                                                  MySize.size16,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            controller
                                                                .bookingDataModel!
                                                                .data![i]
                                                                .resource!
                                                                .name
                                                                .toString(),
                                                            style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize:
                                                                  MySize.size16,
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            height:
                                                                MySize.size10,
                                                          ),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .start,
                                                            children: [
                                                              SvgPicture.asset(
                                                                "assets/location.svg",
                                                                height: MySize
                                                                    .size14,
                                                                color: appTheme
                                                                    .textGrayColor,
                                                              ),
                                                              Expanded(
                                                                child: Text(
                                                                  " ${controller.bookingDataModel!.data![i].resource!.address.toString()}",
                                                                  style: TextStyle(
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .normal,
                                                                      fontSize:
                                                                          MySize
                                                                              .size16,
                                                                      color: appTheme
                                                                          .textGrayColor),
                                                                  maxLines: 4,
                                                                ),
                                                              ),
                                                            ],
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                          ),
                                                          SizedBox(
                                                            height:
                                                                MySize.size8,
                                                          ),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .spaceBetween,
                                                            children: [
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  SvgPicture
                                                                      .asset(
                                                                    "assets/date.svg",
                                                                    height: MySize
                                                                        .size14,
                                                                  ),
                                                                  (controller
                                                                              .bookingDataModel!
                                                                              .data![i]
                                                                              .startAt !=
                                                                          null)
                                                                      ? Text(
                                                                          " ${DateFormat("MM.dd.yyyy").format(getDateFromStringFromUtc(
                                                                                controller.bookingDataModel!.data![i].startAt.toString(),
                                                                              ).toLocal())} | ${DateFormat("hh:mm a").format(getDateFromStringFromUtc(
                                                                                controller.bookingDataModel!.data![i].startAt.toString(),
                                                                              ).toLocal())}",
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                MySize.size16,
                                                                          ),
                                                                        )
                                                                      : SizedBox(),
                                                                ],
                                                              ),
                                                              Row(
                                                                mainAxisAlignment:
                                                                    MainAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  SvgPicture
                                                                      .asset(
                                                                    "assets/time.svg",
                                                                    height: MySize
                                                                        .size14,
                                                                  ),
                                                                  Text(
                                                                    " ${getDateFromString(
                                                                          controller
                                                                              .bookingDataModel!
                                                                              .data![i]
                                                                              .endAt
                                                                              .toString(),
                                                                        )
                                                                            .difference(getDateFromString(
                                                                              controller.bookingDataModel!.data![i].startAt.toString(),
                                                                            ))
                                                                            .inHours} H",
                                                                    style: TextStyle(
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .normal,
                                                                        fontSize:
                                                                            MySize
                                                                                .size16,
                                                                        color: appTheme
                                                                            .textGrayColor),
                                                                  ),
                                                                ],
                                                              ),
                                                            ],
                                                          ),
                                                          SizedBox(
                                                            height:
                                                                MySize.size8,
                                                          ),
                                                          Row(
                                                            children: [
                                                              if (getButtonVisibleOrNot(
                                                                  c: controller,
                                                                  i: i))
                                                                InkWell(
                                                                  child: button(
                                                                      title:
                                                                          "Cancel",
                                                                      backgroundColor:
                                                                          Colors
                                                                              .white,
                                                                      textColor:
                                                                          appTheme
                                                                              .primaryTheme,
                                                                      borderColor:
                                                                          appTheme
                                                                              .primaryTheme,
                                                                      width: 90,
                                                                      height:
                                                                          35),
                                                                  onTap: () {
                                                                    // c.callApiForDeleteBookingList(
                                                                    //     context:
                                                                    //         context,
                                                                    //     id: c
                                                                    //         .bookingDataModel!
                                                                    //         .data![i]
                                                                    //         .id);
                                                                    _asyncConfirmDialog(
                                                                        context,
                                                                        i);
                                                                  },
                                                                ),
                                                              if (getButtonVisibleOrNot(
                                                                  c: controller,
                                                                  i: i))
                                                                SizedBox(
                                                                  width: 10,
                                                                ),
                                                              InkWell(
                                                                onTap: () {
                                                                  Get.toNamed(
                                                                      Routes
                                                                          .productDetail,
                                                                      arguments: {
                                                                        "isBooked":
                                                                            true,
                                                                        "isBooked":
                                                                            true,
                                                                        StringConstant.productData: controller
                                                                            .bookingDataModel!
                                                                            .data![i]
                                                                            .resource,
                                                                        StringConstant.bookingModel: controller
                                                                            .bookingDataModel!
                                                                            .data![i],
                                                                      });
                                                                },
                                                                child: button(
                                                                    title:
                                                                        "View",
                                                                    width: 130,
                                                                    height: 35),
                                                              ),
                                                            ],
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                          )
                                                        ],
                                                      )
                                                    ],
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                        Container(
                                          width: double.infinity,
                                          child: SfRangeSlider(
                                            activeColor: Colors.black,
                                            inactiveColor: Colors.grey,
                                            showDividers: true,
                                            labelPlacement:
                                                LabelPlacement.onTicks,
                                            enableTooltip: true,
                                            min: DateTime(
                                                2000,
                                                01,
                                                01,
                                                getDateFromString(
                                                        controller
                                                            .bookingDataModel!
                                                            .data![i]
                                                            .resource!
                                                            .openAt
                                                            .toString(),
                                                        formatter: 'HH:mm:ss')
                                                    .hour,
                                                getDateFromString(
                                                        controller
                                                            .bookingDataModel!
                                                            .data![i]
                                                            .resource!
                                                            .openAt
                                                            .toString(),
                                                        formatter: 'HH:mm:ss')
                                                    .minute),
                                            max: DateTime(
                                                2000,
                                                01,
                                                01,
                                                getDateFromString(
                                                        controller
                                                            .bookingDataModel!
                                                            .data![i]
                                                            .resource!
                                                            .closeAt
                                                            .toString(),
                                                        formatter: 'HH:mm:ss')
                                                    .hour,
                                                getDateFromString(
                                                        controller
                                                            .bookingDataModel!
                                                            .data![i]
                                                            .resource!
                                                            .closeAt
                                                            .toString(),
                                                        formatter: 'HH:mm:ss')
                                                    .hour),
                                            values: SfRangeValues(
                                                DateTime(
                                                    2000,
                                                    01,
                                                    01,
                                                    getDateFromStringFromUtc(
                                                      controller
                                                          .bookingDataModel!
                                                          .data![i]
                                                          .startAt
                                                          .toString(),
                                                    ).toLocal().hour,
                                                    getDateFromStringFromUtc(
                                                      controller
                                                          .bookingDataModel!
                                                          .data![i]
                                                          .startAt
                                                          .toString(),
                                                    ).toLocal().minute),
                                                DateTime(
                                                    2000,
                                                    01,
                                                    01,
                                                    getDateFromStringFromUtc(
                                                      controller
                                                          .bookingDataModel!
                                                          .data![i]
                                                          .endAt
                                                          .toString(),
                                                    ).toLocal().hour,
                                                    getDateFromStringFromUtc(
                                                      controller
                                                          .bookingDataModel!
                                                          .data![i]
                                                          .endAt
                                                          .toString(),
                                                    ).toLocal().minute)),
                                            // interval: (getDateFromString(
                                            //                     controller
                                            //                         .bookingDataModel!
                                            //                         .data![i]
                                            //                         .resource!
                                            //                         .closeAt
                                            //                         .toString(),
                                            //                     formatter:
                                            //                         'HH:mm:ss')
                                            //                 .difference(
                                            //                     getDateFromStringFromUtc(
                                            //                   controller
                                            //                       .bookingDataModel!
                                            //                       .data![i]
                                            //                       .startAt
                                            //                       .toString(),
                                            //                 ))
                                            //                 .inHours %
                                            //             2 ==
                                            //         0)
                                            //     ? 2
                                            //     : (getDateFromString(
                                            //                         controller
                                            //                             .bookingDataModel!
                                            //                             .data![
                                            //                                 i]
                                            //                             .resource!
                                            //                             .closeAt
                                            //                             .toString(),
                                            //                         formatter:
                                            //                             'HH:mm:ss')
                                            //                     .difference(
                                            //                         getDateFromStringFromUtc(
                                            //                       controller
                                            //                           .bookingDataModel!
                                            //                           .data![i]
                                            //                           .startAt
                                            //                           .toString(),
                                            //                     ))
                                            //                     .inHours %
                                            //                 3 ==
                                            //             0)
                                            //         ? 3
                                            //         : 4,
                                            interval: 4,
                                            showTicks: true,
                                            showLabels: true,
                                            shouldAlwaysShowTooltip: false,
                                            dateFormat:
                                                DateFormat('hh:mm\n  a'),
                                            dateIntervalType:
                                                DateIntervalType.hours,
                                            onChanged:
                                                (SfRangeValues newValues) {},
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                },
                                separatorBuilder: (context, i) {
                                  return SizedBox(
                                    height: MySize.size30,
                                  );
                                },
                                itemCount:
                                    controller.bookingDataModel!.data!.length,
                              ),
                            )
                          : Center(
                              child: Text(
                                "No booking data found",
                                style: TextStyle(fontSize: MySize.size14!),
                              ),
                            ))),
            ],
          ),
        ),
      );
    });
  }

  getButtonVisibleOrNot({AllMyBookingController? c, int i = 0}) {
    if (c!.bookingDataModel!.data![i].plan!) {
      return false;
    } else {
      if (getDateFromString(c.bookingDataModel!.data![i].createdAt!)
          .add(Duration(days: 1))
          .isAfter(DateTime.now())) {
        return true;
      } else {
        return false;
      }
    }
  }

  _asyncConfirmDialog(BuildContext context, i) async {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button for close dialog!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Cancel Booking'),
          content: const Text('Are you sure cancel this booking?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text(
                'Ok',
                style: TextStyle(
                  color: Colors.white,
                ),
              ),
              style: TextButton.styleFrom(backgroundColor: Colors.red),
              // color: Colors.red,

              onPressed: () {
                Navigator.of(context).pop();
                controller.callApiForDeleteBookingList(
                    context: context,
                    id: controller.bookingDataModel!.data![i].id);
              },
            )
          ],
        );
      },
    );
  }
}
