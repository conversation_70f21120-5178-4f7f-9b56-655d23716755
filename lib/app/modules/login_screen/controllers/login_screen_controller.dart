import 'dart:convert';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import '../../../../constants/api_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';

class LoginScreenController extends GetxController {
  //TODO: Implement LoginScreenController
  Rx<TextEditingController> mobileNumber = TextEditingController().obs;
  final count = 0.obs;
  Rx<CountryCode> countryCode = CountryCode.fromCode("US").obs;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  /*@override
  void onInit() {
    super.onInit();
  }*/

  /*@override
  void onReady() {
    super.onReady();
  }*/

  String sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<User> signInWithApple() async {
    final _firebaseAuth = FirebaseAuth.instance;

    final rawNonce = generateNonce();
    final nonce = sha256ofString(rawNonce);

    // 1. perform the sign-in request
    final credential = await SignInWithApple.getAppleIDCredential(scopes: [
      AppleIDAuthorizationScopes.email,
      AppleIDAuthorizationScopes.fullName,
    ], nonce: nonce);

    final oauthCredential = OAuthProvider("apple.com").credential(
      idToken: credential.identityToken,
      rawNonce: rawNonce,
    );
    final userCredential =
        await _firebaseAuth.signInWithCredential(oauthCredential);

    final firebaseUser = userCredential.user!;

    return firebaseUser;
  }

  callApiForRegisterFirebaseToken(
      {required BuildContext context,
      String? token,
      User? user,
      bool isGoogle = false}) {
    FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();
    dict["firebase_token"] = token;

    return NetworkClient.getInstance.callApi(
      context,
      authUrl,
      ApiConstant.verifyUser,
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        AuthModel authModel = AuthModel.fromJson(response);
        if (authModel.userType == "new") {
          Get.toNamed(Routes.registrationScreen, arguments: {
            "userData": user,
            "logInType": (isGoogle) ? "google" : "apple",
            "token": authModel.token
          });
        } else {
          box.write(Constant.tokenKey, authModel.token);
          box.write(Constant.loginMethod, (isGoogle) ? "google" : "apple");

          Get.offAllNamed(Routes.homeScreen);
        }
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app.resolve<CustomDialogs>().getDialog(title: "Failed", desc: message);

        print(" error");
      },
    );
  }

  Future<User?> signInWithGoogle({required BuildContext context}) async {
    FirebaseAuth auth = FirebaseAuth.instance;
    User? user;

    final GoogleSignIn googleSignIn = GoogleSignIn();

    final GoogleSignInAccount? googleSignInAccount =
        await googleSignIn.signIn();

    if (googleSignInAccount != null) {
      final GoogleSignInAuthentication googleSignInAuthentication =
          await googleSignInAccount.authentication;

      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleSignInAuthentication.accessToken,
        idToken: googleSignInAuthentication.idToken,
      );

      try {
        final UserCredential userCredential =
            await auth.signInWithCredential(credential);

        user = userCredential.user;
      } on FirebaseAuthException catch (e) {
        if (e.code == 'account-exists-with-different-credential') {
          ScaffoldMessenger.of(context).showSnackBar(
            customSnackBar(
              content: 'The account already exists with a different credential',
            ),
          );
        } else if (e.code == 'invalid-credential') {
          ScaffoldMessenger.of(context).showSnackBar(
            customSnackBar(
              content: 'Error occurred while accessing credentials. Try again.',
            ),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          customSnackBar(
            content: 'Error occurred using Google Sign In. Try again.',
          ),
        );
      }
    }

    return user;
  }

  static SnackBar customSnackBar({required String content}) {
    return SnackBar(
      backgroundColor: Colors.black,
      content: Text(
        content,
        style: TextStyle(color: Colors.redAccent, letterSpacing: 0.5),
      ),
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}

class AuthModel {
  int? status;
  String? message;
  String? userType;
  String? token;

  AuthModel({this.status, this.message, this.userType, this.token});

  AuthModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    userType = json['userType'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['userType'] = userType;
    data['token'] = token;
    return data;
  }
}
