import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:small_officer/utilities/text_field.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../controllers/login_screen_controller.dart';

class LoginScreenView extends GetWidget<LoginScreenController> {
  @override
  Widget build(BuildContext context) {
    MySize().init(context);
    return Scaffold(
      body: Stack(
        children: [
          Container(
            height: MySize.screenHeight,
            width: MySize.screenWidth,
            child: SingleChildScrollView(
              child: Container(
                child: Form(
                  key: controller.formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: MySize.getScaledSizeWidth(36),
                          right: MySize.getScaledSizeWidth(36),
                          top: MySize.getScaledSizeHeight(280),
                        ),
                        child: Container(
                          // color: Colors.red,
                          width: MySize.getScaledSizeWidth(350),
                          height: MySize.getScaledSizeHeight(90),
                          child: Image(
                            image: AssetImage("assets/logo1.jpg"),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                          left: MySize.getScaledSizeWidth(36),
                          right: MySize.getScaledSizeWidth(36),
                          top: MySize.getScaledSizeHeight(70),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  "assets/login_icon.svg",
                                  height: MySize.size20,
                                  width: MySize.getScaledSizeWidth(20),
                                ),
                                SizedBox(
                                  width: MySize.getScaledSizeWidth(10),
                                ),
                                Text(
                                  "Login",
                                  style: TextStyle(fontSize: MySize.size20),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: MySize.size18,
                            ),
                            getTextFormField(
                                prefixIcon: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Text(
                                    "+1",
                                    style: TextStyle(
                                      fontSize: MySize.size16,
                                    ),
                                  ),
                                ),
                                // prefixIcon: countryCodePicker(onChange: (v) {
                                //   if (v != null) {
                                //     controller.countryCode.value = v;
                                //   }
                                // }),
                                textInputType: TextInputType.number,
                                hintText: "Mobile Number",
                                textEditingController: controller.mobileNumber.value,
                                validation: (val) {
                                  if (isNullEmptyOrFalse(val)) {
                                    return "Please Enter Mobile Number";
                                  } else if (val!.length != 10) {
                                    return "Please Enter Valid Mobile Number";
                                  }
                                  return null;
                                }
                                //  formator: [MaskedInputFormater('(###) ###-####')],
                                ),
                            // GestureDetector(
                            //   onTap: () {
                            //     String token =
                            //         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6Miwicm9sZSI6IlVzZXIiLCJpYXQiOjE3MDY1MTQ1MDYsImV4cCI6MTczODA1MDUwNn0.E8sA_JWp9XqwQu7NjQq3z389O23KK18lKDynMOxJawQ";

                            //     box.write(Constant.tokenKey, token);
                            //   },
                            //   child: SizedBox(
                            //     child: Icon(Icons.abc),
                            //     height: MySize.size50,
                            //   ),
                            // ),
                            InkWell(
                              onTap: () {
                                Get.toNamed(Routes.termsCondition);
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    "By Accepting",
                                    style: TextStyle(
                                        fontSize: MySize.size14, color: BaseTheme().secondaryColor.withOpacity(0.8)),
                                  ),
                                  Text(
                                    " Terms & Condition",
                                    style: TextStyle(fontSize: MySize.size14, color: BaseTheme().primaryTheme),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: MySize.size14,
                            ),
                            InkWell(
                              onTap: () async {
                                if (controller.formKey.currentState!.validate()) {
                                  app.resolve<CustomDialogs>().showCircularDialog(context);

                                  await FirebaseAuth.instance
                                      .verifyPhoneNumber(
                                    phoneNumber:
                                        "${controller.countryCode.value.dialCode}${controller.mobileNumber.value.text}",
                                    verificationCompleted: (PhoneAuthCredential credential) {},
                                    verificationFailed: (FirebaseAuthException e) {
                                      app.resolve<CustomDialogs>().hideCircularDialog(context);

                                      if (e.code == 'invalid-phone-number') {
                                        app.resolve<CustomDialogs>().getDialog(
                                            title: "Phone Number Invalid", desc: "Please Check Your Phone Number.");
                                      } else if (e.code == "too-many-requests") {
                                        app.resolve<CustomDialogs>().getDialog(
                                            title: "Too many requests",
                                            desc:
                                                "We have blocked all requests from this device due to unusual activity. Try again later");
                                        // errorSnackBar("Too many requests",
                                        //     "We have blocked all requests from this device due to unusual activity. Try again later");
                                      }
                                    },
                                    codeSent: (String verificationId, int? resendToken) {
                                      app.resolve<CustomDialogs>().hideCircularDialog(context);

                                      // Get.to(
                                      //         () => OtpScreen(verificationId, widget.link));
                                      Get.toNamed(Routes.passwordScreen, arguments: {
                                        StringConstant.verificationId: verificationId,
                                        StringConstant.resendToken: resendToken,
                                        StringConstant.mobileNumber: controller.countryCode.value.dialCode.toString() +
                                            controller.mobileNumber.value.text,
                                      });
                                    },
                                    codeAutoRetrievalTimeout: (String verificationId) {
                                      app.resolve<CustomDialogs>().hideCircularDialog(context);

                                      print("CART ::: $verificationId");
                                    },
                                  )
                                      .catchError((e) {
                                    app.resolve<CustomDialogs>().hideCircularDialog(context);

                                    print(e.toString());
                                  });
                                }
                                //Get.toNamed(Routes.HOME_SCREEN);
                                //Get.toNamed(Routes.PASSWORD_SCREEN);

                                // try {
                                //   UserCredential userCredential = await FirebaseAuth
                                //       .instance
                                //       .createUserWithEmailAndPassword(
                                //           email: "<EMAIL>",
                                //           password: "Admin1234");
                                //   final user =
                                //       await FirebaseAuth.instance.currentUser!;
                                //   final idToken = await user.getIdToken();
                                //   final token = idToken;
                                //   print(userCredential);
                                //   // if (!isNullEmptyOrFal se(token)) {
                                //   //   callLoginAPI(context, token);
                                //   // }
                                // } on FirebaseAuthException catch (e) {
                                //   if (e.code == 'weak-password') {
                                //     print('The password provided is too weak.');
                                //   } else if (e.code == 'email-already-in-use') {}
                                // } catch (e) {
                                //   print(e);
                                // }
                              },
                              child: Container(
                                height: MySize.getScaledSizeWidth(50),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                    color: BaseTheme().newPrimaryColor,
                                    borderRadius: BorderRadius.circular(MySize.size5!)),
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        "Let's Start",
                                        style: TextStyle(color: Colors.white, fontSize: MySize.size16),
                                      ),
                                      SizedBox(
                                        width: MySize.getScaledSizeWidth(5),
                                      ),
                                      Icon(
                                        Icons.arrow_forward,
                                        color: Colors.white,
                                        size: MySize.getScaledSizeHeight(20),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              height: MySize.size40,
                            ),
                            // Row(
                            //   children: [
                            //     Expanded(
                            //       child: Container(
                            //         height: MySize.size1,
                            //         color: Colors.red,
                            //       ),
                            //     ),
                            //     Text(
                            //       "  Or  ",
                            //       style: TextStyle(
                            //           color: Colors.red,
                            //           fontSize: MySize.size14),
                            //     ),
                            //     Expanded(
                            //       child: Container(
                            //         height: MySize.size1,
                            //         color: Colors.red,
                            //       ),
                            //     ),
                            //   ],
                            // ),
                            // SizedBox(
                            //   height: MySize.size20,
                            // ),
                            //   Row(
                            //     children: [
                            //       if (Platform.isIOS)
                            //         Expanded(
                            //           child: InkWell(
                            //             onTap: () {
                            //               controller
                            //                   .signInWithApple()
                            //                   .then((value) async {
                            //                 if (value != null) {
                            //                   String? token =
                            //                       await value.getIdToken();
                            //                   controller
                            //                       .callApiForRegisterFirebaseToken(
                            //                           context: context,
                            //                           token: token,
                            //                           user: value);
                            //                 }
                            //                 // authController
                            //                 //         .emailController.value.text =
                            //                 //     value.email ??
                            //                 //         value.providerData.first.email!;
                            //                 // authController.fullName.value.text =
                            //                 //     value.displayName ?? "";
                            //                 // authController.readOnlyEmail.value = true;
                            //               });
                            //             },
                            //             child: Container(
                            //               height: MySize.size44,
                            //               decoration: BoxDecoration(
                            //                   borderRadius: BorderRadius.circular(
                            //                       MySize.size4!),
                            //                   border: Border.all(
                            //                     color: BaseTheme().borderColor,
                            //                   )),
                            //               child: Center(
                            //                 child: Row(
                            //                   mainAxisAlignment:
                            //                       MainAxisAlignment.center,
                            //                   crossAxisAlignment:
                            //                       CrossAxisAlignment.center,
                            //                   children: [
                            //                     SvgPicture.asset(
                            //                       "assets/apple.svg",
                            //                       height: MySize.size19,
                            //                     ),
                            //                     SizedBox(
                            //                       width:
                            //                           MySize.getScaledSizeWidth(
                            //                               7),
                            //                     ),
                            //                     Text(
                            //                       "Apple",
                            //                       style: TextStyle(
                            //                           fontSize: MySize.size19),
                            //                     ),
                            //                   ],
                            //                 ),
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //       if (Platform.isIOS)
                            //         SizedBox(
                            //           width: MySize.getScaledSizeWidth(15),
                            //         ),
                            //       Expanded(
                            //         child: InkWell(
                            //           onTap: () {
                            //             controller
                            //                 .signInWithGoogle(context: context)
                            //                 .then((value) async {
                            //               if (value != null) {
                            //                 String? token =
                            //                     await value.getIdToken();
                            //                 controller
                            //                     .callApiForRegisterFirebaseToken(
                            //                         context: context,
                            //                         token: token,
                            //                         isGoogle: true,
                            //                         user: value);
                            //               }
                            //             });
                            //           },
                            //           child: Container(
                            //             height: MySize.size44,
                            //             decoration: BoxDecoration(
                            //                 borderRadius: BorderRadius.circular(
                            //                     MySize.size4!),
                            //                 border: Border.all(
                            //                   color: BaseTheme().borderColor,
                            //                 )),
                            //             child: Center(
                            //               child: Row(
                            //                 mainAxisAlignment:
                            //                     MainAxisAlignment.center,
                            //                 crossAxisAlignment:
                            //                     CrossAxisAlignment.center,
                            //                 children: [
                            //                   SvgPicture.asset(
                            //                     "assets/google.svg",
                            //                     height: MySize.size19,
                            //                   ),
                            //                   SizedBox(
                            //                     width:
                            //                         MySize.getScaledSizeWidth(7),
                            //                   ),
                            //                   Text(
                            //                     "Google",
                            //                     style: TextStyle(
                            //                         fontSize: MySize.size19),
                            //                   ),
                            //                 ],
                            //               ),
                            //             ),
                            //           ),
                            //         ),
                            //       ),
                            //     ],
                            //   )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            child: Container(
              width: MySize.getScaledSizeWidth(240),
              height: MySize.getScaledSizeHeight(200),
              color: appTheme.newPrimaryColor,
              child: Stack(
                children: [
                  Positioned(
                    bottom: 50,
                    child: Image.asset(
                      "assets/v20.png",
                      height: MySize.size40,
                      width: 27,
                    ),
                  ),
                  Positioned(
                    bottom: 10,
                    child: Container(
                      width: MySize.getScaledSizeWidth(240),
                      child: Center(
                          child: Text(
                        "First, Let's make\nan account",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: MySize.size24,
                        ),
                      )),
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
