import 'package:get/get.dart';
import 'package:small_officer/constants/constant.dart';

import '../../../../Models/invoice_list_model.dart';

class InvoiceDetailController extends GetxController {
  //TODO: Implement InvoiceDetailController
  Invoice? invoice;
  final count = 0.obs;
  @override
  void onInit() {
    if (Get.arguments != null) {
      invoice = Get.arguments[Constant.invoiceData];
    }
    super.onInit();
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}
  void increment() => count.value++;
}
