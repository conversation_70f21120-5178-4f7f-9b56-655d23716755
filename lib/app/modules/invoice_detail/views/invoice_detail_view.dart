import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/utilities/submit_button.dart';
import '../../../../constants/color_constant.dart';
import '../../../../constants/size_constant.dart';
import '../../../../utilities/file_handle_api.dart';
import '../../../../utilities/pdf_invoice_api.dart';
import '../../../../utilities/utilities.dart';
import '../../../routes/app_pages.dart';
import '../controllers/invoice_detail_controller.dart';

class InvoiceDetailView extends GetWidget<InvoiceDetailController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Padding(
            padding: EdgeInsets.only(left: MySize.getScaledSizeWidth(15), top: MySize.size17!, bottom: MySize.size17!),
            child: SvgPicture.asset(
              "assets/arrow_back.svg",
            ),
          ),
        ),
        title: Container(
          // color: Colors.red,
          width: MySize.getScaledSizeWidth(192),
          height: MySize.getScaledSizeHeight(55),
          child: Image(
            image: AssetImage("assets/logo1.jpg"),
            fit: BoxFit.fill,
          ),
        ),
        actions: [
          InkWell(
            onTap: () {
              Get.toNamed(Routes.notification);
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  right: MySize.getScaledSizeWidth(15),
                  top: MySize.size15!,
                  bottom: MySize.size15!),
              child: Image(
                image: AssetImage("assets/notification.png"),
              ),
            ),
          ),
        ],
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        height: MySize.screenHeight,
        width: MySize.screenWidth,
        padding: EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(15), vertical: MySize.size15!),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Invoice",
              style: TextStyle(
                fontSize: MySize.size20!,
                color: appTheme.primaryTheme,
                fontWeight: FontWeight.w700,
              ),
            ),
            SizedBox(
              height: MySize.size10!,
            ),
            getDivider(),
            SizedBox(
              height: MySize.size10!,
            ),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  // color: appTheme.fillColor,
                  // borderRadius: BorderRadius.circular(MySize.size4!),
                  // border: Border.all(
                  //   color: appTheme.borderColor,
                  // ),
                  ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "#${controller.invoice!.id}",
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: MySize.size16!,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        (controller.invoice!.isPaid!) ? "Paid" : "Unpaid",
                        style: TextStyle(
                          fontSize: MySize.size18!,
                          color: (controller.invoice!.isPaid!) ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (!controller.invoice!.isPaid!)
                        SizedBox(
                          height: MySize.size8,
                        ),
                      if (!controller.invoice!.isPaid!)
                        Row(
                          children: [
                            Text(
                              "Due date: ",
                              style: TextStyle(
                                fontSize: MySize.size14!,
                                color: Colors.red,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                            Text(
                              DateFormat("dd MMM yyyy").format(getDateFromString(
                                controller.invoice!.dueDate.toString(),
                              )),
                              style: TextStyle(
                                fontSize: MySize.size14!,
                                color: appTheme.primaryTheme,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              height: MySize.size12,
            ),
            getDivider(),
            SizedBox(
              height: MySize.size10,
            ),
            Text(
              "Bill to",
              style: TextStyle(
                fontSize: MySize.size16!,
                color: appTheme.textGrayColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: MySize.size8,
            ),
            Text(
              (isNullEmptyOrFalse(controller.invoice!.booking!))
                  ? "N/A"
                  : (controller.invoice!.booking!.user!.firstName.toString() +
                      ((isNullEmptyOrFalse(controller.invoice!.booking!.user!.lastName)
                          ? ""
                          : controller.invoice!.booking!.user!.lastName.toString()))),
              style: TextStyle(
                fontSize: MySize.size16!,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: MySize.size10,
            ),
            getDivider(),
            SizedBox(
              height: MySize.size10,
            ),
            Text(
              "Date",
              style: TextStyle(
                fontSize: MySize.size16!,
                color: appTheme.textGrayColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: MySize.size8,
            ),
            Text(
              (isNullEmptyOrFalse(controller.invoice!.createdAt!))
                  ? "N/A"
                  : DateFormat("dd MMM, yyyy hh:mm a").format(getDateFromString(
                      controller.invoice!.createdAt.toString(),
                    )),
              style: TextStyle(
                fontSize: MySize.size16!,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: MySize.size10,
            ),
            getDivider(),
            SizedBox(
              height: MySize.size10,
            ),
            Text(
              "Terms",
              style: TextStyle(
                fontSize: MySize.size16!,
                color: appTheme.textGrayColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: MySize.size8,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  (isNullEmptyOrFalse(controller.invoice!.booking!.resource))
                      ? "N/A"
                      : controller.invoice!.booking!.resource!.name.toString(),
                  style: TextStyle(
                    fontSize: MySize.size16!,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  "\$ ${(isNullEmptyOrFalse(controller.invoice!.booking!.resource)) ? "-" : controller.invoice!.booking!.resource!.ratePerHour.toString()}/hr",
                  style: TextStyle(
                    fontSize: MySize.size16!,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: MySize.size10,
            ),
            getDivider(),
            SizedBox(
              height: MySize.size10,
            ),
            Text(
              "Total hours",
              style: TextStyle(
                fontSize: MySize.size16!,
                color: appTheme.textGrayColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            SizedBox(
              height: MySize.size8,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  (isNullEmptyOrFalse(controller.invoice!.booking)
                      ? "-"
                      : getDateFromStringFromUtc(
                          controller.invoice!.booking!.endAt.toString(),
                        )
                          .toLocal()
                          .difference(getDateFromStringFromUtc(
                            controller.invoice!.booking!.startAt.toString(),
                          ).toLocal())
                          .inHours
                          .toString()),
                  style: TextStyle(
                    fontSize: MySize.size16!,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  "\$${(isNullEmptyOrFalse(controller.invoice!)) ? "-" : controller.invoice!.totalAmount.toString()}",
                  style: TextStyle(
                    fontSize: MySize.size16!,
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: MySize.size10,
            ),
            getDivider(),
            SizedBox(
              height: MySize.size20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Total",
                  style: TextStyle(
                    fontSize: MySize.size16!,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  "\$${(isNullEmptyOrFalse(controller.invoice!)) ? "-" : controller.invoice!.totalAmount.toString()}",
                  style: TextStyle(
                    fontSize: MySize.size16!,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Container(),
            ),
            Padding(
              padding: EdgeInsets.symmetric(
                vertical: MySize.size20!,
              ),
              child: InkWell(
                onTap: () async {
                  // reportView(context);
                  final pdfFile = await PdfInvoiceApi.generate(controller.invoice!);
                  Get.snackbar("Success", "Invoice Download Successfully.");

                  // opening the pdf file
                  FileHandleApi.openFile(pdfFile);
                },
                child: button(
                    height: 60,
                    width: double.infinity,
                    textColor: Colors.white,
                    fontsize: 20,
                    title: "Download Invoice"),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Container getDivider() {
    return Container(
      width: double.infinity,
      height: MySize.getScaledSizeHeight(0.5),
      color: appTheme.borderColor,
    );
  }
}
