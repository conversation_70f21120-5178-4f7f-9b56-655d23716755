import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/Models/booking_data_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';

class MyBookingController extends GetxController {
  //TODO: Implement MyBookingController
  RxList<DateModel> dateList = RxList();

  final count = 0.obs;
  RxBool hasList = true.obs;
  RxBool hasDataForPast = true.obs;

  RxBool hasData = false.obs;
  BookingDataModel? bookingDataModel;
  BookingDataModel? bookingPastDataModel;
  DateTime fromDate = DateTime(
      DateTime.now().year, DateTime.now().month, DateTime.now().day, 0, 00, 00);
  DateTime toDate = DateTime(DateTime.now().year, DateTime.now().month,
      DateTime.now().day, 23, 59, 59);
  @override
  void onInit() {
    super.onInit();
    DateFormat formatter = DateFormat('MMM');
    DateFormat dateformatter = DateFormat('EEEE');
    for (int i = 0; i < 7; i++) {
      dateList.add(
        DateModel(
          dateTime: DateTime.now().add(
            Duration(days: i),
          ),
          date: DateTime.now()
              .add(
                Duration(days: i),
              )
              .day,
          month: formatter.format(DateTime.now().add(
            Duration(days: i),
          )),
          weekDay: dateformatter.format(DateTime.now().add(
            Duration(days: i),
          )),
          isActive: (i == 0) ? true : false,
        ),
      );
    }
    print(dateList);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      callApiForGetBookingList(context: Get.context!);
      // callApiForGetPastBookingList(context: Get.context!);
    });
  }

  callApiForGetPastBookingList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //  app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    hasDataForPast.value = false;

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.bookingList}?past=true",
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasDataForPast.value = true;
        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        bookingPastDataModel = BookingDataModel.fromJson(response);
        print(bookingPastDataModel?.data?.length.toString());
      },
      failureCallback: (status, message) {
        hasDataForPast.value = true;

        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForGetBookingList({required BuildContext context}) {
    print('Past...');
    print(fromDate.toIso8601String());
    print(toDate.toIso8601String());
    // FocusScope.of(context).unfocus();
    //  app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    hasData.value = false;

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.bookingList}?from=${fromDate.toIso8601String()}&to=${toDate.toIso8601String()}&past=false",
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        log(jsonEncode(response));

        bookingDataModel = BookingDataModel.fromJson(response);
        print(bookingDataModel?.data?.length.toString());
      },
      failureCallback: (status, message) {
        hasData.value = true;

        // app.resolve<CustomDialogs>().hideCircularDialog(context);

        print(" error");
      },
    );
  }

  callApiForDeleteBookingList({required BuildContext context, int? id}) {
    FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    print(id.toString());
    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      "${ApiConstant.bookingList}/$id",
      MethodType.delete,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        hasData.value = false;
        callApiForGetBookingList(context: context);
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app
            .resolve<CustomDialogs>()
            .getDialog(title: "Error", desc: status["message"]);

        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}

class DateModel {
  int? date;
  DateTime? dateTime;
  String? weekDay;
  String? month;
  bool? isActive = false;
  bool? enabled = true;

  DateModel({
    this.date,
    this.dateTime,
    this.weekDay,
    this.month,
    this.isActive,
    this.enabled,
  });
}
