import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/size_constant.dart';
import 'package:small_officer/utilities/submit_button.dart';

import '../../../../constants/constant.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/my_booking_controller.dart';

class MyBookingView extends GetWidget<MyBookingController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyBookingController>(
      init: MyBookingController(),
      builder: (c) {
        return Obx(() {
          return Scaffold(
            body: Padding(
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
              child: Container(
                padding: EdgeInsets.symmetric(
                    horizontal: MySize.getScaledSizeWidth(15),
                    vertical: MySize.getScaledSizeHeight(10)),
                child: Column(
                  children: [
                    Space.height(10),
                    Container(
                      height: MySize.size40!,
                      child: Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                c.hasList.value = true;

                                c.callApiForGetBookingList(context: context);
                              },
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "My Booking",
                                    style: TextStyle(
                                      color: (c.hasList.value == false)
                                          ? appTheme.primaryTheme
                                          : appTheme.newPrimaryColor,
                                      fontWeight: FontWeight.w500,
                                      fontSize: MySize.size18,
                                    ),
                                  ),
                                  Container(
                                    height: MySize.size2,
                                    color: (c.hasList.value == true)
                                        ? appTheme.newPrimaryColor
                                        : Colors.transparent,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                c.hasList.value = false;
                                c.callApiForGetPastBookingList(
                                    context: context);
                              },
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "Booking History",
                                    style: TextStyle(
                                      color: (c.hasList.value == true)
                                          ? appTheme.primaryTheme
                                          : appTheme.newPrimaryColor,
                                      fontWeight: FontWeight.w500,
                                      fontSize: MySize.size18,
                                    ),
                                  ),
                                  Container(
                                    height: MySize.size2,
                                    color: (c.hasList.value == false)
                                        ? appTheme.newPrimaryColor
                                        : Colors.transparent,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                        child: (c.hasList.value == false)
                            ? getPastTournament(c)
                            : getMyTournament(c)),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  getPastTournament(MyBookingController c) {
    return Column(
      children: [
        Space.height(10),
        Expanded(
          child: (!c.hasDataForPast.value)
              ? Center(
                  child: getShimerForBookingList(),
                )
              : ((c.bookingPastDataModel != null &&
                      c.bookingPastDataModel!.data != null &&
                      c.bookingPastDataModel!.data!.isNotEmpty)
                  ? Container(
                      padding: EdgeInsets.only(top: MySize.size15!),
                      child: ListView.separated(
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, i) {
                          return InkWell(
                            onTap: () {
                              // Get.toNamed(Routes.PRODUCT_DETAIL,
                              //     arguments: {"isBooked": true});
                            },
                            child: Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          color: appTheme.borderColor),
                                      borderRadius: BorderRadius.circular(
                                          MySize.size10!)),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: MySize.getScaledSizeWidth(10),
                                    vertical: MySize.size10!,
                                  ),
                                  child: Row(
                                    children: [
                                      ClipRRect(
                                        // child: Image(
                                        //   image: AssetImage(
                                        //       "assets/table.png"),
                                        //   fit: BoxFit.cover,
                                        //   height: MySize.size140,
                                        //   width: MySize.size140,
                                        // ),
                                        child: CommonNetworkImageView(
                                          url: (c.bookingPastDataModel!.data![i]
                                                      .resource!.imageUrl !=
                                                  null)
                                              ? c.bookingPastDataModel!.data![i]
                                                  .resource!.imageUrl
                                                  .toString()
                                              : "",
                                          height: MySize.size140!,
                                          width: MySize.size140!,
                                          fit: BoxFit.cover,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      SizedBox(
                                        width: MySize.getScaledSizeWidth(7),
                                      ),
                                      Expanded(
                                        child: Container(
                                          // height: MySize.size140,
                                          width: MySize.size140,
                                          child: Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    "#${c.bookingPastDataModel!.data![i].id}",
                                                    style: TextStyle(
                                                      color: appTheme
                                                          .textGrayColor,
                                                      fontSize: MySize.size16,
                                                    ),
                                                  ),
                                                  Text(
                                                    "\$${c.bookingPastDataModel!.data![i].resource!.ratePerHour} /Hour",
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: MySize.size16,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    c.bookingPastDataModel!
                                                        .data![i].resource!.name
                                                        .toString(),
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: MySize.size16,
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    height: MySize.size10,
                                                  ),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    children: [
                                                      SvgPicture.asset(
                                                        "assets/location.svg",
                                                        height: MySize.size14,
                                                        color: appTheme
                                                            .textGrayColor,
                                                      ),
                                                      Expanded(
                                                        child: Text(
                                                          " ${c.bookingPastDataModel!.data![i].resource!.address.toString()}",
                                                          style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .normal,
                                                              fontSize:
                                                                  MySize.size16,
                                                              color: appTheme
                                                                  .textGrayColor),
                                                          maxLines: 4,
                                                        ),
                                                      ),
                                                    ],
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                  ),
                                                  SizedBox(
                                                    height: MySize.size8,
                                                  ),
                                                  FittedBox(
                                                    fit: BoxFit.scaleDown,
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          children: [
                                                            SvgPicture.asset(
                                                              "assets/date.svg",
                                                              height:
                                                                  MySize.size14,
                                                            ),
                                                            (c.bookingPastDataModel!.data![i]
                                                                        .startAt !=
                                                                    null)
                                                                ? Text(
                                                                    " ${DateFormat("MM.dd.yyyy").format(getDateFromStringFromUtc(
                                                                      c
                                                                          .bookingPastDataModel!
                                                                          .data![
                                                                              i]
                                                                          .startAt
                                                                          .toString(),
                                                                    ).toLocal())} | ${DateFormat("hh:mm a").format(getDateFromStringFromUtc(
                                                                      c
                                                                          .bookingPastDataModel!
                                                                          .data![
                                                                              i]
                                                                          .startAt
                                                                          .toString(),
                                                                    ).toLocal())}",
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          MySize
                                                                              .size16,
                                                                    ),
                                                                  )
                                                                : SizedBox(),
                                                          ],
                                                        ),
                                                        Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .start,
                                                          children: [
                                                            SvgPicture.asset(
                                                              "assets/time.svg",
                                                              height:
                                                                  MySize.size14,
                                                            ),
                                                            Text(
                                                              " ${getDateFromString(
                                                                c.bookingPastDataModel!
                                                                    .data![i].endAt
                                                                    .toString(),
                                                              ).difference(getDateFromString(
                                                                    c
                                                                        .bookingPastDataModel!
                                                                        .data![
                                                                            i]
                                                                        .startAt
                                                                        .toString(),
                                                                  )).inHours} H",
                                                              style: TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .normal,
                                                                  fontSize: MySize
                                                                      .size16,
                                                                  color: appTheme
                                                                      .textGrayColor),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  SizedBox(
                                                    height: MySize.size8,
                                                  ),
                                                  Row(
                                                    children: [
                                                      // button(
                                                      //     title:
                                                      //     "Cancel",
                                                      //     backgroundColor:
                                                      //     Colors
                                                      //         .white,
                                                      //     textColor:
                                                      //     appTheme
                                                      //         .primaryTheme,
                                                      //     borderColor:
                                                      //     appTheme
                                                      //         .primaryTheme,
                                                      //     width: 90,
                                                      //     height: 35),
                                                      InkWell(
                                                        child: button(
                                                            title: "View",
                                                            width: 130,
                                                            height: 35),
                                                        onTap: () {
                                                          Get.toNamed(
                                                              Routes
                                                                  .productDetail,
                                                              arguments: {
                                                                "isBooked":
                                                                    true,
                                                                "isFromHistory":
                                                                    true,
                                                                StringConstant
                                                                        .productData:
                                                                    c
                                                                        .bookingPastDataModel!
                                                                        .data![
                                                                            i]
                                                                        .resource,
                                                                StringConstant
                                                                        .bookingModel:
                                                                    c.bookingPastDataModel!
                                                                        .data![i],
                                                              });
                                                        },
                                                      ),
                                                    ],
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                  )
                                                ],
                                              )
                                            ],
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                        separatorBuilder: (context, i) {
                          return SizedBox(
                            height: MySize.size60,
                          );
                        },
                        itemCount: c.bookingPastDataModel!.data!.length,
                      ),
                    )
                  : Center(
                      child: Text(
                        "No booking data found",
                        style: TextStyle(fontSize: MySize.size14!),
                      ),
                    )),
        ),
      ],
    );
  }

  getMyTournament(MyBookingController c) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Text(
            //   "My Booking",
            //   style: TextStyle(
            //     fontWeight: FontWeight.w500,
            //     fontSize: MySize.size22,
            //   ),
            // ),
            InkWell(
              onTap: () {
                Get.toNamed(Routes.allMyBooking);
              },
              child: Text(
                "See all",
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
        SizedBox(
          height: MySize.size20,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            for (int i = 0; i < c.dateList.value.length; i++)
              InkWell(
                onTap: () {
                  c.dateList.value.forEach((element) {
                    element.isActive = false;
                  });
                  c.dateList.value[i].isActive = true;
                  c.fromDate = DateTime(
                      c.dateList.value[i].dateTime!.year,
                      c.dateList.value[i].dateTime!.month,
                      c.dateList.value[i].dateTime!.day,
                      0,
                      00,
                      00);
                  c.toDate = DateTime(
                      c.dateList.value[i].dateTime!.year,
                      c.dateList.value[i].dateTime!.month,
                      c.dateList.value[i].dateTime!.day,
                      23,
                      59,
                      59);
                  c.dateList.refresh();
                  c.hasData.value = false;
                  c.callApiForGetBookingList(context: Get.context!);
                  c.update();
                },
                child: Container(
                  height: MySize.size75,
                  width: MySize.getScaledSizeWidth(50),
                  decoration: BoxDecoration(
                    color: (c.dateList.value[i].isActive == true)
                        ? appTheme.newPrimaryColor
                        : Colors.white,
                    borderRadius: BorderRadius.circular(MySize.size14!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        c.dateList.value[i].weekDay.toString()[0],
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: MySize.size12,
                            color: (c.dateList.value[i].isActive == true)
                                ? Colors.white
                                : appTheme.primaryTheme),
                      ),
                      SizedBox(
                        height: MySize.size6,
                      ),
                      Text(
                        c.dateList.value[i].date.toString(),
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: MySize.size20,
                            color: (c.dateList.value[i].isActive == true)
                                ? Colors.white
                                : appTheme.primaryTheme),
                      ),
                      SizedBox(
                        height: MySize.size6,
                      ),
                      Text(
                        c.dateList.value[i].month.toString(),
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: MySize.size12,
                            color: (c.dateList.value[i].isActive == true)
                                ? Colors.white
                                : appTheme.primaryTheme),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        Expanded(
          child: (!c.hasData.value)
              ? Center(
                  child: getShimerForBookingList(),
                )
              : ((c.bookingDataModel != null &&
                      c.bookingDataModel!.data != null &&
                      c.bookingDataModel!.data!.isNotEmpty)
                  ? Container(
                      padding: EdgeInsets.only(top: MySize.size15!),
                      child: ListView.separated(
                        itemBuilder: (context, i) {
                          return InkWell(
                            onTap: () {
                              // Get.toNamed(Routes.PRODUCT_DETAIL,
                              //     arguments: {"isBooked": true});
                            },
                            child: Column(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          color: appTheme.borderColor),
                                      borderRadius: BorderRadius.circular(
                                          MySize.size10!)),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: MySize.getScaledSizeWidth(10),
                                    vertical: MySize.size10!,
                                  ),
                                  child: Row(
                                    children: [
                                      ClipRRect(
                                        // child: Image(
                                        //   image: AssetImage(
                                        //       "assets/table.png"),
                                        //   fit: BoxFit.cover,
                                        //   height: MySize.size140,
                                        //   width: MySize.size140,
                                        // ),
                                        child: CommonNetworkImageView(
                                          url: (c.bookingDataModel!.data![i]
                                                      .resource!.imageUrl !=
                                                  null)
                                              ? c.bookingDataModel!.data![i]
                                                  .resource!.imageUrl
                                                  .toString()
                                              : "",
                                          height: MySize.size140!,
                                          width: MySize.size140!,
                                          fit: BoxFit.cover,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      SizedBox(
                                        width: MySize.getScaledSizeWidth(7),
                                      ),
                                      Expanded(
                                        child: Container(
                                          //   height: MySize.size140,
                                          //width: MySize.size140,
                                          child: Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    "#${c.bookingDataModel!.data![i].id}",
                                                    style: TextStyle(
                                                      color: appTheme
                                                          .textGrayColor,
                                                      fontSize: MySize.size16,
                                                    ),
                                                  ),
                                                  Text(
                                                    "\$${c.bookingDataModel!.data![i].resource!.ratePerHour} /Hour",
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: MySize.size16,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Text(
                                                c.bookingDataModel!.data![i]
                                                    .resource!.name
                                                    .toString(),
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: MySize.size16,
                                                ),
                                              ),
                                              SizedBox(
                                                height: MySize.size10,
                                              ),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  SvgPicture.asset(
                                                    "assets/location.svg",
                                                    height: MySize.size14,
                                                    color:
                                                        appTheme.textGrayColor,
                                                  ),
                                                  Expanded(
                                                    child: Text(
                                                      " ${c.bookingDataModel!.data![i].resource!.address.toString()}",
                                                      style: TextStyle(
                                                          fontWeight:
                                                              FontWeight.normal,
                                                          fontSize:
                                                              MySize.size16,
                                                          color: appTheme
                                                              .textGrayColor),
                                                      softWrap: true,
                                                      maxLines: 3,
                                                    ),
                                                  ),
                                                ],
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                              ),
                                              SizedBox(
                                                height: MySize.size8,
                                              ),
                                              FittedBox(
                                                fit: BoxFit.scaleDown,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        SvgPicture.asset(
                                                          "assets/date.svg",
                                                          height: MySize.size14,
                                                        ),
                                                        (c.bookingDataModel!.data![i]
                                                                    .startAt !=
                                                                null)
                                                            ? Text(
                                                                '${c.bookingDataModel!.data![i].formatedStartDate} | ${c.bookingDataModel!.data![i].formatedStartTime}',
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: MySize
                                                                      .size16,
                                                                ),
                                                              )
                                                            /*Text(
                                                                " " +
                                                                    DateFormat(
                                                                            "MM.dd.yyyy")
                                                                        .format(
                                                                            getDateFromStringFromUtc(
                                                                      c
                                                                          .bookingDataModel!
                                                                          .data![
                                                                              i]
                                                                          .startAt
                                                                          .toString(),
                                                                    ).toLocal()) +
                                                                    " | " +
                                                                    DateFormat(
                                                                            "hh:mm a")
                                                                        .format(
                                                                            getDateFromStringFromUtc(
                                                                      c
                                                                          .bookingDataModel!
                                                                          .data![
                                                                              i]
                                                                          .startAt
                                                                          .toString(),
                                                                    ).toLocal()),
                                                                style: TextStyle(
                                                                  fontSize: MySize
                                                                      .size16,
                                                                ),
                                                              )*/
                                                            : SizedBox(),
                                                      ],
                                                    ),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .start,
                                                      children: [
                                                        SvgPicture.asset(
                                                          "assets/time.svg",
                                                          height: MySize.size14,
                                                        ),
                                                        Text(
                                                          " ${getDateFromStringWithHourAndMinute(DateTime(DateTime.parse(
                                                                c.bookingDataModel!
                                                                    .data![i].endAt
                                                                    .toString(),
                                                              ).year, DateTime.parse(
                                                                c.bookingDataModel!
                                                                    .data![i].endAt
                                                                    .toString(),
                                                              ).month, DateTime.parse(
                                                                c.bookingDataModel!
                                                                    .data![i].endAt
                                                                    .toString(),
                                                              ).day, DateTime.parse(
                                                                c.bookingDataModel!
                                                                    .data![i].endAt
                                                                    .toString(),
                                                              ).hour, DateTime.parse(
                                                                c.bookingDataModel!
                                                                    .data![i].endAt
                                                                    .toString(),
                                                              ).minute).difference(DateTime(DateTime.parse(
                                                                    c
                                                                        .bookingDataModel!
                                                                        .data![
                                                                            i]
                                                                        .startAt
                                                                        .toString(),
                                                                  ).year, DateTime.parse(
                                                                    c
                                                                        .bookingDataModel!
                                                                        .data![
                                                                            i]
                                                                        .startAt
                                                                        .toString(),
                                                                  ).month, DateTime.parse(
                                                                    c
                                                                        .bookingDataModel!
                                                                        .data![
                                                                            i]
                                                                        .startAt
                                                                        .toString(),
                                                                  ).day, DateTime.parse(
                                                                    c
                                                                        .bookingDataModel!
                                                                        .data![
                                                                            i]
                                                                        .startAt
                                                                        .toString(),
                                                                  ).hour, DateTime.parse(
                                                                    c
                                                                        .bookingDataModel!
                                                                        .data![
                                                                            i]
                                                                        .startAt
                                                                        .toString(),
                                                                  ).minute) /*DateTime.parse(
                                                                c
                                                                    .bookingDataModel!
                                                                    .data![i]
                                                                    .startAt
                                                                    .toString(),
                                                              )*/
                                                              ).inSeconds)}",
                                                          style: TextStyle(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .normal,
                                                              fontSize:
                                                                  MySize.size16,
                                                              color: appTheme
                                                                  .textGrayColor),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                height: MySize.size8,
                                              ),
                                              Center(
                                                child: Row(
                                                  children: [
                                                    if (getButtonVisibleOrNot(
                                                        c: c, i: i))
                                                      InkWell(
                                                        child: button(
                                                            title: "Cancel",
                                                            backgroundColor:
                                                                Colors.white,
                                                            textColor: appTheme
                                                                .primaryTheme,
                                                            borderColor: appTheme
                                                                .primaryTheme,
                                                            width: 90,
                                                            height: 35),
                                                        onTap: () {
                                                          // c.callApiForDeleteBookingList(
                                                          //     context:
                                                          //         context,
                                                          //     id: c
                                                          //         .bookingDataModel!
                                                          //         .data![i]
                                                          //         .id);
                                                          _asyncConfirmDialog(
                                                              context, i, c);
                                                        },
                                                      ),
                                                    if (getButtonVisibleOrNot(
                                                        c: c, i: i))
                                                      SizedBox(
                                                        width: 10,
                                                      ),
                                                    InkWell(
                                                      onTap: () {
                                                        Get.toNamed(
                                                            Routes
                                                                .productDetail,
                                                            arguments: {
                                                              "isBooked": true,
                                                              StringConstant
                                                                      .productData:
                                                                  c
                                                                      .bookingDataModel!
                                                                      .data![i]
                                                                      .resource,
                                                              StringConstant
                                                                      .bookingModel:
                                                                  c.bookingDataModel!
                                                                      .data![i],
                                                            });
                                                      },
                                                      child: Center(
                                                        child: button(
                                                            title: "View",
                                                            width: 130,
                                                            height: 35),
                                                      ),
                                                    ),
                                                  ],
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                ),
                                              )
                                            ],
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  width: double.infinity,
                                ),
                              ],
                            ),
                          );
                        },
                        separatorBuilder: (context, i) {
                          return SizedBox(
                            height: MySize.size60,
                          );
                        },
                        itemCount: c.bookingDataModel!.data!.length,
                      ),
                    )
                  : Center(
                      child: Text(
                        "No booking data found",
                        style: TextStyle(fontSize: MySize.size14!),
                      ),
                    )),
        ),
      ],
    );
  }

  getButtonVisibleOrNot({MyBookingController? c, int i = 0}) {
    if (c!.bookingDataModel!.data![i].plan!) {
      return false;
    } else {
      if (getDateFromString(c.bookingDataModel!.data![i].createdAt!)
          .add(Duration(days: 1))
          .isAfter(DateTime.now())) {
        return true;
      } else {
        return false;
      }
    }
  }

  _asyncConfirmDialog(
      BuildContext context, i, MyBookingController controller) async {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap button for close dialog!
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Cancel Booking'),
          content: const Text('Are you sure cancel this booking?'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              child: const Text(
                'Ok',
                style: TextStyle(
                  color: Colors.white,
                ),
              ),
              style: TextButton.styleFrom(backgroundColor: Colors.red),
              // color: Colors.red,
              onPressed: () {
                Navigator.of(context).pop();
                controller.callApiForDeleteBookingList(
                    context: context,
                    id: controller.bookingDataModel!.data![i].id);
              },
            )
          ],
        );
      },
    );
  }
}
