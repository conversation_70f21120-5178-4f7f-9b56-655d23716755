import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:small_officer/Models/sign_up_model.dart';

import '../../../../constants/api_constant.dart';
import '../../../../constants/constant.dart';
import '../../../../data/network_client.dart';
import '../../../../main.dart';
import '../../../../utilities/customeDialogs.dart';
import '../../../routes/app_pages.dart';

class RegistrationScreenController extends GetxController {
  //TODO: Implement RegistrationScreenController
  User? user;
  String? loginMethod;
  String? email;
  String? token;
  Rx<TextEditingController> nameController = TextEditingController().obs;
  Rx<TextEditingController> emailController = TextEditingController().obs;
  Rx<TextEditingController> aboutController = TextEditingController().obs;
  Rx<TextEditingController> mobileNumber = TextEditingController().obs;
  Rx<TextEditingController> companyName = TextEditingController().obs;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null) {
      user = Get.arguments["userData"];
      loginMethod = Get.arguments["logInType"];
      token = Get.arguments["token"];
    }
    if (loginMethod == "google") {
      nameController.value.text = user!.displayName.toString();
      emailController.value.text = user!.email.toString();

      if (user!.phoneNumber != null) {
        mobileNumber.value.text = user!.phoneNumber.toString();
      }
      email = user!.email.toString();
    } else if (loginMethod == "apple") {
      if (user!.displayName != null) {
        nameController.value.text = user!.displayName.toString();
      }
      if (user!.email != null) {
        emailController.value.text = user!.email.toString();
        email = user!.email.toString();
      }

      if (user!.phoneNumber != null) {
        mobileNumber.value.text = user!.phoneNumber.toString();
      }
    } else if (loginMethod == "mobile") {
      if (user!.phoneNumber != null) {
        mobileNumber.value.text = user!.phoneNumber.toString();
      }
      email = user!.phoneNumber.toString();
    }
  }

  callApiForRegisterUser({required BuildContext context, User? user}) {
    FocusScope.of(context).unfocus();
    app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};
    GetStorage box = GetStorage();
    dict["firstName"] = nameController.value.text;
    dict["lastName"] = "";
    // dict["about"] = aboutController.value.text;
    dict["email"] = emailController.value.text;

    dict["mobile"] = mobileNumber.value.text;
    dict["image"] = null;

    return NetworkClient.getInstance.callApi(
      context,
      authUrl,
      ApiConstant.userSignUp,
      MethodType.post,
      headers: NetworkClient.getInstance.getAuthHeaders(tokenRegister: token),
      params: dict,
      successCallback: (response, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        //AuthModel authModel = AuthModel.fromJson(response);

        SignUpModel signUpModel = SignUpModel.fromJson(response);
        print(signUpModel);
        box.write(Constant.tokenKey, signUpModel.token);
        box.write(Constant.loginMethod, loginMethod);
        Get.offAllNamed(Routes.privacyScreen);
      },
      failureCallback: (status, message) {
        app.resolve<CustomDialogs>().hideCircularDialog(context);
        app.resolve<CustomDialogs>().getDialog(title: "Failed", desc: message);

        print(" error");
      },
    );
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  @override
  void onClose() {}
  void increment() => count.value++;
}
