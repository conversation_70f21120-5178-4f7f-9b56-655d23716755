import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/utilities/text_field.dart';
import '../../../../constants/size_constant.dart';
import '../../../../utilities/submit_button.dart';
import '../controllers/registration_screen_controller.dart';

class RegistrationScreenView extends GetWidget<RegistrationScreenController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        child: SingleChildScrollView(
          child: Container(
            padding:
                EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(20)),
            child: Form(
              key: controller.formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: MySize.getScaledSizeHeight(15)),
                  Row(
                    children: [
                      InkWell(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(Icons.arrow_back),
                      ),
                      Spacer(),
                      Text("Registration",
                          style: TextStyle(
                              fontSize: MySize.getScaledSizeHeight(23),
                              fontWeight: FontWeight.w500)),
                      Spacer(),
                    ],
                  ),
                  SizedBox(height: MySize.size25),
                  Text(
                    "Hi",
                    style: TextStyle(
                        color: appTheme.borderColor,
                        fontSize: MySize.getScaledSizeHeight(25)),
                  ),
                  Text(
                    controller.email.toString(),
                    style: TextStyle(
                        color: appTheme.primaryTheme,
                        fontWeight: FontWeight.bold,
                        fontSize: MySize.getScaledSizeHeight(20)),
                  ),
                  SizedBox(height: MySize.size45),
                  Text(
                    "Welcome to our platform",
                    style: TextStyle(
                        color: appTheme.primaryTheme,
                        fontSize: MySize.getScaledSizeHeight(22)),
                  ),
                  SizedBox(height: MySize.size10),
                  Text(
                    "Please provide your details",
                    style: TextStyle(
                        color: appTheme.borderColor,
                        fontSize: MySize.getScaledSizeHeight(16)),
                  ),
                  SizedBox(height: MySize.size10),
                  getTextFormField(
                      hintText: "Name*",
                      textEditingController: controller.nameController.value,
                      validation: (val) {
                        if (val!.isEmpty) {
                          return "Please Enter Name";
                        }
                        return null;
                      },
                      prefixIcon: Padding(
                          padding: EdgeInsets.all(MySize.size16!),
                          child: SvgPicture.asset(
                            "assets/account_logo.svg",
                            height: MySize.size15,
                          ))),
                  SizedBox(height: MySize.size10),
                  getTextFormField(
                      hintText: "Email*",
                      textEditingController: controller.emailController.value,
                      isReadOnly: (controller.loginMethod == "google" ||
                              controller.loginMethod == 'apple')
                          ? true
                          : false,
                      validation: (val) {
                        if (val!.isEmpty) {
                          return "Please Enter Email";
                        }
                        return null;
                      },
                      prefixIcon: Padding(
                          padding: EdgeInsets.all(MySize.size16!),
                          child: SvgPicture.asset(
                            "assets/mail.svg",
                            height: MySize.size15,
                          ))),
                  SizedBox(height: MySize.size10),
                  getTextFormField(
                      hintText: "Mobile Number*",
                      textEditingController: controller.mobileNumber.value,
                      isReadOnly:
                          (controller.loginMethod == "mobile") ? true : false,
                      validation: (val) {
                        if (val!.isEmpty) {
                          return "Please Enter Mobile Number";
                        }
                        return null;
                      },
                      prefixIcon: Padding(
                          padding: EdgeInsets.all(MySize.size16!),
                          child: SvgPicture.asset(
                            "assets/mobile_logo.svg",
                            height: MySize.size15,
                          ))),
                  // SizedBox(height: MySize.size10),
                  // getTextFormField(
                  //   hintText: "About",
                  //   textEditingController: controller.aboutController.value,
                  //   maxLine: 1,
                  //   prefixIcon: Padding(
                  //     padding: EdgeInsets.all(MySize.size16!),
                  //     child: SvgPicture.asset(
                  //       "assets/info.svg",
                  //       height: MySize.size15,
                  //     ),
                  //   ),
                  // ),
                  SizedBox(height: MySize.size10),
                  getTextFormField(
                      hintText: "Company Name",
                      textEditingController: controller.companyName.value,
                      // validation: (val) {
                      //   if (val!.isEmpty) {
                      //     return "Please Enter Name";
                      //   }
                      //   return null;
                      // },
                      prefixIcon: Padding(
                          padding: EdgeInsets.all(MySize.size16!),
                          child: SvgPicture.asset(
                            "assets/company_logo.svg",
                            height: MySize.size15,
                          ))),
                  SizedBox(height: MySize.getScaledSizeHeight(250)),
                  InkWell(
                      onTap: () {
                        if (controller.formKey.currentState!.validate()) {
                          controller.callApiForRegisterUser(context: context);
                        }
                        // Get.toNamed(Routes.OTP_SCREEN);
                      },
                      child: button(
                          title: "Create Account ✔",
                          width: MySize.getScaledSizeWidth(480))),
                  SizedBox(height: MySize.getScaledSizeHeight(50)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
