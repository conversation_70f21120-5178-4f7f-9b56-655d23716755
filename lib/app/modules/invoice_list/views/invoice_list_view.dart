import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:small_officer/Models/invoice_list_model.dart';
import 'package:small_officer/app/routes/app_pages.dart';
import 'package:small_officer/constants/color_constant.dart';
import 'package:small_officer/constants/constant.dart';

import '../../../../constants/size_constant.dart';
import '../../../../utilities/utilities.dart';
import '../controllers/invoice_list_controller.dart';

class InvoiceListView extends GetWidget<InvoiceListController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: () {
              Get.back();
            },
            child: Padding(
              padding: EdgeInsets.only(
                  left: MySize.getScaledSizeWidth(15),
                  top: MySize.size17!,
                  bottom: MySize.size17!),
              child: SvgPicture.asset(
                "assets/arrow_back.svg",
              ),
            ),
          ),
          title: Container(
            // color: Colors.red,
            width: MySize.getScaledSizeWidth(192),
            height: MySize.getScaledSizeHeight(55),
            child: Image(
              image: AssetImage("assets/logo1.jpg"),
              fit: BoxFit.fill,
            ),
          ),
          actions: [
            InkWell(
              onTap: () {
                Get.toNamed(Routes.notification);
              },
              child: Padding(
                padding: EdgeInsets.only(
                    left: MySize.getScaledSizeWidth(15),
                    right: MySize.getScaledSizeWidth(15),
                    top: MySize.size15!,
                    bottom: MySize.size15!),
                child: Image(
                  image: AssetImage("assets/notification.png"),
                ),
              ),
            ),
          ],
          centerTitle: true,
          elevation: 0,
        ),
        body: Container(
          height: MySize.safeHeight,
          width: MySize.screenWidth,
          padding: EdgeInsets.symmetric(
            horizontal: MySize.size15!,
            vertical: MySize.size15!,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Invoice",
                style: TextStyle(
                  fontSize: MySize.size20!,
                  color: appTheme.primaryTheme,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Expanded(
                // child: Column(
                //   mainAxisAlignment: MainAxisAlignment.start,
                //   crossAxisAlignment: CrossAxisAlignment.start,
                //   children: [
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     Container(
                //       width: double.infinity,
                //       height: MySize.getScaledSizeHeight(0.5),
                //       color: appTheme.borderColor,
                //     ),
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     Text(
                //       "Today",
                //       style: TextStyle(
                //         fontSize: MySize.size18!,
                //         color: appTheme.textGrayColor,
                //         fontWeight: FontWeight.w700,
                //       ),
                //     ),
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     getInvoiceData(),
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     getInvoiceData(
                //         name: "Baraka Office",
                //         time: "12:23 PM",
                //         inNo: "#123216",
                //         isPaid: false),
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     getInvoiceData(
                //         name: "Baraka Office",
                //         time: "12:23 PM",
                //         inNo: "#123216",
                //         isPaid: true),
                //     SizedBox(
                //       height: MySize.size20!,
                //     ),
                //     Text(
                //       "! May",
                //       style: TextStyle(
                //         fontSize: MySize.size18!,
                //         color: appTheme.textGrayColor,
                //         fontWeight: FontWeight.w700,
                //       ),
                //     ),
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     getInvoiceData(),
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     getInvoiceData(
                //         name: "Baraka Office",
                //         time: "12:23 PM",
                //         inNo: "#123216",
                //         isPaid: true),
                //     SizedBox(
                //       height: MySize.size10!,
                //     ),
                //     getInvoiceData(
                //         name: "Baraka Office",
                //         time: "12:23 PM",
                //         inNo: "#123216",
                //         isPaid: true),
                //   ],
                // ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                        height: MySize.size10!,
                      ),
                      Container(
                        width: double.infinity,
                        height: MySize.getScaledSizeHeight(0.5),
                        color: appTheme.borderColor,
                      ),
                      SizedBox(
                        height: MySize.size10!,
                      ),
                      (controller.hasData.value)
                          ? GroupedListView<dynamic, String>(
                              elements: controller.invoiceListModel!.data!,
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              groupBy: (element) =>
                                  element.createdAt.substring(0, 10),
                              groupComparator: (value1, value2) =>
                                  value2.compareTo(value1),
                              itemComparator: (item1, item2) =>
                                  (item1.createdAt).compareTo(item2.createdAt),
                              order: GroupedListOrder.ASC,
                              // useStickyGroupSeparators: true,
                              groupSeparatorBuilder: (String value) => Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Padding(
                                  padding: EdgeInsets.only(top: MySize.size15!),
                                  child: Text(
                                    getTitle(value),
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              itemBuilder: (c, element) {
                                return getInvoiceData(
                                    name: "Baraka Office",
                                    time: "12:23 PM",
                                    inNo: "#123216",
                                    isPaid: true,
                                    invoice: element);
                              },
                            )
                          : Container(
                              height: MySize.getScaledSizeHeight(800),
                              child: Center(
                                child: getShimerForInvoice(),
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  getTitle(String date) {
    DateTime now =
        getDateFromStringFromUtc(date.toString(), formatter: "yyyy-MM-dd")
            .toLocal();
    int i = controller.calculateDifference(now);
    if (i == 0) {
      return "Today";
    } else if (i == -1) {
      return "Yesterday";
    } else {
      return DateFormat("dd MMM, yyyy").format(
          getDateFromStringFromUtc(date, formatter: "yyyy-MM-dd").toLocal());
    }
  }

  InkWell getInvoiceData(
      {String inNo = "#323232",
      String name = "Hill office",
      String time = "10:30 AM",
      bool isPaid = true,
      Invoice? invoice}) {
    return InkWell(
      onTap: () {
        Get.toNamed(Routes.invoiceDetail,
            arguments: {Constant.invoiceData: invoice});
      },
      child: Container(
        width: double.infinity,
        height: MySize.size100,
        margin: EdgeInsets.only(bottom: MySize.size10!),
        decoration: BoxDecoration(
            // color: appTheme.fillColor,
            borderRadius: BorderRadius.circular(MySize.size4!),
            border: Border.all(
              color: appTheme.borderColor,
            )),
        // padding: EdgeInsets.symmetric(
        //   horizontal: MySize.getScaledSizeWidth(10),
        //   vertical: MySize.size10!,
        // ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                ClipRRect(
                  child: CommonNetworkImageView(
                    url:
                        isNullEmptyOrFalse(invoice!.booking!.resource?.imageUrl)
                            ? ""
                            : invoice.booking!.resource!.imageUrl.toString(),
                    height: MySize.getScaledSizeHeight(100),
                    width: MySize.getScaledSizeHeight(100),
                    fit: BoxFit.fill,
                  ),
                  borderRadius: BorderRadius.circular(MySize.size4!),
                ),
                SizedBox(
                  width: MySize.getScaledSizeWidth(20),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "#${invoice.id}",
                          style: TextStyle(
                            fontSize: MySize.size14!,
                            color: appTheme.primaryTheme,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        SizedBox(
                          height: MySize.size3,
                        ),
                        Text(
                          (isNullEmptyOrFalse(invoice.booking!.resource))
                              ? "N/A"
                              : invoice.booking!.resource!.name.toString(),
                          style: TextStyle(
                            fontSize: MySize.size18!,
                            color: appTheme.primaryTheme,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: MySize.size15,
                    ),
                    Text(
                      "Generated at ${DateFormat("hh:mm a").format(getDateFromString(
                            invoice.createdAt.toString(),
                          ))}",
                      style: TextStyle(
                        fontSize: MySize.size16!,
                        color: appTheme.primaryTheme,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Container(
              width: MySize.getScaledSizeWidth(100),
              height: MySize.size100,
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(color: appTheme.borderColor),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    (invoice.isPaid!) ? "Paid" : "Unpaid",
                    style: TextStyle(
                      fontSize: MySize.size18!,
                      color: (invoice.isPaid!) ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
