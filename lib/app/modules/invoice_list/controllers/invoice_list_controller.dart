import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:small_officer/Models/invoice_list_model.dart';
import '../../../../constants/api_constant.dart';
import '../../../../data/network_client.dart';

class InvoiceListController extends GetxController {
  final count = 0.obs;
  RxBool hasData = false.obs;
  InvoiceListModel? invoiceListModel;
  @override
  void onInit() {
    super.onInit();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      callApiForInvoiceList(context: Get.context!);
    });
  }

  int calculateDifference(DateTime date) {
    DateTime now = DateTime.now();
    return DateTime(date.year, date.month, date.day).difference(DateTime(now.year, now.month, now.day)).inDays;
  }

  /*@override
  void onReady() {
    super.onReady();
  }*/

  callApiForInvoiceList({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    //app.resolve<CustomDialogs>().showCircularDialog(context);
    Map<String, dynamic> dict = {};

    return NetworkClient.getInstance.callApi(
      context,
      baseURL,
      ApiConstant.invoice,
      MethodType.get,
      headers: NetworkClient.getInstance.getAuthHeaders(),
      params: dict,
      successCallback: (response, message) {
        hasData.value = true;
        invoiceListModel = InvoiceListModel.fromJson(response);
        print(invoiceListModel!.dataLength.toString());
        //   app.resolve<CustomDialogs>().hideCircularDialog(context);
      },
      failureCallback: (status, message) {
        hasData.value = true;

        print(" error");
      },
    );
  }

  @override
  void onClose() {}
  void increment() => count.value++;
}
