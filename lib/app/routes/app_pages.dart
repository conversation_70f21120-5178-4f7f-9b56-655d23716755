import 'package:get/get.dart';

import '../modules/all_my_booking/bindings/all_my_booking_binding.dart';
import '../modules/all_my_booking/views/all_my_booking_view.dart';
import '../modules/app_under_maintenance/bindings/app_under_maintenance_binding.dart';
import '../modules/app_under_maintenance/views/app_under_maintenance_view.dart';
import '../modules/app_update_available/bindings/app_update_available_binding.dart';
import '../modules/app_update_available/views/app_update_available_view.dart';
import '../modules/chat_detail/bindings/chat_detail_binding.dart';
import '../modules/chat_detail/views/chat_detail_view.dart';
import '../modules/create_booking/bindings/create_booking_binding.dart';
import '../modules/create_booking/views/create_booking_view.dart';
import '../modules/edit_profile/bindings/edit_profile_binding.dart';
import '../modules/edit_profile/views/edit_profile_view.dart';
import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/home_drawer/bindings/home_drawer_binding.dart';
import '../modules/home_drawer/views/home_drawer_view.dart';
import '../modules/home_screen/bindings/home_screen_binding.dart';
import '../modules/home_screen/views/home_screen_view.dart';
import '../modules/info/bindings/info_binding.dart';
import '../modules/info/views/info_view.dart';
import '../modules/invoice_detail/bindings/invoice_detail_binding.dart';
import '../modules/invoice_detail/views/invoice_detail_view.dart';
import '../modules/invoice_list/bindings/invoice_list_binding.dart';
import '../modules/invoice_list/views/invoice_list_view.dart';
import '../modules/lock_screen/bindings/lock_screen_binding.dart';
import '../modules/lock_screen/views/lock_screen_view.dart';
import '../modules/login_screen/bindings/login_screen_binding.dart';
import '../modules/login_screen/views/login_screen_view.dart';
import '../modules/my_booking/bindings/my_booking_binding.dart';
import '../modules/my_booking/views/my_booking_view.dart';
import '../modules/my_profile/bindings/my_profile_binding.dart';
import '../modules/my_profile/views/my_profile_view.dart';
import '../modules/no_internet/bindings/no_internet_binding.dart';
import '../modules/no_internet/views/no_internet_view.dart';
import '../modules/notification/bindings/notification_binding.dart';
import '../modules/notification/views/notification_view.dart';
import '../modules/otp_screen/bindings/otp_screen_binding.dart';
import '../modules/otp_screen/views/otp_screen_view.dart';
import '../modules/password_screen/bindings/password_screen_binding.dart';
import '../modules/password_screen/views/password_screen_view.dart';
import '../modules/past_booking/bindings/past_booking_binding.dart';
import '../modules/past_booking/views/past_booking_view.dart';
import '../modules/payment_method/bindings/payment_method_binding.dart';
import '../modules/payment_method/views/payment_method_view.dart';
import '../modules/privacy_screen/bindings/privacy_screen_binding.dart';
import '../modules/privacy_screen/views/privacy_screen_view.dart';
import '../modules/product_detail/bindings/product_detail_binding.dart';
import '../modules/product_detail/views/product_detail_view.dart';
import '../modules/registration_screen/bindings/registration_screen_binding.dart';
import '../modules/registration_screen/views/registration_screen_view.dart';
import '../modules/report_issue/bindings/report_issue_binding.dart';
import '../modules/report_issue/views/report_issue_view.dart';
import '../modules/splash_screen/bindings/splash_screen_binding.dart';
import '../modules/splash_screen/views/splash_screen_view.dart';
import '../modules/terms_condition/bindings/terms_condition_binding.dart';
import '../modules/terms_condition/views/terms_condition_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.splashScreen;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH_SCREEN,
      page: () => SplashScreenView(),
      binding: SplashScreenBinding(),
    ),
    GetPage(
      name: _Paths.OTP_SCREEN,
      page: () => OtpScreenView(),
      binding: OtpScreenBinding(),
    ),
    GetPage(
      name: _Paths.REGISTRATION_SCREEN,
      page: () => RegistrationScreenView(),
      binding: RegistrationScreenBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN_SCREEN,
      page: () => LoginScreenView(),
      binding: LoginScreenBinding(),
    ),
    GetPage(
      name: _Paths.PASSWORD_SCREEN,
      page: () => PasswordScreenView(),
      binding: PasswordScreenBinding(),
    ),
    GetPage(
      name: _Paths.PRIVACY_SCREEN,
      page: () => PrivacyScreenView(),
      binding: PrivacyScreenBinding(),
    ),
    GetPage(
      name: _Paths.HOME_SCREEN,
      page: () => HomeScreenView(),
      binding: HomeScreenBinding(),
    ),
    GetPage(
      name: _Paths.MY_BOOKING,
      page: () => MyBookingView(),
      binding: MyBookingBinding(),
    ),
    GetPage(
      name: _Paths.PAST_BOOKING,
      page: () => PastBookingView(),
      binding: PastBookingBinding(),
    ),
    GetPage(
      name: _Paths.MY_PROFILE,
      page: () => MyProfileView(),
      binding: MyProfileBinding(),
    ),
    GetPage(
      name: _Paths.HOME_DRAWER,
      page: () => HomeDrawerView(),
      binding: HomeDrawerBinding(),
    ),
    GetPage(
      name: _Paths.REPORT_ISSUE,
      page: () => ReportIssueView(),
      binding: ReportIssueBinding(),
    ),
    GetPage(
      name: _Paths.EDIT_PROFILE,
      page: () => EditProfileView(),
      binding: EditProfileBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATION,
      page: () => NotificationView(),
      binding: NotificationBinding(),
    ),
    GetPage(
      name: _Paths.TERMS_CONDITION,
      page: () => TermsConditionView(),
      binding: TermsConditionBinding(),
    ),
    GetPage(
      name: _Paths.PRODUCT_DETAIL,
      page: () => ProductDetailView(),
      binding: ProductDetailBinding(),
    ),
    GetPage(
      name: _Paths.PAYMENT_METHOD,
      page: () => PaymentMethodView(),
      binding: PaymentMethodBinding(),
    ),
    GetPage(
      name: _Paths.CHAT_DETAIL,
      page: () => ChatDetailView(),
      binding: ChatDetailBinding(),
    ),
    GetPage(
      name: _Paths.CREATE_BOOKING,
      page: () => CreateBookingView(),
      binding: CreateBookingBinding(),
    ),
    GetPage(
      name: _Paths.ALL_MY_BOOKING,
      page: () => AllMyBookingView(),
      binding: AllMyBookingBinding(),
    ),
    GetPage(
      name: _Paths.INVOICE_LIST,
      page: () => InvoiceListView(),
      binding: InvoiceListBinding(),
    ),
    GetPage(
      name: _Paths.INVOICE_DETAIL,
      page: () => InvoiceDetailView(),
      binding: InvoiceDetailBinding(),
    ),
    GetPage(
      name: _Paths.INFO,
      page: () => InfoView(),
      binding: InfoBinding(),
    ),
    GetPage(
      name: _Paths.LOCK_SCREEN,
      page: () => LockScreenView(),
      binding: LockScreenBinding(),
    ),
    GetPage(
      name: _Paths.APP_UNDER_MAINTENANCE,
      page: () => const AppUnderMaintenanceView(),
      binding: AppUnderMaintenanceBinding(),
    ),
    GetPage(
      name: _Paths.APP_UPDATE_AVAILABLE,
      page: () => const AppUpdateAvailableView(),
      binding: AppUpdateAvailableBinding(),
    ),
    GetPage(
      name: _Paths.NO_INTERNET,
      page: () => const NoInternetView(),
      binding: NoInternetBinding(),
    ),
  ];
}
