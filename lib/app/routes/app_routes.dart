part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();

  static const home = _Paths.HOME;
  static const splashScreen = _Paths.SPLASH_SCREEN;
  static const otpScreen = _Paths.OTP_SCREEN;
  static const registrationScreen = _Paths.REGISTRATION_SCREEN;
  static const loginScreen = _Paths.LOGIN_SCREEN;
  static const privacyScreen = _Paths.PRIVACY_SCREEN;
  static const passwordScreen = _Paths.PASSWORD_SCREEN;
  static const homeScreen = _Paths.HOME_SCREEN;
  static const myBooking = _Paths.MY_BOOKING;
  static const pastBooking = _Paths.PAST_BOOKING;
  static const myProfile = _Paths.MY_PROFILE;
  static const homeDrawer = _Paths.HOME_DRAWER;
  static const reportIssue = _Paths.REPORT_ISSUE;
  static const editProfile = _Paths.EDIT_PROFILE;
  static const notification = _Paths.NOTIFICATION;
  static const termsCondition = _Paths.TERMS_CONDITION;
  static const productDetail = _Paths.PRODUCT_DETAIL;
  static const paymentMethod = _Paths.PAYMENT_METHOD;
  static const chatDetail = _Paths.CHAT_DETAIL;
  static const createBooking = _Paths.CREATE_BOOKING;
  static const allMyBooking = _Paths.ALL_MY_BOOKING;
  static const invoiceList = _Paths.INVOICE_LIST;
  static const invoiceDetail = _Paths.INVOICE_DETAIL;
  static const info = _Paths.INFO;
  static const lockScreen = _Paths.LOCK_SCREEN;
  static const APP_UNDER_MAINTENANCE = _Paths.APP_UNDER_MAINTENANCE;
  static const APP_UPDATE_AVAILABLE = _Paths.APP_UPDATE_AVAILABLE;
  static const NO_INTERNET = _Paths.NO_INTERNET;
}

abstract class _Paths {
  static const HOME = '/home';
  static const SPLASH_SCREEN = '/splash-screen';
  static const OTP_SCREEN = '/otp-screen';
  static const REGISTRATION_SCREEN = '/registration-screen';
  static const LOGIN_SCREEN = '/login-screen';
  static const PRIVACY_SCREEN = '/privacy-screen';
  static const PASSWORD_SCREEN = '/password-screen';
  static const HOME_SCREEN = '/home-screen';
  static const MY_BOOKING = '/my-booking';
  static const PAST_BOOKING = '/past-booking';
  static const MY_PROFILE = '/my-profile';
  static const HOME_DRAWER = '/home-drawer';
  static const REPORT_ISSUE = '/report-issue';
  static const EDIT_PROFILE = '/edit-profile';
  static const NOTIFICATION = '/notification';
  static const TERMS_CONDITION = '/terms-condition';
  static const PRODUCT_DETAIL = '/product-detail';
  static const PAYMENT_METHOD = '/payment-method';
  static const CHAT_DETAIL = '/chat-detail';
  static const CREATE_BOOKING = '/create-booking';
  static const ALL_MY_BOOKING = '/all-my-booking';
  static const INVOICE_LIST = '/invoice-list';
  static const INVOICE_DETAIL = '/invoice-detail';
  static const INFO = '/info';
  static const LOCK_SCREEN = '/lock-screen';
  static const APP_UNDER_MAINTENANCE = '/app-under-maintenance';
  static const APP_UPDATE_AVAILABLE = '/app-update-available';
  static const NO_INTERNET = '/no-internet';
}
