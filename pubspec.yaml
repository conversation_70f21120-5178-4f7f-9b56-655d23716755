name: small_officer
version: 1.1.10+31
publish_to: none
description: A new Flutter project.
environment:
  sdk: ">=2.15.1 <3.0.0"

dependencies:
  animated_toggle_switch: ^0.5.2
  cached_network_image: null
  carousel_slider: null
  connectivity: null
  country_code_picker: ^3.0.0
  crypto: null
  cupertino_icons: ^1.0.2
  dio: null
  grouped_list:
  dots_indicator: null
  firebase_dynamic_links: null
  firebase_auth: null
  firebase_core: null
  socket_io_client: ^2.0.0
  marquee: ^2.2.3
  url_launcher:
  share_plus:
  flutter:
    sdk: flutter
  flutter_stripe: ^9.2.0
  flutter_svg: null
  get: ^4.6.5
  get_storage: null
  google_sign_in: null
  http: null
  image_picker: null
  intl: null
  kiwi: null
  logging: null
  path: null
  pin_code_fields: null
  pinput: 1.2.2
  advance_pdf_viewer: ^2.0.1
  shared_preferences:
  sign_in_with_apple: null
  sms_autofill: null
  open_file_safe: ^3.2.3
  google_maps_flutter:
  swipebuttonflutter: ^1.0.0
  syncfusion_flutter_calendar: null
  syncfusion_flutter_sliders: null
  system_settings:
  firebase_database:
  app_settings:
  flutter_slidable:
  time_range_picker: null
  shimmer:
  pdf: ^3.8.3
  path_provider: ^2.0.11
  #Notification
  flutter_local_notifications: ^12.0.4
  firebase_messaging: ^14.1.1
  webview_flutter: ^3.0.4

  ##New UI
  linked_scroll_controller: ^0.2.0
  package_info_plus: ^1.4.2

dev_dependencies:
  build_runner: ^2.1.7
  flutter_lints: ^2.0.1
  flutter_test:
    sdk: flutter
  json_serializable: ^6.1.5
  kiwi_generator: null
  lints: ^2.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/service_icons/
#  fonts:
#    - family: Helvetica
#      fonts:
#        - asset: Fonts/Helvetica.ttf
