# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: a742f71d7f3484253a623b30e19256aa4668ecbb3de6ad1beb0bcf8d4777ecd8
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  advance_pdf_viewer:
    dependency: "direct main"
    description:
      name: advance_pdf_viewer
      sha256: "0263dd0a9e1314937d63db64fcc7e8f3b0a1d96da7a07849e2de31f195393e0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  animated_toggle_switch:
    dependency: "direct main"
    description:
      name: animated_toggle_switch
      sha256: c23f68956e8f4e64c2c4d40a6b6b7e9ed3ab0521be9f653e968954f14070a1d0
      url: "https://pub.dev"
    source: hosted
    version: "0.5.2"
  app_settings:
    dependency: "direct main"
    description:
      name: app_settings
      sha256: "66715a323ac36d6c8201035ba678777c0d2ea869e4d7064300d95af10c3bb8cb"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "0c8368c9b3f0abbc193b9d6133649a614204b528982bebc7026372d61677ce3a"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.7"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  barcode:
    dependency: transitive
    description:
      name: barcode
      sha256: "789f898eef0bd88312470bdb2cc996f895ad7dd5f89e9adde84b204546a90b45"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  bidi:
    dependency: transitive
    description:
      name: bidi
      sha256: dc00274c7edabae2ab30c676e736ea1eb0b1b7a1b436cb5fe372e431ccb39ab0
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "3fbda25365741f8251b39f3917fb3c8e286a96fd068a5a242e11c2012d495777"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "757153e5d9cd88253cb13f28c2fb55a537dc31fefd98137549895b5beb7c6169"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "6c4dd11d05d056e76320b828a1db0fc01ccd376922526f8e9d6c796a5adbac20"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: b0a8a7b8a76c493e85f1b84bffa0588859a06197863dba8c9036b15581fd9727
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "0671ad4162ed510b70d0eb4ad6354c249f8429cab4ae7a4cec86bbc2886eb76e"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.7+1"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "598a2a682e2a7a90f08ba39c0aaa9374c5112340f0a2e275f61b59389543d166"
      url: "https://pub.dev"
    source: hosted
    version: "8.6.1"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: fd3d0dc1d451f9a252b32d95d3f0c3c487bc41a75eba2e6097cb0b9c71491b15
      url: "https://pub.dev"
    source: hosted
    version: "3.2.3"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: bb2b8403b4ccdc60ef5f25c70dead1f3d32d24b9d6117cfc087f496b178594a7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: b8eb814ebfcb4dea049680f8c1ffb2df399e4d03bf7a352c775e26fa06e02fa0
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "4ad01d6e56db961d29661561effde45e519939fdaeb46c351275b182eac70189"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.dev"
    source: hosted
    version: "1.17.0"
  connectivity:
    dependency: "direct main"
    description:
      name: connectivity
      sha256: a8e91263cf3e25fb5cc95e19dfde4999e32a648ac3b9e8a558a28165731678f8
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  connectivity_for_web:
    dependency: transitive
    description:
      name: connectivity_for_web
      sha256: "01a390c1d5adc2ed1fa1f52d120c07fe9fd01166a93f965a832fd6cfc0ea6482"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+1"
  connectivity_macos:
    dependency: transitive
    description:
      name: connectivity_macos
      sha256: "51ae08d5162eca9669b9d8951ed83ce19c5355a81149f94e4dee2740beb93628"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  connectivity_platform_interface:
    dependency: transitive
    description:
      name: connectivity_platform_interface
      sha256: "2d82e942df9d49f29a24bb07fb5ce085d4a53e47818c62364d2b6deb9e0d7a8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  country_code_picker:
    dependency: "direct main"
    description:
      name: country_code_picker
      sha256: "92818885f0e47486539f80463b66f649970506a91dd3c0731ca3ba5308324a4d"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "0b0036e8cccbfbe0555fd83c1d31a6f30b77a96b598b35a5d36dd41f718695e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+4"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "706b5707578e0c1b4b7550f64078f0a0f19dec3f50a178ffae7006b0a9ca58fb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: e35129dc44c9118cee2a5603506d823bab99c68393879edb440e0090d07586be
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "253bfaa3d340778d8bc755e89c3af38e85ef95e65fd5d5670aa3167f8d4f6577"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: a9d76e72985d7087eb7c5e7903224ae52b337131518d127c554b9405936752b8
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1+1"
  dots_indicator:
    dependency: "direct main"
    description:
      name: dots_indicator
      sha256: "58b6a365744aa62aa1b70c4ea29e5106fbe064f5edaf7e9652e9b856edbfd9bb"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  fading_edge_scrollview:
    dependency: transitive
    description:
      name: fading_edge_scrollview
      sha256: c25c2231652ce774cc31824d0112f11f653881f43d7f5302c05af11942052031
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "13a6ccf6a459a125b3fcdb6ec73bd5ff90822e071207c663bfd1f70062d51d18"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "770eb1ab057b5ae4326d1c24cc57710758b9a46026349d021d6311bd27580046"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "7a6f1ae6107265664f3f7f89a66074882c4d506aef1441c9af313c1f7e6f41ce"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: "412705a646a0ae90f33f37acfae6a0f7cbc02222d6cd34e479421c3e74d3853c"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "1372760c6b389842b77156203308940558a2817360154084368608413835fc26"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      sha256: f693c0aa998b1101453878951b171b69f0db5199003df1c943b33493a1de7917
      url: "https://pub.dev"
    source: hosted
    version: "4.6.3"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      sha256: "689ae048b78ad088ba31acdec45f5badb56201e749ed8b534947a7303ddb32aa"
      url: "https://pub.dev"
    source: hosted
    version: "6.15.3"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      sha256: f35d637a1707afd51f30090bb5234b381d5071ccbfef09b8c393bc7c65e440cd
      url: "https://pub.dev"
    source: hosted
    version: "5.5.3"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: a4a99204da264a0aa9d54a332ea0315ce7b0768075139c77abefe98093dd98be
      url: "https://pub.dev"
    source: hosted
    version: "2.14.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: b63e3be6c96ef5c33bdec1aab23c91eb00696f6452f0519401d640938c94cba2
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "0fd5c4b228de29b55fac38aed0d9e42514b3d3bd47675de52bf7f8fccaf922fa"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  firebase_database:
    dependency: "direct main"
    description:
      name: firebase_database
      sha256: "36699bda00feb31433606f034078c690f148c15a4721b351a83c796ba4bba4e7"
      url: "https://pub.dev"
    source: hosted
    version: "10.2.3"
  firebase_database_platform_interface:
    dependency: transitive
    description:
      name: firebase_database_platform_interface
      sha256: "8bd62f80b51d71a81087a33fe2e2a868fb818a6e33b319a137a7bcb35dec262d"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.5+3"
  firebase_database_web:
    dependency: transitive
    description:
      name: firebase_database_web
      sha256: "05e6b0c2a192569cd5ce2be4a708756ca9f56e4c7b6fe1916f0f9d96a826f235"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3+3"
  firebase_dynamic_links:
    dependency: "direct main"
    description:
      name: firebase_dynamic_links
      sha256: "9b984d0abd227a702451a997abcca763f4dbf67e260dad60e5506d55e3eff244"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.3"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: "6ef00a0be18f3231e9727f7c4b31db89dbfa16792098beb850603c30854560ff"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6+3"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "7a09d8c21147f009882a27c96de1918ea283f974d73cb6fae1d234bb9ec18d8b"
      url: "https://pub.dev"
    source: hosted
    version: "14.6.4"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: e9e9dc48a3d8ffa67aaba3d6b1ebf74bc7d7d8c83d10b1458ff97878b9d8a2b0
      url: "https://pub.dev"
    source: hosted
    version: "4.5.3"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "381f217e41e0e407baf8df21787b97e46fabfacefd6a953425be3a6cdf2269f4"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.3"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "05001537bd3fac7644fa6558b09ec8c0a3f2eba78c0765f88912882b1331a5c6"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "2118df84ef0c3ca93f96123a616ae8540879991b8b57af2f81b76a7ada49b2a4"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: f222919a34545931e47b06000836b5101baeffb0e6eb5a4691d2d42851740dd9
      url: "https://pub.dev"
    source: hosted
    version: "12.0.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: "6af440e3962eeab8459602c309d7d4ab9e62f05d5cfe58195a28f846a0b5d523"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "5ec1feac5f7f7d9266759488bc5f76416152baba9aa1b26fe572246caa00d1ab"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "950e77c2bbe1692bc0874fc7fb491b96a4dc340457f4ea1641443d0a6c1ea360"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: cc4231579e3eae41ae166660df717f4bad1359c87f4a4322ad8ba1befeb3d2be
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  flutter_stripe:
    dependency: "direct main"
    description:
      name: flutter_stripe
      sha256: fb1a0647867a26b1fced98706ef96c664a5ae2579e29b67af5ddd054e01d83df
      url: "https://pub.dev"
    source: hosted
    version: "9.2.2"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: f991fdb1533c3caeee0cdc14b04f50f0c3916f0dbcbc05237ccbe4e3c6b93f3f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: aeac15850ef1b38ee368d4c53ba9a847e900bb2c53a4db3f6881cbb3cb684338
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: "2ba20a47c8f1f233bed775ba2dd0d3ac97b4cf32fc17731b3dfc672b06b0e92a"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.5"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "7940fdc3b1035db4d65d387c1bdd6f9574deaa6777411569c05ecc25672efacd"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "555d5d736339b0478e821167ac521c810d7b51c3b2734e6802a9f046b64ea37a"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      sha256: "7b417a64ee7a060f42cf44d8c274d3b562423f6fe57d2911b7b536857c0d8eb6"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: "9512c862df77c1f0fa5f445513dd3c57f5996f0a809dccb74e54b690ee4e3a0f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.15"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: a9462a433bf3ebe60aadcf4906d2d6341a270d69d3e0fcaa8eb2b64699fcfb4f
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: "308f0af138fa78e8224d598d46ca182673874d0ef4d754b7157c073b5b4b8e0d"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.7"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: "280170a2dcac3364317b5786f0d2e3c4128fdb795bc0d87ffe56226b0cf1f57d"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      sha256: aab6fdc41374014494f9e9026b9859e7309639d50a0bf4a2a412467a5ae4abc6
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: "8d60a787b29cb7d2bcf29230865f4a91f17323c6ac5b6b9027a6418e48d9ffc3"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.18"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: "6ec0e13a4c5c646471b9f6a25ceb3ae76d339889d4c0f79b729bf0714215a63e"
      url: "https://pub.dev"
    source: hosted
    version: "5.6.2"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: e69553c0fc6a76216e9d06a8c3767e291ad9be42171f879aab7ab708569d4393
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: "69b9ce0e760945ff52337921a8b5871592b74c92f85e7632293310701eea68cc"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.0+2"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  grouped_list:
    dependency: "direct main"
    description:
      name: grouped_list
      sha256: fef106470186081c32636aa055492eee7fc7fe8bf0921a48d31ded24821af19f
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: a72242c9a0ffb65d03de1b7113bc4e189686fc07c7147b8b41811d0dd0e0d9bf
      url: "https://pub.dev"
    source: hosted
    version: "4.0.17"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: b9603755b35253ccfad4be0762bb74d5e8bf9ff75edebf0ac3beec24fac1c5b5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: d2bab152deb2547ea6f53d82ebca9b7e77386bb706e5789e815d37e08ea475bb
      url: "https://pub.dev"
    source: hosted
    version: "0.8.7+3"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "869fe8a64771b7afbc99fc433a5f7be2fea4d1cb3d7c11a48b6b579eb9c797f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: b3e2f21feb28b24dd73a35d7ad6e83f568337c70afab5eabac876e23803f264b
      url: "https://pub.dev"
    source: hosted
    version: "0.8.8"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "02cbc21fe1706b97942b575966e5fbbeaac535e76deef70d3a242e4afb857831"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: cee2aa86c56780c13af2c77b5f2f72973464db204569e1ba2dd744459a065af4
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "7c7b96bb9413a9c28229e717e6fd1e3edd1cc5569c1778fcca060ecf729b65ee"
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: c3066601ea42113922232c7b7b3330a2d86f029f685bba99d82c30e799914952
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  infinite_listview:
    dependency: transitive
    description:
      name: infinite_listview
      sha256: f6062c1720eb59be553dfa6b89813d3e8dd2f054538445aaa5edaddfa5195ce6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  js_wrapping:
    dependency: transitive
    description:
      name: js_wrapping
      sha256: e385980f7c76a8c1c9a560dfb623b890975841542471eade630b2871d243851c
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: "43793352f90efa5d8b251893a63d767b2f7c833120e3cc02adad55eefec04dc7"
      url: "https://pub.dev"
    source: hosted
    version: "6.6.2"
  kiwi:
    dependency: "direct main"
    description:
      name: kiwi
      sha256: dbb7346ce54ccaaed75e6c8321c64813dfcf9d78647ebbc53b447c92d070b5cc
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  kiwi_generator:
    dependency: "direct dev"
    description:
      name: kiwi_generator
      sha256: "47eea1a40eab7f1a6b648ca66cdab3bdd54ac47a7abccaec7a464eb6a74d337e"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  linked_scroll_controller:
    dependency: "direct main"
    description:
      name: linked_scroll_controller
      sha256: e6020062bcf4ffc907ee7fd090fa971e65d8dfaac3c62baf601a3ced0b37986a
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  lints:
    dependency: "direct dev"
    description:
      name: lints
      sha256: "5e4a9cd06d447758280a8ac2405101e0e2094d2a1dbdd3756aec3fe7775ba593"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  logging:
    dependency: "direct main"
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  marquee:
    dependency: "direct main"
    description:
      name: marquee
      sha256: "4b5243d2804373bdc25fc93d42c3b402d6ec1f4ee8d0bb72276edd04ae7addb8"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  numberpicker:
    dependency: transitive
    description:
      name: numberpicker
      sha256: "4c129154944b0f6b133e693f8749c3f8bfb67c4d07ef9dcab48b595c22d1f156"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "107f3ed1330006a3bea63615e81cf637433f5135a52466c7caa0e7152bca9143"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  open_file_safe:
    dependency: "direct main"
    description:
      name: open_file_safe
      sha256: "99a43b36307bf6946bdc3b54514bc5081af53f46feaec782c840a116b681647d"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.3"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "7a6114becdf042be2b0777d77ace954d4a205644171a1cbd8712976b9bbb837c"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  package_info_plus_linux:
    dependency: transitive
    description:
      name: package_info_plus_linux
      sha256: "04b575f44233d30edbb80a94e57cad9107aada334fc02aabb42b6becd13c43fc"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  package_info_plus_macos:
    dependency: transitive
    description:
      name: package_info_plus_macos
      sha256: a2ad8b4acf4cd479d4a0afa5a74ea3f5b1c7563b77e52cc32b3ee6956d5482a6
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: f7a0c8f1e7e981bc65f8b64137a53fd3c195b18d429fba960babc59a5a1c7ae8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  package_info_plus_web:
    dependency: transitive
    description:
      name: package_info_plus_web
      sha256: f0829327eb534789e0a16ccac8936a80beed4e2401c4d3a74f3f39094a822d3b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  package_info_plus_windows:
    dependency: transitive
    description:
      name: package_info_plus_windows
      sha256: a6040b8695c82f8dd3c3d4dfab7ef96fbe9c67aac21b9bcf5db272589ef84441
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.dev"
    source: hosted
    version: "1.8.2"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "3087813781ab814e4157b172f1a11c46be20179fcc9bea043e0fba36bc0acaa2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "2cec049d282c7f13c594b4a73976b0b4f2d7a1838a6dd5aaf7bd9719196bee86"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.27"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "1995d88ec2948dac43edf8fe58eb434d35d22a2940ecee1a9fefcd62beee6eb3"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: ffbb8cc9ed2c9ec0e4b7a541e56fd79b138e8f47d2fb86815f15358a349b3b57
      url: "https://pub.dev"
    source: hosted
    version: "2.1.11"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "57585299a729335f1298b43245842678cb9f43a6310351b18fb577d6e33165ec"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: a34ecd7fb548f8e57321fd8e50d865d266941b54e6c3b7758cf8f37c24116905
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  pdf:
    dependency: "direct main"
    description:
      name: pdf
      sha256: "586d3debf5432e5377044754032cfa53ab45e9abf371d4865e9ad5019570e246"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  pin_input_text_field:
    dependency: transitive
    description:
      name: pin_input_text_field
      sha256: "8d6fc670aa673a4df5976086f0e8039972a5b2bcb783c8db8dd3b9b4b072ca90"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  pinput:
    dependency: "direct main"
    description:
      name: pinput
      sha256: "27eb69042f75755bdb6544f6e79a50a6ed09d6e97e2d75c8421744df1e392949"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "6a2128648c854906c53fa8e33986fc0247a1116122f9534dd20e3ab9e16a32bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "0a445f19bbaa196f5a4f93461aa066b94e6e025622eb1e9bc77872a5e25233a5"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: f582d5741930f3ad1bf0211d358eddc0508cc346e5b4b248bd1e569c995ebb7a
      url: "https://pub.dev"
    source: hosted
    version: "4.5.3"
  share_plus_linux:
    dependency: transitive
    description:
      name: share_plus_linux
      sha256: dc32bf9f1151b9864bb86a997c61a487967a08f2e0b4feaa9a10538712224da4
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  share_plus_macos:
    dependency: transitive
    description:
      name: share_plus_macos
      sha256: "44daa946f2845045ecd7abb3569b61cd9a55ae9cc4cbec9895b2067b270697ae"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "0c6e61471bd71b04a138b8b588fa388e66d8b005e6f2deda63371c5c505a0981"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  share_plus_web:
    dependency: transitive
    description:
      name: share_plus_web
      sha256: eaef05fa8548b372253e772837dd1fbe4ce3aca30ea330765c945d7d4f7c9935
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  share_plus_windows:
    dependency: transitive
    description:
      name: share_plus_windows
      sha256: "3a21515ae7d46988d42130cd53294849e280a5de6ace24bae6912a1bffd757d4"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "0344316c947ffeb3a529eac929e1978fcd37c26be4e8468628bac399365a3ca1"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: fe8401ec5b6dcd739a0fe9588802069e608c3fdbfd3c3c93e546cf2f90438076
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "0dc5c49ad8a05ed358b991b60c7b0ba1a14e16dae58af9b420d6b9e82dc024ab"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "71d6806d1449b0a9d4e85e0c7a917771e672a3d5dc61149cc9fac871115018e1"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "23b052f17a25b90ff2b61aad4cc962154da76fb62848a9ce088efe30d7c50ab1"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "7347b194fb0bbeb4058e6a4e87ee70350b6b2b90f8ac5f8bd5b3a01548f6d33a"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: f95e6a43162bce43c9c3405f3eb6f39e5b5d11f65fab19196cf8225e2777624d
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sign_in_with_apple:
    dependency: "direct main"
    description:
      name: sign_in_with_apple
      sha256: ac3b113767dfdd765078c507dad9d4d9fe96b669cc7bd88fc36fc15376fb3400
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  sign_in_with_apple_platform_interface:
    dependency: transitive
    description:
      name: sign_in_with_apple_platform_interface
      sha256: a5883edee09ed6be19de19e7d9f618a617fe41a6fa03f76d082dfb787e9ea18d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  sign_in_with_apple_web:
    dependency: transitive
    description:
      name: sign_in_with_apple_web
      sha256: "44b66528f576e77847c14999d5e881e17e7223b7b0625a185417829e5306f47a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sms_autofill:
    dependency: "direct main"
    description:
      name: sms_autofill
      sha256: "2eb159f53ed7d76002d6c15cad99e14dbd5f997f3bb7f9bcad9bd44ba5fcd6c3"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  socket_io_client:
    dependency: "direct main"
    description:
      name: socket_io_client
      sha256: "5d2a0a12c2a4a5f48d14e5b6aef7db78d3b425a9b084071059fa54bd12d2576c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  socket_io_common:
    dependency: transitive
    description:
      name: socket_io_common
      sha256: "2ab92f8ff3ebbd4b353bf4a98bee45cc157e3255464b2f90f66e09c4472047eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "373f96cf5a8744bc9816c1ff41cf5391bbdbe3d7a96fe98c622b6738a8a7bd33"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "6adebc0006c37dd63fe05bca0a929b99f06402fc95aa35bf36d67f5c06de01fd"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.4"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: b4d6710e1200e96845747e37338ea8a819a12b51689a3bcf31eff0003b37a0b9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.8+4"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "8f7603f3f8f126740bc55c4ca2d1027aab4b74a1267a3e31ce51fe40e3b65b8f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  stripe_android:
    dependency: transitive
    description:
      name: stripe_android
      sha256: e5557f2a81cb5070d48edf33168ca3891a22c63f0be98d90edeba54c4328dd21
      url: "https://pub.dev"
    source: hosted
    version: "9.2.1"
  stripe_ios:
    dependency: transitive
    description:
      name: stripe_ios
      sha256: e397609a5083b79706814342b40a2a58f1b97ecab2b9d6cae8d8e9f59646fc8c
      url: "https://pub.dev"
    source: hosted
    version: "9.2.1"
  stripe_platform_interface:
    dependency: transitive
    description:
      name: stripe_platform_interface
      sha256: "321de409f41088e842140a8e8b334b1111cc6072dfb2fa9e6452155187e8ff2d"
      url: "https://pub.dev"
    source: hosted
    version: "9.2.2"
  swipebuttonflutter:
    dependency: "direct main"
    description:
      name: swipebuttonflutter
      sha256: "7e05c0161c53ea801176ed04eb373bb5adc3cc07f4d35c8043d81783078c2034"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  syncfusion_flutter_calendar:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_calendar
      sha256: "0060cb4220d8ed585e52d251274db5e3ac198b7848164e30748de0e56fafd697"
      url: "https://pub.dev"
    source: hosted
    version: "22.1.37"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: e2b49a16805a54714ba0712d62e9f3c6311081ec2bec835529b778cc5349430a
      url: "https://pub.dev"
    source: hosted
    version: "22.1.37"
  syncfusion_flutter_datepicker:
    dependency: transitive
    description:
      name: syncfusion_flutter_datepicker
      sha256: e398ec1e7a0afa74f9a7c1fc62e245757f19cf564e51ea308d4f587a87ea4372
      url: "https://pub.dev"
    source: hosted
    version: "22.1.37"
  syncfusion_flutter_sliders:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_sliders
      sha256: bca13f542ce4a5914dc9697db038ed2beb959f78efcebcc8391b1c3619180146
      url: "https://pub.dev"
    source: hosted
    version: "22.1.37"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  system_settings:
    dependency: "direct main"
    description:
      name: system_settings
      sha256: "666693f8dace789bcf1200a88f6132b6906026643a5ee93ff140d3a547e5faf1"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.dev"
    source: hosted
    version: "0.4.16"
  time_range_picker:
    dependency: "direct main"
    description:
      name: time_range_picker
      sha256: "63c3e0beba3c261f7d95e9a2ecef3a3e0bc88d4874edbbef4471c1a60c228526"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: eb1e00ab44303d50dd487aab67ebc575456c146c6af44422f9c13889984c00f3
      url: "https://pub.dev"
    source: hosted
    version: "6.1.11"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "15f5acbf0dce90146a0f5a2c4a002b1814a6303c4c5c075aa2623b2d16156f03"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.36"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "9af7ea73259886b92199f9e42c116072f05ff9bea2dcb339ab935dfc957392c2"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "207f4ddda99b95b4d4868320a352d374b0b7e05eefad95a4a26f57da413443f5"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "91ee3e75ea9dadf38036200c5d3743518f4a5eb77a8d13fda1ee5764373f185e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: bfdfa402f1f3298637d71ca8ecfe840b4696698213d5346e9d12d4ab647ee2ea
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "6bb1e5d7fe53daf02a8fee85352432a40b1f868a81880e99ec7440113d5cfcab"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "254708f17f7c20a9c8c471f67d86d76d4a3f9c1591aad1e15292008aceb82771"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: ea8d3fc7b2e0f35de38a7465063ecfcf03d8217f7962aa2a6717132cb5d43a79
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: a5eaa5d19e123ad4f61c3718ca1ed921c4e6254238d9145f82aa214955d9aced
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "15edc42f7eaa478ce854eaf1fbb9062a899c0e4e56e775dd73b7f4709c97c4ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "392c1d83b70fe2495de3ea2c84531268d5b8de2de3f01086a53334d8b6030a88"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.4"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "8b3b2450e98876c70bfcead876d9390573b34b9418c19e28168b74f6cb252dbd"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.4"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "812165e4e34ca677bdfbfa58c01e33b27fd03ab5fa75b70832d4b7d4ca1fa8cf"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.5"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: a5364369c758892aa487cbf59ea41d9edd10f9d9baf06a94e80f1bd1b4c7bbc0
      url: "https://pub.dev"
    source: hosted
    version: "2.9.5"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: c0e3a4f7be7dae51d8f152230b86627e3397c1ba8c3fa58e63d44a9f3edc9cef
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: bd512f03919aac5f1313eb8249f223bacf4927031bf60b02601f81f687689e86
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "979ee37d622dec6365e2efa4d906c37470995871fe9ae080d967e192d88286b5"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=2.19.0 <3.0.0"
  flutter: ">=3.7.0"
