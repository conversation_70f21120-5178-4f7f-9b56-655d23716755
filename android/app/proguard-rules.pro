# Add project-specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Optimize and shrink code
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose

# Remove debug logs
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int d(...);
    public static int i(...);
}

# Remove System.out and System.err calls
-assumenosideeffects class java.lang.System {
    public static java.io.PrintStream out;
    public static java.io.PrintStream err;
}

# Keep JS interfaces used with WebView
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
    public *;
}

# Preserve line number information for debugging stack traces
-keepattributes SourceFile,LineNumberTable

# Hide the original source file name
-renamesourcefileattribute SourceFile

# Keep classes and methods used by the Android framework
-keep class android.** { *; }
-keep interface android.** { *; }

# Keep classes and methods used by the Kotlin runtime
-keep class kotlin.** { *; }
-keep interface kotlin.** { *; }

# Keep classes and methods used by Firebase
-keep class com.google.firebase.** { *; }

# Keep classes and methods used by Stripe
-keep class com.stripe.** { *; }

# Preserve model classes used by Firebase Firestore
-keep class com.yourpackage.model.** { *; }

# Preserve data classes used by Gson serialization
-keepclassmembers class com.yourpackage.data.model.** {
    *;
}

# Specify additional custom rules for libraries and dependencies below this line
